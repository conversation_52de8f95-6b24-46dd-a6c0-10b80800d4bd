import 'package:flutter/widgets.dart';
import 'package:flutter_kexue/data/login/repo/account_repo.dart';
import 'package:flutter_kexue/data/login/repo/model/account_biz_model.dart';

/// 账户管理使用示例
/// 展示如何使用 AccountRepo 和 AccountBizModel
class AccountUsageExample {
  final AccountRepo _accountRepo = AccountRepo();

  /// 示例：用户登录流程
  Future<void> exampleLogin() async {
    debugPrint('=== 用户登录示例 ===');
    
    // 模拟用户登录
    final loginResult = await _accountRepo.login(
      phone: '***********',
      verificationCode: '1234',
      username: 'testuser',
    );
    
    if (loginResult.isSuccess) {
      debugPrint('登录成功: ${loginResult.data}');
      
      // 获取当前用户信息
      final currentUser = await _accountRepo.getCurrentUser();
      debugPrint('当前用户: $currentUser');
      
      // 检查登录状态
      final isLoggedIn = await _accountRepo.isUserLoggedIn();
      debugPrint('登录状态: $isLoggedIn');
      
      // 获取访问令牌
      final accessToken = await _accountRepo.getAccessToken();
      debugPrint('访问令牌: $accessToken');
    } else {
      debugPrint('登录失败: ${loginResult.message}');
    }
  }

  /// 示例：检查用户会话
  Future<void> exampleCheckSession() async {
    debugPrint('\n=== 检查用户会话示例 ===');
    
    // 验证当前会话是否有效
    final isValidSession = await _accountRepo.validateSession();
    debugPrint('会话有效性: $isValidSession');
    
    if (isValidSession) {
      // 获取用户摘要信息
      final userSummary = await _accountRepo.getUserSummary();
      debugPrint('用户摘要: $userSummary');
      
      // 检查是否需要刷新令牌
      final shouldRefresh = await _accountRepo.shouldRefreshToken();
      debugPrint('需要刷新令牌: $shouldRefresh');
    }
  }

  /// 示例：更新用户资料
  Future<void> exampleUpdateProfile() async {
    debugPrint('\n=== 更新用户资料示例 ===');
    
    // 检查是否已登录
    final isLoggedIn = await _accountRepo.isUserLoggedIn();
    if (!isLoggedIn) {
      debugPrint('用户未登录，无法更新资料');
      return;
    }
    
    // 更新用户资料
    final updateSuccess = await _accountRepo.updateUserProfile(
      nickname: '新昵称',
      email: '<EMAIL>',
      gender: 1, // 男性
      birthday: '1990-01-01',
    );
    
    if (updateSuccess) {
      debugPrint('用户资料更新成功');
      
      // 获取更新后的用户信息
      final updatedUser = await _accountRepo.getCurrentUser();
      debugPrint('更新后的用户信息: $updatedUser');
    } else {
      debugPrint('用户资料更新失败');
    }
  }

  /// 示例：刷新访问令牌
  Future<void> exampleRefreshToken() async {
    debugPrint('\n=== 刷新访问令牌示例 ===');
    
    // 获取当前刷新令牌
    final refreshToken = await _accountRepo.getRefreshToken();
    if (refreshToken == null) {
      debugPrint('没有可用的刷新令牌');
      return;
    }
    
    // 模拟刷新令牌（实际应该调用API）
    final newAccessToken = 'new_access_token_${DateTime.now().millisecondsSinceEpoch}';
    final newRefreshToken = 'new_refresh_token_${DateTime.now().millisecondsSinceEpoch}';
    final newExpireTime = DateTime.now().add(const Duration(hours: 24)).toIso8601String();
    
    final refreshSuccess = await _accountRepo.refreshAccessToken(
      newAccessToken,
      newRefreshToken: newRefreshToken,
      expireTime: newExpireTime,
    );
    
    if (refreshSuccess) {
      debugPrint('令牌刷新成功');
      
      // 获取新的访问令牌
      final accessToken = await _accountRepo.getAccessToken();
      debugPrint('新的访问令牌: $accessToken');
    } else {
      debugPrint('令牌刷新失败');
    }
  }

  /// 示例：用户退出登录
  Future<void> exampleLogout() async {
    debugPrint('\n=== 用户退出登录示例 ===');
    
    // 退出登录
    final logoutSuccess = await _accountRepo.logout();
    
    if (logoutSuccess) {
      debugPrint('退出登录成功');
      
      // 验证登录状态
      final isLoggedIn = await _accountRepo.isUserLoggedIn();
      debugPrint('登录状态: $isLoggedIn');
      
      // 尝试获取用户信息
      final userInfo = await _accountRepo.getCurrentUser();
      debugPrint('用户信息: $userInfo');
    } else {
      debugPrint('退出登录失败');
    }
  }

  /// 示例：AccountBizModel 的使用
  void exampleAccountBizModel() {
    debugPrint('\n=== AccountBizModel 使用示例 ===');
    
    // 创建用户模型
    final user = AccountBizModel(
      userId: 'user123',
      username: 'testuser',
      phone: '***********',
      nickname: '测试用户',
      accessToken: 'access_token_123',
      tokenExpireTime: DateTime.now().add(const Duration(hours: 1)).toIso8601String(),
    );
    
    debugPrint('用户信息: $user');
    debugPrint('是否已登录: ${user.isLoggedIn}');
    debugPrint('令牌是否过期: ${user.isTokenExpired}');
    
    // 转换为 JSON
    final userJson = user.toJson();
    debugPrint('用户JSON: $userJson');
    
    // 从 JSON 创建用户
    final userFromJson = AccountBizModel.fromJson(userJson);
    debugPrint('从JSON创建的用户: $userFromJson');
    
    // 复制并修改用户信息
    final updatedUser = user.copyWith(
      nickname: '更新的昵称',
      email: '<EMAIL>',
    );
    debugPrint('更新后的用户: $updatedUser');
  }

  /// 运行所有示例
  Future<void> runAllExamples() async {
    debugPrint('开始运行账户管理示例...\n');
    
    // 1. AccountBizModel 使用示例
    exampleAccountBizModel();
    
    // 2. 用户登录示例
    await exampleLogin();
    
    // 3. 检查用户会话示例
    await exampleCheckSession();
    
    // 4. 更新用户资料示例
    await exampleUpdateProfile();
    
    // 5. 刷新访问令牌示例
    await exampleRefreshToken();
    
    // 6. 用户退出登录示例
    await exampleLogout();
    
    debugPrint('\n所有示例运行完成！');
  }
}

/// 在应用中使用示例
/// 可以在 main.dart 或其他地方调用
void main() async {
  final example = AccountUsageExample();
  await example.runAllExamples();
}
