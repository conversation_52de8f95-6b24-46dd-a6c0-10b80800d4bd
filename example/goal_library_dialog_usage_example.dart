import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/dialog/plan_lib_dialog/targets_dialog.dart';

/// 目标库对话框使用示例
class GoalLibraryDialogUsageExample extends StatefulWidget {
  const GoalLibraryDialogUsageExample({super.key});

  @override
  State<GoalLibraryDialogUsageExample> createState() => _GoalLibraryDialogUsageExampleState();
}

class _GoalLibraryDialogUsageExampleState extends State<GoalLibraryDialogUsageExample> {
  GoalLibraryItem? selectedGoal;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('目标库对话框示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (selectedGoal != null) ...[
              Text(
                '选择的目标: ${selectedGoal!.title}',
                style: const TextStyle(fontSize: 16),
              ),
              Text(
                '类型: ${selectedGoal!.type == GoalType.clock ? "打卡" : "持戒"}',
                style: const TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 20),
            ],
            
            ElevatedButton(
              onPressed: () => _showGoalLibraryDialog(GoalType.clock),
              child: const Text('打开目标库 (打卡类型)'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () => _showGoalLibraryDialog(GoalType.persist),
              child: const Text('打开目标库 (持戒类型)'),
            ),
            
            const SizedBox(height: 40),
            
            const Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '功能说明：',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• 底部弹出的模态对话框'),
                  Text('• 支持打卡和持戒两个Tab切换'),
                  Text('• 可滑动切换Tab内容'),
                  Text('• 点击目标项进行选择'),
                  Text('• 选中状态有边框高亮'),
                  Text('• 左侧彩色指示器区分类型'),
                  Text('• 右侧显示天数和次数信息'),
                  Text('• 底部确认按钮，选中后可用'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示目标库对话框
  void _showGoalLibraryDialog(GoalType initialType) {
    GoalLibraryDialog.show(
      context: context,
      initialType: initialType,
      onConfirm: (item) {
        setState(() {
          selectedGoal = item;
        });
        print('选择的目标: ${item.title}, 类型: ${item.type}');
      },
    );
  }
}

/// 在应用中使用示例
void main() {
  runApp(MaterialApp(
    home: const GoalLibraryDialogUsageExample(),
    theme: ThemeData(
      primarySwatch: Colors.green,
    ),
  ));
}

/// 使用说明：
/// 
/// 1. 基本调用：
///    ```dart
///    GoalLibraryDialog.show(
///      context: context,
///      initialType: GoalType.checkIn,  // 初始显示的Tab
///      onConfirm: (item) {             // 选择回调
///        print('选择的目标: ${item.title}');
///      },
///    );
///    ```
/// 
/// 2. 对话框特性：
///    - 底部弹出，带半透明遮罩
///    - 圆角白色背景
///    - 标题栏带关闭按钮
///    - Tab栏支持滑动切换
///    - 列表支持滚动
/// 
/// 3. Tab功能：
///    - 打卡Tab：显示打卡类型的目标
///    - 持戒Tab：显示持戒类型的目标
///    - 支持手势滑动切换
///    - Tab指示器为主题色
/// 
/// 4. 目标项功能：
///    - 点击选择目标项
///    - 选中状态有绿色边框
///    - 左侧指示器：绿色(打卡)、红色(持戒)
///    - 右侧信息：显示天数、次数等
/// 
/// 5. 数据结构：
///    ```dart
///    GoalLibraryItem(
///      id: '1',
///      title: '戒撸',
///      subtitle: '不再自慰也不看色情内容',
///      type: GoalType.checkIn,
///      days: 100,        // 可选：天数
///      count: 12,        // 可选：每日次数
///    )
///    ```
/// 
/// 6. 在计划页面中的使用：
///    ```dart
///    GestureDetector(
///      onTap: () {
///        GoalLibraryDialog.show(
///          context: context,
///          initialType: viewModel.us.goalType,
///          onConfirm: (item) {
///            viewModel.us.goalNameController.text = item.title;
///            // 设置其他相关属性
///          },
///        );
///      },
///      child: Container(
///        child: Text('目标库'),
///      ),
///    )
///    ```
