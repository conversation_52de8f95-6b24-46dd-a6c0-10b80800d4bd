import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/dialog/daily_count_dialog.dart';

/// 每日次数设置对话框使用示例
class DailyCountDialogUsageExample extends StatefulWidget {
  const DailyCountDialogUsageExample({super.key});

  @override
  State<DailyCountDialogUsageExample> createState() => _DailyCountDialogUsageExampleState();
}

class _DailyCountDialogUsageExampleState extends State<DailyCountDialogUsageExample> {
  int dailyCount = 1;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('每日次数设置对话框示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '当前每日次数: $dailyCount',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _showDailyCountDialog,
              child: const Text('设置每日次数'),
            ),
            const SizedBox(height: 40),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '功能说明：',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• 居中显示的模态对话框'),
                  Text('• 标题栏带关闭按钮'),
                  Text('• 中间是计数器组件（-/数字/+）'),
                  Text('• 底部显示提示文本'),
                  Text('• 绿色确定按钮'),
                  Text('• 最小值为1，无最大值限制'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示每日次数设置对话框
  void _showDailyCountDialog() {
    DailyCountDialog.show(
      context: context,
      initialCount: dailyCount,
      onConfirm: (count) {
        setState(() {
          dailyCount = count;
        });
        print('设置每日次数: $count');
      },
    );
  }
}

/// 在应用中使用示例
void main() {
  runApp(MaterialApp(
    home: const DailyCountDialogUsageExample(),
    theme: ThemeData(
      primarySwatch: Colors.green,
    ),
  ));
}

/// 使用说明：
/// 
/// 1. 基本调用：
///    ```dart
///    DailyCountDialog.show(
///      context: context,
///      initialCount: 1,           // 初始次数
///      onConfirm: (count) {       // 确认回调
///        print('选择的次数: $count');
///      },
///    );
///    ```
/// 
/// 2. 对话框特性：
///    - 居中显示，带半透明遮罩
///    - 圆角白色背景
///    - 标题栏带关闭按钮
///    - 计数器支持 +/- 操作
///    - 最小值限制为1
///    - 动态更新提示文本
/// 
/// 3. 交互功能：
///    - 点击 - 按钮：减少次数（最小为1）
///    - 点击 + 按钮：增加次数（无上限）
///    - 点击确定：回调选择的次数并关闭对话框
///    - 点击关闭：直接关闭对话框，不触发回调
/// 
/// 4. 样式设计：
///    - 减少按钮：灰色圆形背景
///    - 增加按钮：绿色圆形背景
///    - 数字显示：灰色圆角矩形背景
///    - 确定按钮：绿色圆角矩形，全宽
/// 
/// 5. 在计划页面中的使用：
///    ```dart
///    // 在目标类型配置中使用
///    GestureDetector(
///      onTap: () {
///        DailyCountDialog.show(
///          context: context,
///          initialCount: viewModel.us.dailyCount,
///          onConfirm: (count) {
///            viewModel.us.dailyCount = count;
///          },
///        );
///      },
///      child: Text('${viewModel.us.dailyCount}次'),
///    )
///    ```
