import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';

/// 动态高度计算演示
class DynamicHeightDemo extends StatefulWidget {
  const DynamicHeightDemo({super.key});

  @override
  State<DynamicHeightDemo> createState() => _DynamicHeightDemoState();
}

class _DynamicHeightDemoState extends State<DynamicHeightDemo> {
  DateTime _currentDate = DateTime.now();
  EdgeInsets _padding = const EdgeInsets.symmetric(horizontal: 14);
  double _containerWidth = 350;

  // 模拟有数据的日期
  final Set<DateTime> _datesWithData = {
    DateTime(2024, 3, 8),
    DateTime(2024, 3, 15),
    DateTime(2024, 3, 22),
    DateTime(2024, 3, 29),
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('动态高度计算演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 控制面板
            _buildControlPanel(),
            
            const SizedBox(height: 20),
            
            // 计算信息显示
            _buildCalculationInfo(),
            
            const SizedBox(height: 20),
            
            // 日历容器
            _buildCalendarContainer(),
            
            const SizedBox(height: 20),
            
            // 说明文字
            _buildDescription(),
          ],
        ),
      ),
    );
  }

  Widget _buildControlPanel() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '控制面板',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // 容器宽度控制
            Text('容器宽度: ${_containerWidth.toInt()}px'),
            Slider(
              value: _containerWidth,
              min: 250,
              max: 500,
              divisions: 25,
              onChanged: (value) {
                setState(() {
                  _containerWidth = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Padding控制
            Text('水平Padding: ${_padding.horizontal.toInt()}px'),
            Slider(
              value: _padding.horizontal / 2,
              min: 0,
              max: 50,
              divisions: 25,
              onChanged: (value) {
                setState(() {
                  _padding = EdgeInsets.symmetric(horizontal: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationInfo() {
    final availableWidth = _containerWidth - _padding.left - _padding.right;
    final rowHeight = availableWidth / 7;
    final monthRows = _calculateMonthRows(_currentDate);
    final totalHeight = monthRows * rowHeight;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '计算信息',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('容器宽度: ${_containerWidth.toInt()}px'),
            Text('Padding: ${_padding.left.toInt()}px × 2 = ${_padding.horizontal.toInt()}px'),
            Text('可用宽度: ${availableWidth.toInt()}px'),
            Text('每行高度: ${rowHeight.toStringAsFixed(1)}px (可用宽度 ÷ 7)'),
            Text('当前月行数: $monthRows 行'),
            Text('总高度: ${totalHeight.toStringAsFixed(1)}px'),
            const SizedBox(height: 8),
            Text(
              '${_currentDate.year}年${_currentDate.month}月',
              style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.blue),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarContainer() {
    return Card(
      child: Container(
        width: _containerWidth,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              '日历组件',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TDCalendar(
              selectedDate: _currentDate,
              padding: _padding,
              onMonthChanged: (monthFirstDay) {
                setState(() {
                  _currentDate = monthFirstDay;
                });
              },
              onDateTap: (date) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('点击了: ${date.year}年${date.month}月${date.day}日'),
                    duration: const Duration(seconds: 1),
                  ),
                );
              },
              datesWithData: _datesWithData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '功能说明',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text('• 日历高度根据当前月份的实际行数动态计算'),
            const Text('• 每行高度 = (容器宽度 - 左右padding) ÷ 7'),
            const Text('• 支持外部传入padding参数'),
            const Text('• 高度变化时有平滑的动画过渡'),
            const Text('• 字体大小根据可用空间自适应调整'),
            const SizedBox(height: 12),
            const Text(
              '试试调整上面的滑块，观察日历高度的变化！',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// 计算月份需要的行数
  int _calculateMonthRows(DateTime monthDate) {
    final firstDay = DateTime(monthDate.year, monthDate.month, 1);
    final lastDay = DateTime(monthDate.year, monthDate.month + 1, 0);
    final firstWeekday = firstDay.weekday % 7;
    final totalDays = firstWeekday + lastDay.day;
    return (totalDays / 7).ceil();
  }
}
