import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_kexue/widget/upload/td_upload_h.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// TDUploadH showAddButton 属性示例
class TDUploadHShowAddButtonExample extends StatefulWidget {
  const TDUploadHShowAddButtonExample({super.key});

  @override
  State<TDUploadHShowAddButtonExample> createState() => _TDUploadHShowAddButtonExampleState();
}

class _TDUploadHShowAddButtonExampleState extends State<TDUploadHShowAddButtonExample> {
  final RxList<TDUploadFile> uploadedFiles1 = <TDUploadFile>[].obs;
  final RxList<TDUploadFile> uploadedFiles2 = <TDUploadFile>[].obs;
  final RxList<TDUploadFile> uploadedFiles3 = <TDUploadFile>[].obs;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // 为第一个列表添加一些示例图片
    uploadedFiles1.addAll([
      TDUploadFile(
        key: 1,
        remotePath: 'https://picsum.photos/200/200?random=1',
        status: TDUploadFileStatus.success,
      ),
      TDUploadFile(
        key: 2,
        remotePath: 'https://picsum.photos/200/200?random=2',
        status: TDUploadFileStatus.success,
      ),
    ]);

    // 为第二个列表添加更多图片
    uploadedFiles2.addAll([
      TDUploadFile(
        key: 3,
        remotePath: 'https://picsum.photos/200/200?random=3',
        status: TDUploadFileStatus.success,
      ),
      TDUploadFile(
        key: 4,
        remotePath: 'https://picsum.photos/200/200?random=4',
        status: TDUploadFileStatus.success,
      ),
      TDUploadFile(
        key: 5,
        remotePath: 'https://picsum.photos/200/200?random=5',
        status: TDUploadFileStatus.success,
      ),
    ]);

    // 第三个列表为空
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TDUploadH showAddButton 示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明文字
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'showAddButton 属性说明：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '• showAddButton: true - 显示添加按钮（默认）\n'
                    '• showAddButton: false - 隐藏添加按钮\n'
                    '• 适用于只展示图片，不允许添加的场景',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 示例1：显示添加按钮（默认行为）
            _buildExampleSection(
              title: '示例1：显示添加按钮（showAddButton: true）',
              description: '用户可以添加新图片，适用于编辑场景',
              child: Obx(() {
                return TDUploadH(
                  files: uploadedFiles1,
                  max: 9,
                  height: 77,
                  spacing: 10,
                  showAddButton: true, // 显示添加按钮
                  onClick: (key) => print('点击图片1: $key'),
                  onChange: (fileList, type) => _onValueChanged(uploadedFiles1, fileList, type),
                  onMaxLimitReached: () => _showMessage('最多只能上传9张图片'),
                );
              }),
            ),

            SizedBox(height: 24.h),

            // 示例2：隐藏添加按钮
            _buildExampleSection(
              title: '示例2：隐藏添加按钮（showAddButton: false）',
              description: '只展示图片，不允许添加，适用于查看场景',
              child: Obx(() {
                return TDUploadH(
                  files: uploadedFiles2,
                  max: 9,
                  height: 77,
                  spacing: 10,
                  showAddButton: false, // 隐藏添加按钮
                  onClick: (key) => print('点击图片2: $key'),
                  onChange: (fileList, type) => _onValueChanged(uploadedFiles2, fileList, type),
                );
              }),
            ),

            SizedBox(height: 24.h),

            // 示例3：空列表隐藏添加按钮
            _buildExampleSection(
              title: '示例3：空列表隐藏添加按钮',
              description: '当没有图片且不允许添加时的显示效果',
              child: Obx(() {
                return TDUploadH(
                  files: uploadedFiles3,
                  max: 9,
                  height: 77,
                  spacing: 10,
                  showAddButton: false, // 隐藏添加按钮
                  onClick: (key) => print('点击图片3: $key'),
                  onChange: (fileList, type) => _onValueChanged(uploadedFiles3, fileList, type),
                );
              }),
            ),

            SizedBox(height: 24.h),

            // 控制按钮
            _buildControlSection(),
          ],
        ),
      ),
    );
  }

  /// 构建示例区域
  Widget _buildExampleSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            description,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }

  /// 构建控制区域
  Widget _buildControlSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '控制操作：',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 16.h),
          Wrap(
            spacing: 12.w,
            runSpacing: 12.h,
            children: [
              ElevatedButton(
                onPressed: () => _addRandomImage(uploadedFiles1),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                child: const Text('给示例1添加图片', style: TextStyle(color: Colors.white)),
              ),
              ElevatedButton(
                onPressed: () => _addRandomImage(uploadedFiles2),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: const Text('给示例2添加图片', style: TextStyle(color: Colors.white)),
              ),
              ElevatedButton(
                onPressed: () => _addRandomImage(uploadedFiles3),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                child: const Text('给示例3添加图片', style: TextStyle(color: Colors.white)),
              ),
              ElevatedButton(
                onPressed: _clearAllImages,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('清空所有图片', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 处理文件变化
  void _onValueChanged(RxList<TDUploadFile> targetList, List<TDUploadFile> value, TDUploadType event) {
    final List<TDUploadFile> newFiles = List.from(targetList);

    switch (event) {
      case TDUploadType.add:
        newFiles.addAll(value);
        break;
      case TDUploadType.remove:
        newFiles.removeWhere((element) => element.key == value[0].key);
        break;
      case TDUploadType.replace:
        final firstReplaceFile = value.first;
        final index = newFiles.indexWhere((file) => file.key == firstReplaceFile.key);
        if (index != -1) {
          newFiles[index] = firstReplaceFile;
        }
        break;
    }

    targetList.assignAll(newFiles);
  }

  /// 添加随机图片
  void _addRandomImage(RxList<TDUploadFile> targetList) {
    if (targetList.length >= 9) {
      _showMessage('最多只能上传9张图片');
      return;
    }

    final random = DateTime.now().millisecondsSinceEpoch;
    final newFile = TDUploadFile(
      key: random,
      remotePath: 'https://picsum.photos/200/200?random=$random',
      status: TDUploadFileStatus.success,
    );

    targetList.add(newFile);
    _showMessage('图片添加成功');
  }

  /// 清空所有图片
  void _clearAllImages() {
    uploadedFiles1.clear();
    uploadedFiles2.clear();
    uploadedFiles3.clear();
    _showMessage('所有图片已清空');
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: const TDUploadHShowAddButtonExample(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
