import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/add_record/plan_add_or_edit_page.dart';
import 'package:flutter_kexue/page/page/add_record/entity/record_add_or_edit_props.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:get/get.dart';

/// 新增记录页面使用示例
class AddRecordUsageExample extends StatelessWidget {
  const AddRecordUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('新增记录页面示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _navigateToAddRecord(context),
              child: const Text('跳转到新增记录页面'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _navigateWithPresetData(context),
              child: const Text('带预设数据跳转'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _navigateFromQuickAdd(context),
              child: const Text('快速添加模式'),
            ),
          ],
        ),
      ),
    );
  }

  /// 普通跳转到新增记录页面
  void _navigateToAddRecord(BuildContext context) {
    Get.toNamed(Routes.recordAddOrEdit)?.then((result) {
      if (result != null && result['success'] == true) {
        _showResult(context, result);
      }
    });
  }

  /// 带预设数据跳转
  void _navigateWithPresetData(BuildContext context) {
    Get.toNamed(
      Routes.recordAddOrEdit,
      arguments: const AddRecordProps(
        recordType: '日记',
        presetTitle: '今天的心情',
        isQuickAdd: false,
      ),
    )?.then((result) {
      if (result != null && result['success'] == true) {
        _showResult(context, result);
      }
    });
  }

  /// 快速添加模式
  void _navigateFromQuickAdd(BuildContext context) {
    Get.toNamed(
      Routes.recordAddOrEdit,
      arguments: const AddRecordProps(
        recordType: '多选记录',
        presetTitle: '运动打卡',
        isQuickAdd: true,
      ),
    )?.then((result) {
      if (result != null && result['success'] == true) {
        _showResult(context, result);
      }
    });
  }

  /// 显示返回结果
  void _showResult(BuildContext context, Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建成功'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('记录类型: ${result['type'] == 0 ? '日记' : '多选记录'}'),
            Text('记录名称: ${result['name']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

/// 在应用中使用示例
/// 可以在 main.dart 或其他地方调用
void main() {
  runApp(MaterialApp(
    home: const AddRecordUsageExample(),
    // 添加路由配置
    getPages: [
      GetPage(
        name: Routes.recordAddOrEdit,
        page: () => const AddRecordPage(),
      ),
    ],
  ));
}
