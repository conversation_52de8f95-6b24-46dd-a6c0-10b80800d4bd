import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/image_grid_widget.dart';

/// 图片网格组件使用示例
class ImageGridUsageExample extends StatelessWidget {
  const ImageGridUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    // 示例图片列表
    final List<String> images = [
      'https://picsum.photos/300/300?random=1',
      'https://picsum.photos/300/300?random=2',
      'https://picsum.photos/300/300?random=3',
      'https://picsum.photos/300/300?random=4',
      'https://picsum.photos/300/300?random=5',
      'https://picsum.photos/300/300?random=6',
      'https://picsum.photos/300/300?random=7',
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('图片网格组件示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '4张图片示例：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            ImageGridWidget(
              images: images.take(4).toList(),
              onImageTap: (index) {
                print('点击了第 ${index + 1} 张图片');
              },
            ),
            
            const SizedBox(height: 30),
            
            const Text(
              '超过4张图片示例（显示+数量）：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            ImageGridWidget(
              images: images,
              onImageTap: (index) {
                print('点击了第 ${index + 1} 张图片');
              },
            ),
            
            const SizedBox(height: 30),
            
            const Text(
              '单张图片示例：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            ImageGridWidget(
              images: [images.first],
              onImageTap: (index) {
                print('点击了第 ${index + 1} 张图片');
              },
            ),
            
            const SizedBox(height: 30),
            
            const Text(
              '空图片列表示例：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const ImageGridWidget(
              images: [],
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              '功能说明：',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 5),
            const Text(
              '• 一行显示4个图片\n'
              '• 图片高度固定77px\n'
              '• 图片间隔10px\n'
              '• 圆角4px\n'
              '• 超过4张时显示+剩余数量\n'
              '• 点击可预览大图\n'
              '• 支持左右滑动\n'
              '• 底部显示页码',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
