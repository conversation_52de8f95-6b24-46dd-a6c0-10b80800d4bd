import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_kexue/widget/upload/td_upload_h.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 日记编辑页面布局示例
class DiaryEditorLayoutExample extends StatefulWidget {
  const DiaryEditorLayoutExample({super.key});

  @override
  State<DiaryEditorLayoutExample> createState() => _DiaryEditorLayoutExampleState();
}

class _DiaryEditorLayoutExampleState extends State<DiaryEditorLayoutExample> {
  final RxList<TDUploadFile> uploadedFiles = <TDUploadFile>[].obs;
  final TextEditingController textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 添加一些示例图片
    uploadedFiles.addAll([
      TDUploadFile(
        key: 1,
        remotePath: 'https://picsum.photos/200/200?random=1',
        status: TDUploadFileStatus.success,
      ),
      TDUploadFile(
        key: 2,
        remotePath: 'https://picsum.photos/200/200?random=2',
        status: TDUploadFileStatus.success,
      ),
      TDUploadFile(
        key: 3,
        remotePath: 'https://picsum.photos/200/200?random=3',
        status: TDUploadFileStatus.success,
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        '日记',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
      ),
      centerTitle: true,
      actions: [
        Container(
          margin: EdgeInsets.only(right: 16.w),
          child: TextButton(
            onPressed: () {},
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                '保存',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期显示
          _buildDateSection(),

          SizedBox(height: 20.h),

          // 文本编辑区域
          _buildTextEditor(),

          SizedBox(height: 24.h),

          // 横向滑动图片上传区域
          _buildHorizontalImageUpload(),

          SizedBox(height: 16.h),

          // 九宫格图片上传区域（保留原有功能）
          _buildGridImageUpload(),

          SizedBox(height: 40.h),
        ],
      ),
    );
  }

  /// 构建日期显示部分
  Widget _buildDateSection() {
    final now = DateTime.now();
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[now.weekday - 1];

    return Row(
      children: [
        Text(
          '${now.year}年${now.month}月${now.day}日',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        SizedBox(width: 10.w),
        Text(
          weekday,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建文本编辑器
  Widget _buildTextEditor() {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(minHeight: 120.h),
      alignment: Alignment.topLeft,
      child: TextField(
        controller: textController,
        maxLines: null,
        keyboardType: TextInputType.multiline,
        decoration: InputDecoration(
          hintText: '这一刻的想法...',
          hintStyle: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(0),
          isDense: true,
        ),
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.black,
          height: 1.0,
        ),
      ),
    );
  }

  /// 构建横向滑动图片上传区域
  Widget _buildHorizontalImageUpload() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Column(
        children: [
          // 横向滑动图片列表
          Obx(() {
            return TDUploadH(
              files: uploadedFiles,
              max: 9,
              height: 77,
              spacing: 10,
              showAddButton: true, // 显示添加按钮
              onClick: (key) => print('点击图片: $key'),
              onCancel: () => print('取消'),
              onError: (error) => print('Upload error: $error'),
              onValidate: (error) => print('Validation error: $error'),
              onMaxLimitReached: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('最多只能上传9张图片')),
                );
              },
              onChange: (fileList, type) => _onValueChanged(fileList, type),
            );
          }),
          
          // 底部提示区域
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Row(
              children: [
                Icon(
                  Icons.image_outlined,
                  size: 16.sp,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 8.w),
                Text(
                  '图片',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
                Spacer(),
                Obx(() {
                  return Text(
                    '${uploadedFiles.length}/9',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[500],
                    ),
                  );
                }),
                SizedBox(width: 8.w),
                Icon(
                  Icons.visibility_outlined,
                  size: 16.sp,
                  color: Colors.grey[500],
                ),
                SizedBox(width: 4.w),
                Text(
                  '自己可见',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建九宫格图片上传区域
  Widget _buildGridImageUpload() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '九宫格图片上传',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 12.h),
          LayoutBuilder(
            builder: (context, constraints) {
              final maxWidth = constraints.maxWidth;
              final itemWidth = (maxWidth - 16) / 3;

              return Obx(() {
                return TDUpload(
                  files: uploadedFiles,
                  multiple: false,
                  max: 9,
                  onClick: (key) => print('九宫格点击: $key'),
                  onCancel: () => print('九宫格取消'),
                  onError: (error) => print('九宫格错误: $error'),
                  onValidate: (error) => print('九宫格验证: $error'),
                  width: itemWidth,
                  height: itemWidth,
                  onMaxLimitReached: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('最多只能上传9张图片')),
                    );
                  },
                  onChange: (fileList, type) => _onValueChanged(fileList, type),
                );
              });
            },
          ),
        ],
      ),
    );
  }

  /// 处理文件变化
  void _onValueChanged(List<TDUploadFile> value, TDUploadType event) {
    final List<TDUploadFile> newFiles = List.from(uploadedFiles);

    switch (event) {
      case TDUploadType.add:
        newFiles.addAll(value);
        break;
      case TDUploadType.remove:
        newFiles.removeWhere((element) => element.key == value[0].key);
        break;
      case TDUploadType.replace:
        final firstReplaceFile = value.first;
        final index = newFiles.indexWhere((file) => file.key == firstReplaceFile.key);
        if (index != -1) {
          newFiles[index] = firstReplaceFile;
        }
        break;
    }

    uploadedFiles.assignAll(newFiles);
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }
}

void main() {
  runApp(MaterialApp(
    home: const DiaryEditorLayoutExample(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
