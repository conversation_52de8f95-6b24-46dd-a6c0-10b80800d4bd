import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_kexue/widget/upload/td_upload_h.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// TDUploadH 删除按钮显示测试
class TDUploadHDeleteButtonTest extends StatefulWidget {
  const TDUploadHDeleteButtonTest({super.key});

  @override
  State<TDUploadHDeleteButtonTest> createState() => _TDUploadHDeleteButtonTestState();
}

class _TDUploadHDeleteButtonTestState extends State<TDUploadHDeleteButtonTest> {
  List<TDUploadFile> uploadedFiles = <TDUploadFile>[];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    setState(() {
      uploadedFiles.addAll([
        TDUploadFile(
          key: 1,
          remotePath: 'https://picsum.photos/200/200?random=1',
          status: TDUploadFileStatus.success,
          canDelete: true,
        ),
        TDUploadFile(
          key: 2,
          remotePath: 'https://picsum.photos/200/200?random=2',
          status: TDUploadFileStatus.success,
          canDelete: true,
        ),
        TDUploadFile(
          key: 3,
          remotePath: 'https://picsum.photos/200/200?random=3',
          status: TDUploadFileStatus.success,
          canDelete: true,
        ),
      ]);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('删除按钮显示测试'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明文字
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '删除按钮显示测试：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[800],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '• 检查删除按钮是否在图片右上角正确显示\n'
                    '• 测试不同高度下的显示效果\n'
                    '• 验证点击删除功能是否正常\n'
                    '• 确保按钮不会被容器边界裁剪',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.orange[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 测试1：默认高度 77px
            _buildTestSection(
              title: '测试1：默认高度 (77px)',
              height: 77,
            ),

            SizedBox(height: 24.h),

            // 测试2：较小高度 50px
            _buildTestSection(
              title: '测试2：较小高度 (50px)',
              height: 50,
            ),

            SizedBox(height: 24.h),

            // 测试3：较大高度 100px
            _buildTestSection(
              title: '测试3：较大高度 (100px)',
              height: 100,
            ),

            SizedBox(height: 24.h),

            // 测试4：超小高度 30px
            _buildTestSection(
              title: '测试4：超小高度 (30px)',
              height: 30,
            ),

            SizedBox(height: 24.h),

            // 控制按钮
            _buildControlSection(),
          ],
        ),
      ),
    );
  }

  /// 构建测试区域
  Widget _buildTestSection({
    required String title,
    required double height,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  '高度: ${height.toInt()}px',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.blue[800],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // TDUploadH 组件
          TDUploadH(
            files: uploadedFiles,
            max: 9,
            height: height,
            spacing: 10,
            showAddButton: true,
            enablePreview: false, // 禁用预览避免GetX错误
            canDelete: true,
            canDownload: true,
            onChange: (fileList, type) => _onValueChanged(fileList, type),
            onMaxLimitReached: () => _showMessage('最多只能上传9张图片'),
          ),
          
          SizedBox(height: 12.h),
          
          // 提示信息
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16.sp,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '检查每张图片右上角是否有红色删除按钮，点击测试删除功能',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建控制区域
  Widget _buildControlSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '控制操作：',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _addTestImage,
                  icon: const Icon(Icons.add),
                  label: const Text('添加测试图片'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: uploadedFiles.isNotEmpty ? _resetImages : null,
                  icon: const Icon(Icons.refresh),
                  label: const Text('重置图片'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: Colors.green[700],
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '修复说明：使用 clipBehavior: Clip.none 允许删除按钮超出容器边界，并调整了按钮位置和样式',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 处理文件变化
  void _onValueChanged(List<TDUploadFile> value, TDUploadType event) {
    setState(() {
      final List<TDUploadFile> newFiles = List.from(uploadedFiles);

      switch (event) {
        case TDUploadType.add:
          newFiles.addAll(value);
          break;
        case TDUploadType.remove:
          newFiles.removeWhere((element) => element.key == value[0].key);
          _showMessage('图片已删除');
          break;
        case TDUploadType.replace:
          final firstReplaceFile = value.first;
          final index = newFiles.indexWhere((file) => file.key == firstReplaceFile.key);
          if (index != -1) {
            newFiles[index] = firstReplaceFile;
          }
          break;
      }

      uploadedFiles = newFiles;
    });
  }

  /// 添加测试图片
  void _addTestImage() {
    if (uploadedFiles.length >= 9) {
      _showMessage('最多只能上传9张图片');
      return;
    }

    setState(() {
      final random = DateTime.now().millisecondsSinceEpoch;
      final newFile = TDUploadFile(
        key: random,
        remotePath: 'https://picsum.photos/200/200?random=$random',
        status: TDUploadFileStatus.success,
        canDelete: true,
      );

      uploadedFiles.add(newFile);
    });
    _showMessage('测试图片添加成功');
  }

  /// 重置图片
  void _resetImages() {
    _initializeData();
    _showMessage('图片已重置');
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'TDUploadH Delete Button Test',
      home: const TDUploadHDeleteButtonTest(),
      builder: (context, child) {
        ScreenUtil.init(context, designSize: const Size(375, 812));
        return child!;
      },
    );
  }
}
