import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/diary_details/diary_details_page.dart';
import 'package:flutter_kexue/page/page/diary_details/entity/diary_details_props.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 日记详情页面示例
class DiaryDetailsExample extends StatelessWidget {
  const DiaryDetailsExample({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Diary Details Example',
      home: const DiaryDetailsExampleHome(),
      builder: (context, child) {
        ScreenUtil.init(context, designSize: const Size(375, 812));
        return child!;
      },
    );
  }
}

class DiaryDetailsExampleHome extends StatelessWidget {
  const DiaryDetailsExampleHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日记详情示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明文字
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '日记详情页面功能：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '• 内容为空时显示"记一笔"空状态\n'
                    '• 有内容时显示完整UI\n'
                    '• 内容超过300字符时支持展开收起\n'
                    '• 9宫格图片显示\n'
                    '• 标题栏编辑和更多按钮',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 示例按钮
            _buildExampleButton(
              context: context,
              title: '示例1：空内容',
              description: '显示"记一笔"空状态',
              onTap: () => _showEmptyDiary(context),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              context: context,
              title: '示例2：短内容',
              description: '显示短文本内容',
              onTap: () => _showShortContentDiary(context),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              context: context,
              title: '示例3：长内容',
              description: '显示长文本内容，支持展开收起',
              onTap: () => _showLongContentDiary(context),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              context: context,
              title: '示例4：带图片',
              description: '显示文本内容和图片网格',
              onTap: () => _showDiaryWithImages(context),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              context: context,
              title: '示例5：只有图片',
              description: '只显示图片网格',
              onTap: () => _showOnlyImagesDiary(context),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建示例按钮
  Widget _buildExampleButton({
    required BuildContext context,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.sp,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 显示空内容日记
  void _showEmptyDiary(BuildContext context) {
    final props = DiaryDetailsProps(
      id: '1',
      dateTime: DateTime.now(),
      content: '',
      images: [],
      canEdit: true,
    );

    Get.to(() => DiaryDetailsPage(props: props));
  }

  /// 显示短内容日记
  void _showShortContentDiary(BuildContext context) {
    final props = DiaryDetailsProps(
      id: '2',
      dateTime: DateTime.now(),
      content: '今天是美好的第七天，清晨醒来，意识还在混沌与清醒间徘徊，熟悉的冲动如潮水般涌来。我努力死撑住了，指甲几乎抠进了手心，强迫自己立刻光明正大地起身，并开始刷牙洗脸的那瞬间，那颗险些沦陷的心，像一记警钟，将我从危险边缘拉回现实。',
      images: [],
      canEdit: true,
    );

    Get.to(() => DiaryDetailsPage(props: props));
  }

  /// 显示长内容日记
  void _showLongContentDiary(BuildContext context) {
    final props = DiaryDetailsProps(
      id: '3',
      dateTime: DateTime.now(),
      content: '''今天是美好的第七天，清晨醒来，意识还在混沌与清醒间徘徊，熟悉的冲动如潮水般涌来。我努力死撑住了，指甲几乎抠进了手心，强迫自己立刻光明正大地起身，并开始刷牙洗脸的那瞬间，那颗险些沦陷的心，像一记警钟，将我从危险边缘拉回现实。

这是一段很长的内容，用来测试展开收起功能。当内容超过300个字符时，会显示展开收起按钮。用户可以点击"全文"来展开完整内容，也可以点击"收起"来折叠内容。

这个功能的实现参考了计划详情页面的展开收起逻辑，使用了GetX的响应式状态管理。当用户点击展开收起按钮时，会触发状态更新，从而重新渲染UI。

在实际使用中，这个功能可以帮助用户更好地浏览长篇日记内容，避免页面过长影响阅读体验。同时，也可以让用户快速浏览日记的主要内容，需要时再展开查看详细信息。''',
      images: [],
      canEdit: true,
    );

    Get.to(() => DiaryDetailsPage(props: props));
  }

  /// 显示带图片的日记
  void _showDiaryWithImages(BuildContext context) {
    final props = DiaryDetailsProps(
      id: '4',
      dateTime: DateTime.now(),
      content: '今天是美好的第七天，清晨醒来，意识还在混沌与清醒间徘徊，熟悉的冲动如潮水般涌来。',
      images: [
        'https://picsum.photos/400/400?random=1',
        'https://picsum.photos/400/400?random=2',
        'https://picsum.photos/400/400?random=3',
        'https://picsum.photos/400/400?random=4',
        'https://picsum.photos/400/400?random=5',
        'https://picsum.photos/400/400?random=6',
        'https://picsum.photos/400/400?random=7',
        'https://picsum.photos/400/400?random=8',
        'https://picsum.photos/400/400?random=9',
      ],
      canEdit: true,
    );

    Get.to(() => DiaryDetailsPage(props: props));
  }

  /// 显示只有图片的日记
  void _showOnlyImagesDiary(BuildContext context) {
    final props = DiaryDetailsProps(
      id: '5',
      dateTime: DateTime.now(),
      content: '',
      images: [
        'https://picsum.photos/400/400?random=10',
        'https://picsum.photos/400/400?random=11',
        'https://picsum.photos/400/400?random=12',
      ],
      canEdit: true,
    );

    Get.to(() => DiaryDetailsPage(props: props));
  }
}

void main() {
  runApp(const DiaryDetailsExample());
}
