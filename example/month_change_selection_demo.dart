import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';

/// 月份切换自动选中演示
class MonthChangeSelectionDemo extends StatefulWidget {
  const MonthChangeSelectionDemo({super.key});

  @override
  State<MonthChangeSelectionDemo> createState() => _MonthChangeSelectionDemoState();
}

class _MonthChangeSelectionDemoState extends State<MonthChangeSelectionDemo> {
  DateTime _currentDate = DateTime.now();
  DateTime? _selectedDate;

  // 模拟有数据的日期
  final Set<DateTime> _datesWithData = {
    DateTime.now().subtract(const Duration(days: 2)),
    DateTime.now().subtract(const Duration(days: 1)),
    DateTime.now(),
    DateTime.now().add(const Duration(days: 1)),
    DateTime.now().add(const Duration(days: 2)),
  };

  @override
  void initState() {
    super.initState();
    // 初始化时根据当前月份设置选中日期
    _handleMonthChangeSelection(_currentDate.year, _currentDate.month);
  }

  @override
  Widget build(BuildContext context) {
    final today = DateTime.now();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('月份切换自动选中演示'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息卡片
            _buildStatusCard(today),
            
            const SizedBox(height: 20),
            
            // 功能说明
            _buildInstructionCard(),
            
            const SizedBox(height: 20),
            
            // 日历组件
            _buildCalendarCard(),
            
            const SizedBox(height: 20),
            
            // 快速切换按钮
            _buildQuickSwitchButtons(today),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(DateTime today) {
    final isCurrentMonth = _isCurrentMonth(today);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('今天: ${today.year}年${today.month}月${today.day}日'),
            Text('当前显示月份: ${_currentDate.year}年${_currentDate.month}月'),
            Text('选中日期: ${_selectedDate != null ? "${_selectedDate!.year}年${_selectedDate!.month}月${_selectedDate!.day}日" : "未选中"}'),
            const SizedBox(height: 12),
            
            // 显示是否为当月
            Row(
              children: [
                Icon(
                  isCurrentMonth ? Icons.today : Icons.calendar_month,
                  color: isCurrentMonth ? Colors.green : Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isCurrentMonth ? '当前显示本月 → 选中今天' : '当前显示其他月 → 选中第1天',
                  style: TextStyle(
                    color: isCurrentMonth ? Colors.green : Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            
            if (_selectedDate != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isCurrentMonth ? Colors.green.withOpacity(0.1) : Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  isCurrentMonth ? '已选中今天' : '已选中第1天',
                  style: TextStyle(
                    color: isCurrentMonth ? Colors.green : Colors.blue,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionCard() {
    return Card(
      color: Colors.orange.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb_outline, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  '功能说明',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text('📅 切换到当前月份时：自动选中今天'),
            const Text('📆 切换到其他月份时：自动选中该月第1天'),
            const Text('🔄 使用下方按钮或滑动日历来测试'),
            const Text('👀 观察状态卡片中的选中日期变化'),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '日历组件',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TDCalendar(
              selectedDate: _selectedDate,
              onMonthChanged: (monthFirstDay) {
                setState(() {
                  _currentDate = monthFirstDay;
                });

                // 处理月份切换时的自动选中逻辑
                _handleMonthChangeSelection(monthFirstDay.year, monthFirstDay.month);
                
                // 显示提示
                final today = DateTime.now();
                final isCurrentMonth = monthFirstDay.year == today.year && monthFirstDay.month == today.month;
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isCurrentMonth
                        ? '切换到当前月份，已选中今天'
                        : '切换到${monthFirstDay.year}年${monthFirstDay.month}月，已选中第1天'
                    ),
                    backgroundColor: isCurrentMonth ? Colors.green : Colors.blue,
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
              onDateTap: (date) {
                setState(() {
                  _selectedDate = date;
                });
                
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('手动选中: ${date.year}年${date.month}月${date.day}日'),
                    duration: const Duration(seconds: 1),
                  ),
                );
              },
              datesWithData: _datesWithData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSwitchButtons(DateTime today) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速切换测试',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // 第一行按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _switchToMonth(today.year, today.month),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('当前月\n(${today.month}月)', textAlign: TextAlign.center),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      final prevMonth = today.month == 1 ? 12 : today.month - 1;
                      final prevYear = today.month == 1 ? today.year - 1 : today.year;
                      _switchToMonth(prevYear, prevMonth);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('上个月\n(${today.month == 1 ? 12 : today.month - 1}月)', textAlign: TextAlign.center),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // 第二行按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      final nextMonth = today.month == 12 ? 1 : today.month + 1;
                      final nextYear = today.month == 12 ? today.year + 1 : today.year;
                      _switchToMonth(nextYear, nextMonth);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('下个月\n(${today.month == 12 ? 1 : today.month + 1}月)', textAlign: TextAlign.center),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _switchToMonth(2024, 1),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('2024年1月\n(测试)', textAlign: TextAlign.center),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 切换到指定月份
  void _switchToMonth(int year, int month) {
    setState(() {
      _currentDate = DateTime(year, month, 1);
    });
    _handleMonthChangeSelection(year, month);
  }

  /// 处理月份切换时的日期选中逻辑
  void _handleMonthChangeSelection(int year, int month) {
    final today = DateTime.now();
    final isCurrentMonth = year == today.year && month == today.month;
    
    setState(() {
      if (isCurrentMonth) {
        // 如果是当前月份，选中今天
        _selectedDate = today;
      } else {
        // 如果不是当前月份，选中当月第一天
        _selectedDate = DateTime(year, month, 1);
      }
    });
  }

  /// 检查当前显示的是否为本月
  bool _isCurrentMonth(DateTime today) {
    return _currentDate.year == today.year && _currentDate.month == today.month;
  }
}
