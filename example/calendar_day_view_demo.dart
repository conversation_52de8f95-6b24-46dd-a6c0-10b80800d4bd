import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/view/calendar_day_view.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 日历日期视图演示页面
class CalendarDayViewDemo extends StatelessWidget {
  const CalendarDayViewDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日历日期视图演示'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '不同数据状态的日历日期显示效果：',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 16.h),
            
            // 演示网格
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16.w,
                mainAxisSpacing: 16.h,
                childAspectRatio: 1.2,
                children: [
                  // 1. 无数据的普通日期
                  _buildDemoCard(
                    '普通日期',
                    CalendarDayData(
                      date: DateTime(2025, 7, 15),
                      isCurrentMonth: true,
                      isToday: false,
                    ),
                  ),
                  
                  // 2. 今天
                  _buildDemoCard(
                    '今天',
                    CalendarDayData(
                      date: DateTime.now(),
                      isCurrentMonth: true,
                      isToday: true,
                    ),
                  ),
                  
                  // 3. 有记录数据
                  _buildDemoCard(
                    '有记录数据',
                    CalendarDayData(
                      date: DateTime(2025, 7, 16),
                      isCurrentMonth: true,
                      isToday: false,
                      listRecord: ['记录1', '记录2', '记录3', '记录4'],
                    ),
                  ),
                  
                  // 4. 有正能量打卡数据
                  _buildDemoCard(
                    '正能量打卡',
                    CalendarDayData(
                      date: DateTime(2025, 7, 17),
                      isCurrentMonth: true,
                      isToday: false,
                      listPositive: ['运动', '读书', '冥想'],
                    ),
                  ),
                  
                  // 5. 有持戒数据（单独一个列表，应该占满剩余高度）
                  _buildDemoCard(
                    '持戒打卡（单独）',
                    CalendarDayData(
                      date: DateTime(2025, 7, 18),
                      isCurrentMonth: true,
                      isToday: false,
                      listNegative: ['戒烟', '戒酒'],
                    ),
                  ),
                  
                  // 6. 两个列表数据（应该各占一半高度）
                  _buildDemoCard(
                    '两个列表',
                    CalendarDayData(
                      date: DateTime(2025, 7, 19),
                      isCurrentMonth: true,
                      isToday: false,
                      listPositive: ['运动', '学习'],
                      listNegative: ['戒糖', '戒熬夜'],
                    ),
                  ),

                  // 7. 混合数据（三个列表各占1/3）
                  _buildDemoCard(
                    '三个列表',
                    CalendarDayData(
                      date: DateTime(2025, 7, 20),
                      isCurrentMonth: true,
                      isToday: false,
                      listRecord: ['日记', '照片'],
                      listPositive: ['运动', '学习'],
                      listNegative: ['戒糖', '戒熬夜'],
                      isPhoto: true,
                      isDiary: true,
                    ),
                  ),

                  // 8. 只有记录数据（应该占满剩余高度）
                  _buildDemoCard(
                    '只有记录',
                    CalendarDayData(
                      date: DateTime(2025, 7, 21),
                      isCurrentMonth: true,
                      isToday: false,
                      listRecord: ['重要记录', '特殊事件'],
                      isPhoto: true,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建演示卡片
  Widget _buildDemoCard(String title, CalendarDayData dayData) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.r),
                topRight: Radius.circular(8.r),
              ),
            ),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          // 日历日期视图
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: CalendarDayView(dayData: dayData),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: const CalendarDayViewDemo(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
