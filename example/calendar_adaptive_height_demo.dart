import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';

/// 日历自适应高度演示
class CalendarAdaptiveHeightDemo extends StatefulWidget {
  const CalendarAdaptiveHeightDemo({super.key});

  @override
  State<CalendarAdaptiveHeightDemo> createState() => _CalendarAdaptiveHeightDemoState();
}

class _CalendarAdaptiveHeightDemoState extends State<CalendarAdaptiveHeightDemo> {
  DateTime _currentDate = DateTime.now();
  
  // 模拟有数据的日期
  final Set<DateTime> _datesWithData = {
    DateTime(2024, 2, 14), // 2月情人节
    DateTime(2024, 2, 29), // 2月29日（闰年）
    DateTime(2024, 3, 8),  // 3月妇女节
    DateTime(2024, 3, 15),
    DateTime(2024, 3, 22),
    DateTime(2024, 4, 1),  // 4月愚人节
    DateTime(2024, 5, 1),  // 5月劳动节
    DateTime(2024, 5, 12),
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('自适应高度日历演示'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 当前月份信息
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_currentDate.year}年${_currentDate.month}月',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '行数: ${_calculateMonthRows(_currentDate)}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          
          // 快速切换按钮
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildQuickButton('2024年2月', DateTime(2024, 2, 1)),
                _buildQuickButton('2024年3月', DateTime(2024, 3, 1)),
                _buildQuickButton('2024年4月', DateTime(2024, 4, 1)),
                _buildQuickButton('2024年5月', DateTime(2024, 5, 1)),
              ],
            ),
          ),
          
          const Divider(),
          
          // 日历组件
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TDCalendar(
                  selectedDate: _currentDate,
                  onMonthChanged: (monthFirstDay) {
                    setState(() {
                      _currentDate = monthFirstDay;
                    });
                  },
                  onDateTap: (date) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('点击了: ${date.year}年${date.month}月${date.day}日'),
                        duration: const Duration(seconds: 1),
                      ),
                    );
                  },
                  datesWithData: _datesWithData,
                ),
              ),
            ),
          ),
          
          // 说明文字
          Container(
            padding: const EdgeInsets.all(16),
            child: const Text(
              '注意观察不同月份的高度变化：\n'
              '• 2月通常只需要4-5行\n'
              '• 3月、5月等通常需要5-6行\n'
              '• 高度会根据实际行数自动调整',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickButton(String text, DateTime date) {
    final isSelected = _currentDate.year == date.year && _currentDate.month == date.month;
    
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _currentDate = date;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Colors.green : Colors.grey[300],
        foregroundColor: isSelected ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  /// 计算月份需要的行数（与日历组件中的逻辑保持一致）
  int _calculateMonthRows(DateTime monthDate) {
    // 获取月份第一天
    final firstDay = DateTime(monthDate.year, monthDate.month, 1);
    
    // 获取月份最后一天
    final lastDay = DateTime(monthDate.year, monthDate.month + 1, 0);
    
    // 获取第一天是星期几（0=周日，1=周一...6=周六）
    final firstWeekday = firstDay.weekday % 7;
    
    // 计算总天数（包括上个月填充的天数）
    final totalDays = firstWeekday + lastDay.day;
    
    // 计算需要的行数
    return (totalDays / 7).ceil();
  }
}
