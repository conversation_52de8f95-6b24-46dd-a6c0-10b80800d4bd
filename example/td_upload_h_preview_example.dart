import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_kexue/widget/upload/td_upload_h.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// TDUploadH 图片预览功能示例
class TDUploadHPreviewExample extends StatefulWidget {
  const TDUploadHPreviewExample({super.key});

  @override
  State<TDUploadHPreviewExample> createState() => _TDUploadHPreviewExampleState();
}

class _TDUploadHPreviewExampleState extends State<TDUploadHPreviewExample> {
  List<TDUploadFile> uploadedFiles = <TDUploadFile>[];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // 添加一些示例图片
    setState(() {
      uploadedFiles.addAll([
        TDUploadFile(
          key: 1,
          remotePath: 'https://picsum.photos/400/400?random=1',
          status: TDUploadFileStatus.success,
        ),
        TDUploadFile(
          key: 2,
          remotePath: 'https://picsum.photos/400/400?random=2',
          status: TDUploadFileStatus.success,
        ),
        TDUploadFile(
          key: 3,
          remotePath: 'https://picsum.photos/400/400?random=3',
          status: TDUploadFileStatus.success,
        ),
      ]);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TDUploadH 图片预览示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 功能说明
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '图片预览功能特点：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '• 点击图片可以预览大图\n'
                    '• 支持缩放、滑动切换\n'
                    '• 可控制删除和下载按钮显示\n'
                    '• 删除后自动同步到上级页面\n'
                    '• 支持本地文件和网络图片',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 示例1：完整功能（可删除、可下载）
            _buildExampleSection(
              title: '示例1：完整功能',
              description: '支持预览、删除、下载',
              child: TDUploadH(
                files: uploadedFiles,
                max: 9,
                height: 77,
                spacing: 10,
                showAddButton: true,
                enablePreview: true,
                canDelete: true,
                canDownload: true,
                onChange: (fileList, type) => _onValueChanged(fileList, type),
                onMaxLimitReached: () => _showMessage('最多只能上传9张图片'),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例2：只能预览和下载（不能删除）
            _buildExampleSection(
              title: '示例2：只读模式',
              description: '只能预览和下载，不能删除',
              child: TDUploadH(
                files: uploadedFiles,
                max: 9,
                height: 77,
                spacing: 10,
                showAddButton: false,
                enablePreview: true,
                canDelete: false,
                canDownload: true,
                onChange: (fileList, type) => _onValueChanged(fileList, type),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例3：纯展示模式（不能删除、不能下载）
            _buildExampleSection(
              title: '示例3：纯展示模式',
              description: '只能预览，不能删除和下载',
              child: TDUploadH(
                files: uploadedFiles,
                max: 9,
                height: 77,
                spacing: 10,
                showAddButton: false,
                enablePreview: true,
                canDelete: false,
                canDownload: false,
                onChange: (fileList, type) => _onValueChanged(fileList, type),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例4：禁用预览
            _buildExampleSection(
              title: '示例4：禁用预览',
              description: '点击图片不会跳转预览',
              child: TDUploadH(
                files: uploadedFiles,
                max: 9,
                height: 77,
                spacing: 10,
                showAddButton: true,
                enablePreview: false,
                canDelete: true,
                canDownload: true,
                onChange: (fileList, type) => _onValueChanged(fileList, type),
              ),
            ),

            SizedBox(height: 24.h),

            // 控制按钮
            _buildControlSection(),
          ],
        ),
      ),
    );
  }

  /// 构建示例区域
  Widget _buildExampleSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            description,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }

  /// 构建控制区域
  Widget _buildControlSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '控制操作：',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 16.h),
          Wrap(
            spacing: 12.w,
            runSpacing: 12.h,
            children: [
              ElevatedButton(
                onPressed: _addRandomImage,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                child: const Text('添加图片', style: TextStyle(color: Colors.white)),
              ),
              ElevatedButton(
                onPressed: _addLocalImage,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: const Text('添加本地图片', style: TextStyle(color: Colors.white)),
              ),
              ElevatedButton(
                onPressed: uploadedFiles.isNotEmpty ? _clearAllImages : null,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('清空所有', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 处理文件变化
  void _onValueChanged(List<TDUploadFile> value, TDUploadType event) {
    setState(() {
      final List<TDUploadFile> newFiles = List.from(uploadedFiles);

      switch (event) {
        case TDUploadType.add:
          newFiles.addAll(value);
          break;
        case TDUploadType.remove:
          newFiles.removeWhere((element) => element.key == value[0].key);
          break;
        case TDUploadType.replace:
          final firstReplaceFile = value.first;
          final index = newFiles.indexWhere((file) => file.key == firstReplaceFile.key);
          if (index != -1) {
            newFiles[index] = firstReplaceFile;
          }
          break;
      }

      uploadedFiles = newFiles;
    });
  }

  /// 添加随机网络图片
  void _addRandomImage() {
    if (uploadedFiles.length >= 9) {
      _showMessage('最多只能上传9张图片');
      return;
    }

    setState(() {
      final random = DateTime.now().millisecondsSinceEpoch;
      final newFile = TDUploadFile(
        key: random,
        remotePath: 'https://picsum.photos/400/400?random=$random',
        status: TDUploadFileStatus.success,
      );

      uploadedFiles.add(newFile);
    });
    _showMessage('网络图片添加成功');
  }

  /// 添加本地图片（模拟）
  void _addLocalImage() {
    if (uploadedFiles.length >= 9) {
      _showMessage('最多只能上传9张图片');
      return;
    }

    setState(() {
      // 这里模拟一个本地文件路径
      final random = DateTime.now().millisecondsSinceEpoch;
      final newFile = TDUploadFile(
        key: random,
        // 注意：这里使用一个模拟的本地路径，实际使用时应该是真实的文件路径
        file: File('/path/to/local/image_$random.jpg'),
        status: TDUploadFileStatus.success,
      );

      uploadedFiles.add(newFile);
    });
    _showMessage('本地图片添加成功（模拟）');
  }

  /// 清空所有图片
  void _clearAllImages() {
    setState(() {
      uploadedFiles.clear();
    });
    _showMessage('所有图片已清空');
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'TDUploadH Preview Example',
      home: const TDUploadHPreviewExample(),
      builder: (context, child) {
        ScreenUtil.init(context, designSize: const Size(375, 812));
        return child!;
      },
    );
  }
}
