import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';

/// 选中日期演示
class SelectedDateDemo extends StatefulWidget {
  const SelectedDateDemo({super.key});

  @override
  State<SelectedDateDemo> createState() => _SelectedDateDemoState();
}

class _SelectedDateDemoState extends State<SelectedDateDemo> {
  DateTime _currentDate = DateTime.now();
  DateTime? _selectedDate;

  // 模拟有数据的日期
  final Set<DateTime> _datesWithData = {
    DateTime(2024, 3, 8),
    DateTime(2024, 3, 15),
    DateTime(2024, 3, 22),
    DateTime(2024, 3, 29),
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选中日期演示'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息卡片
            _buildStatusCard(),
            
            const SizedBox(height: 20),
            
            // 日历组件
            _buildCalendarCard(),
            
            const SizedBox(height: 20),
            
            // 操作按钮
            _buildActionButtons(),
            
            const SizedBox(height: 20),
            
            // 功能说明
            _buildFeatureDescription(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('当前月份: ${_currentDate.year}年${_currentDate.month}月'),
            Text('选中日期: ${_selectedDate != null ? "${_selectedDate!.year}年${_selectedDate!.month}月${_selectedDate!.day}日" : "未选中"}'),
            if (_selectedDate != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF4ACE7F).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '已选中',
                  style: TextStyle(
                    color: const Color(0xFF4ACE7F),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '日历组件',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TDCalendar(
              selectedDate: _selectedDate,
              onMonthChanged: (monthFirstDay) {
                setState(() {
                  _currentDate = monthFirstDay;
                });
              },
              onDateTap: (date) {
                setState(() {
                  _selectedDate = date;
                });
                
                // 显示选中提示
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('选中了: ${date.year}年${date.month}月${date.day}日'),
                    duration: const Duration(seconds: 1),
                    backgroundColor: const Color(0xFF4ACE7F),
                  ),
                );
              },
              datesWithData: _datesWithData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '操作按钮',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _selectedDate = DateTime.now();
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4ACE7F),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('选中今天'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _selectedDate = null;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('清除选中'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  final random = DateTime.now().millisecond % 28 + 1;
                  setState(() {
                    _selectedDate = DateTime(_currentDate.year, _currentDate.month, random);
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('随机选中日期'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '功能说明',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text('✅ 点击日期可以选中，选中的日期会显示浅绿色背景'),
            const Text('✅ 支持程序化设置选中日期'),
            const Text('✅ 可以清除选中状态'),
            const Text('✅ 选中状态在月份切换时保持'),
            const Text('✅ 绿色边框表示有数据的日期'),
            const Text('✅ 今天会显示特殊的文字颜色'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: const Text(
                '提示：选中日期的背景色为浅绿色，与今天的文字颜色和有数据日期的边框颜色保持一致的设计风格。',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
