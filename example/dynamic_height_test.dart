import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/view/calendar_day_view.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 动态高度测试页面
class DynamicHeightTest extends StatelessWidget {
  const DynamicHeightTest({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('动态高度测试'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '固定1/3高度规则测试：',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '• 1个列表：占1/3高度，剩余空白\n• 2个列表：各占1/3高度，剩余空白\n• 3个列表：各占1/3高度',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            
            // 测试网格
            Expanded(
              child: GridView.count(
                crossAxisCount: 3,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.h,
                childAspectRatio: 0.8,
                children: [
                  // 1. 只有记录数据（占1/3高度，剩余空白）
                  _buildTestCard(
                    '只有记录\n(1/3+空白)',
                    CalendarDayData(
                      date: DateTime(2025, 7, 15),
                      isCurrentMonth: true,
                      isToday: false,
                      listRecord: ['重要记录'],
                    ),
                  ),

                  // 2. 只有正能量数据（占1/3高度，剩余空白）
                  _buildTestCard(
                    '只有正能量\n(1/3+空白)',
                    CalendarDayData(
                      date: DateTime(2025, 7, 16),
                      isCurrentMonth: true,
                      isToday: false,
                      listPositive: ['运动'],
                    ),
                  ),

                  // 3. 只有持戒数据（占1/3高度，剩余空白）
                  _buildTestCard(
                    '只有持戒\n(1/3+空白)',
                    CalendarDayData(
                      date: DateTime(2025, 7, 17),
                      isCurrentMonth: true,
                      isToday: false,
                      listNegative: ['戒烟'],
                    ),
                  ),

                  // 4. 两个列表（各占1/3高度，剩余空白）
                  _buildTestCard(
                    '两个列表\n(2/3+空白)',
                    CalendarDayData(
                      date: DateTime(2025, 7, 18),
                      isCurrentMonth: true,
                      isToday: false,
                      listRecord: ['记录'],
                      listPositive: ['运动'],
                    ),
                  ),

                  // 5. 三个列表（各占1/3高度）
                  _buildTestCard(
                    '三个列表\n(满格)',
                    CalendarDayData(
                      date: DateTime(2025, 7, 19),
                      isCurrentMonth: true,
                      isToday: false,
                      listRecord: ['记录'],
                      listPositive: ['运动'],
                      listNegative: ['戒烟'],
                    ),
                  ),
                  
                  // 6. 无数据（显示传统布局）
                  _buildTestCard(
                    '无数据',
                    CalendarDayData(
                      date: DateTime(2025, 7, 20),
                      isCurrentMonth: true,
                      isToday: false,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建测试卡片
  Widget _buildTestCard(String title, CalendarDayData dayData) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.r),
                topRight: Radius.circular(8.r),
              ),
            ),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          // 日历日期视图
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(4.w),
              child: CalendarDayView(dayData: dayData),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: const DynamicHeightTest(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
