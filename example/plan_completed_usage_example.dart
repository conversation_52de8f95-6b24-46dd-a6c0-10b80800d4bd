import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/view/plan_completed_view.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/plan_home_viewmodel.dart';

/// 已完成计划视图使用示例
class PlanCompletedUsageExample extends StatefulWidget {
  const PlanCompletedUsageExample({super.key});

  @override
  State<PlanCompletedUsageExample> createState() => _PlanCompletedUsageExampleState();
}

class _PlanCompletedUsageExampleState extends State<PlanCompletedUsageExample>
    with TickerProviderStateMixin {
  late PlanHomeViewModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = PlanHomeViewModel();
    viewModel.initTabController(this);
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('已完成计划示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Column(
        children: [
          // 功能说明
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.blue[50],
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '功能演示：',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text('• 筛选选项：全部、成功、破戒、终止'),
                Text('• 搜索功能：输入关键词搜索计划'),
                Text('• 按日期分组显示'),
                Text('• 不同状态显示不同颜色'),
                Text('• 点击计划项查看详情'),
              ],
            ),
          ),
          
          // 已完成计划视图
          Expanded(
            child: PlanCompletedView(viewModel: viewModel),
          ),
        ],
      ),
    );
  }
}

/// 在应用中使用示例
void main() {
  runApp(MaterialApp(
    home: const PlanCompletedUsageExample(),
    theme: ThemeData(
      primarySwatch: Colors.green,
    ),
  ));
}

/// 使用说明：
/// 
/// 1. 筛选功能：
///    - 点击顶部的"全部"、"成功"、"破戒"、"终止"来筛选不同状态的计划
///    - 选中的筛选项会显示为绿色
/// 
/// 2. 搜索功能：
///    - 在搜索框中输入关键词（如"早睡100天"）
///    - 点击搜索按钮或直接输入进行搜索
///    - 支持标题和副标题的模糊搜索
/// 
/// 3. 列表显示：
///    - 按日期分组显示计划
///    - 不同状态用不同颜色的指示器：
///      * 绿色：成功/戒断类型
///      * 红色：负面记录/破戒
///    - 显示进度条（已完成的计划）
///    - 点击计划项可查看详情
/// 
/// 4. 数据结构：
///    ```dart
///    CompletedPlanItemUIState(
///      title: '戒撸100天',
///      subtitle: '成功 共50天',
///      date: '2025年7月2日',
///      status: CompletedPlanStatus.success,
///      progressText: '完成200次',
///      type: PlanItemType.abstinence,
///    )
///    ```
