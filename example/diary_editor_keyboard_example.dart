import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_kexue/widget/upload/td_upload_h.dart';
import 'package:flutter_kexue/widget/dialog/image_picker_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 日记编辑页面键盘处理示例
class DiaryEditorKeyboardExample extends StatefulWidget {
  const DiaryEditorKeyboardExample({super.key});

  @override
  State<DiaryEditorKeyboardExample> createState() => _DiaryEditorKeyboardExampleState();
}

class _DiaryEditorKeyboardExampleState extends State<DiaryEditorKeyboardExample> {
  final RxList<TDUploadFile> uploadedFiles = <TDUploadFile>[].obs;
  final TextEditingController textController = TextEditingController();
  final FocusNode textFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 添加一些示例图片
    uploadedFiles.addAll([
      TDUploadFile(
        key: 1,
        remotePath: 'https://picsum.photos/200/200?random=1',
        status: TDUploadFileStatus.success,
      ),
      TDUploadFile(
        key: 2,
        remotePath: 'https://picsum.photos/200/200?random=2',
        status: TDUploadFileStatus.success,
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true, // 键盘弹出时调整布局
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // 主要内容区域
          Expanded(
            child: _buildBody(),
          ),
          // 底部栏（会被键盘顶起）
          _buildBottomBar(),
        ],
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        '日记',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
      ),
      centerTitle: true,
      actions: [
        Container(
          margin: EdgeInsets.only(right: 16.w),
          child: TextButton(
            onPressed: _saveContent,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                '保存',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 16.h, bottom: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期显示
          _buildDateSection(),

          SizedBox(height: 20.h),

          // 文本编辑区域
          _buildTextEditor(),
        ],
      ),
    );
  }

  /// 构建日期显示部分
  Widget _buildDateSection() {
    final now = DateTime.now();
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[now.weekday - 1];

    return Row(
      children: [
        Text(
          '${now.year}年${now.month}月${now.day}日',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        SizedBox(width: 10.w),
        Text(
          weekday,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建文本编辑器
  Widget _buildTextEditor() {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(minHeight: 120.h),
      alignment: Alignment.topLeft,
      child: TextField(
        controller: textController,
        focusNode: textFocusNode,
        maxLines: null,
        keyboardType: TextInputType.multiline,
        decoration: InputDecoration(
          hintText: '这一刻的想法...',
          hintStyle: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(0),
          isDense: true,
        ),
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.black,
          height: 1.0,
        ),
        onChanged: (value) {
          print('文本内容: $value');
        },
      ),
    );
  }

  /// 构建底部栏（会被键盘顶起）
  Widget _buildBottomBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false, // 不需要顶部安全区域
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 横向滑动图片区域
            _buildHorizontalImageUpload(),

            // 底部操作栏
            _buildBottomActionBar(),
          ],
        ),
      ),
    );
  }

  /// 构建横向滑动图片上传区域
  Widget _buildHorizontalImageUpload() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Obx(() {
        return TDUploadH(
          files: uploadedFiles,
          max: 9,
          height: 60,
          spacing: 10,
          showAddButton: false, // 通过底部按钮添加
          onClick: (key) => _viewImage(key),
          onChange: (fileList, type) => _onValueChanged(fileList, type),
        );
      }),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      color: Colors.grey[50],
      child: Row(
        children: [
          // 图片按钮
          GestureDetector(
            onTap: _onImageButtonTap,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.image_outlined,
                  size: 24.sp,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 8.w),
                Text(
                  '图片',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          Spacer(),
          
          // 图片计数
          Obx(() {
            return Text(
              '${uploadedFiles.length}/9',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[500],
              ),
            );
          }),
          
          SizedBox(width: 8.w),
          
          // 可见性标识
          Icon(
            Icons.visibility_outlined,
            size: 16.sp,
            color: Colors.grey[500],
          ),
          SizedBox(width: 4.w),
          Text(
            '自己可见',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 图片按钮点击事件
  void _onImageButtonTap() {
    // 关闭键盘
    _dismissKeyboard();
    
    // 延迟一点时间确保键盘完全关闭
    Future.delayed(const Duration(milliseconds: 100), () {
      // 显示图片选择对话框
      ImagePickerDialog.show(
        context: context,
        onImageSelected: _onImageSelected,
      );
    });
  }

  /// 图片选择回调
  void _onImageSelected(String imagePath) {
    // 检查是否达到最大限制
    if (uploadedFiles.length >= 9) {
      _showMessage('最多只能上传9张图片');
      return;
    }

    // 创建新的上传文件
    final newFile = TDUploadFile(
      key: DateTime.now().millisecondsSinceEpoch,
      file: File(imagePath),
      status: TDUploadFileStatus.success,
    );

    // 添加到列表
    _onValueChanged([newFile], TDUploadType.add);

    // 显示成功提示
    _showMessage('图片添加成功');
  }

  /// 处理文件变化
  void _onValueChanged(List<TDUploadFile> value, TDUploadType event) {
    final List<TDUploadFile> newFiles = List.from(uploadedFiles);

    switch (event) {
      case TDUploadType.add:
        newFiles.addAll(value);
        break;
      case TDUploadType.remove:
        newFiles.removeWhere((element) => element.key == value[0].key);
        break;
      case TDUploadType.replace:
        final firstReplaceFile = value.first;
        final index = newFiles.indexWhere((file) => file.key == firstReplaceFile.key);
        if (index != -1) {
          newFiles[index] = firstReplaceFile;
        }
        break;
    }

    uploadedFiles.assignAll(newFiles);
  }

  /// 查看图片
  void _viewImage(int key) {
    print('查看图片: $key');
    // 这里可以实现图片预览功能
  }

  /// 关闭键盘
  void _dismissKeyboard() {
    if (textFocusNode.hasFocus) {
      textFocusNode.unfocus();
    }
    FocusScope.of(context).unfocus();
  }

  /// 保存内容
  void _saveContent() {
    final content = textController.text;
    final imageCount = uploadedFiles.length;
    
    _showMessage('保存成功！内容长度: ${content.length}, 图片数量: $imageCount');
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    textController.dispose();
    textFocusNode.dispose();
    super.dispose();
  }
}

void main() {
  runApp(MaterialApp(
    home: const DiaryEditorKeyboardExample(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
