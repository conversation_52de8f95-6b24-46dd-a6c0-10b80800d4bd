import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/dialog/image_picker_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 图片选择对话框使用示例
class ImagePickerDialogUsageExample extends StatefulWidget {
  const ImagePickerDialogUsageExample({super.key});

  @override
  State<ImagePickerDialogUsageExample> createState() => _ImagePickerDialogUsageExampleState();
}

class _ImagePickerDialogUsageExampleState extends State<ImagePickerDialogUsageExample> {
  String? _selectedImagePath;
  List<String> _imageHistory = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图片选择对话框示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明文字
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '图片选择功能说明：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '• 支持拍照和从相册选择\n• 最多只能选择一张图片\n• 自动压缩图片质量\n• 限制图片最大尺寸为1920x1920',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 选择图片按钮
            Center(
              child: ElevatedButton.icon(
                onPressed: _showImagePicker,
                icon: const Icon(Icons.add_a_photo),
                label: const Text('选择图片'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ),
            ),

            SizedBox(height: 24.h),

            // 当前选中的图片显示
            if (_selectedImagePath != null) ...[
              Text(
                '当前选中的图片：',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 12.h),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // 图片预览
                    ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.r),
                        topRight: Radius.circular(8.r),
                      ),
                      child: Image.file(
                        File(_selectedImagePath!),
                        width: double.infinity,
                        height: 200.h,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: double.infinity,
                            height: 200.h,
                            color: Colors.grey[300],
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.error, size: 40.sp, color: Colors.red),
                                SizedBox(height: 8.h),
                                Text('图片加载失败', style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    // 图片信息
                    Padding(
                      padding: EdgeInsets.all(12.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '图片路径：',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            _selectedImagePath!,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: _showImagePicker,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('重新选择'),
                                ),
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: _clearImage,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('清除图片'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24.h),
            ],

            // 历史记录
            if (_imageHistory.isNotEmpty) ...[
              Text(
                '选择历史记录：',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  children: _imageHistory.asMap().entries.map((entry) {
                    final index = entry.key;
                    final path = entry.value;
                    return Padding(
                      padding: EdgeInsets.only(bottom: index < _imageHistory.length - 1 ? 8.h : 0),
                      child: Row(
                        children: [
                          Text('${index + 1}. '),
                          Expanded(
                            child: Text(
                              path.split('/').last,
                              style: TextStyle(fontSize: 12.sp),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 显示图片选择对话框
  void _showImagePicker() {
    // 方式1：使用静态方法
    ImagePickerDialog.show(
      context: context,
      onImageSelected: _onImageSelected,
    );

    // 方式2：使用扩展方法（注释掉的代码）
    // context.showImagePicker(
    //   onImageSelected: _onImageSelected,
    // );
  }

  /// 图片选择回调
  void _onImageSelected(String imagePath) {
    setState(() {
      _selectedImagePath = imagePath;
      _imageHistory.insert(0, imagePath);
      // 保持历史记录最多10条
      if (_imageHistory.length > 10) {
        _imageHistory = _imageHistory.take(10).toList();
      }
    });

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('图片选择成功: ${imagePath.split('/').last}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );

    print('选中的图片路径: $imagePath');
  }

  /// 清除当前图片
  void _clearImage() {
    setState(() {
      _selectedImagePath = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('图片已清除'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 1),
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: const ImagePickerDialogUsageExample(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
