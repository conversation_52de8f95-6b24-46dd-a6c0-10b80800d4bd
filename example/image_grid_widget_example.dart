import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/image_grid_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// ImageGridWidget 组件示例
class ImageGridWidgetExample extends StatelessWidget {
  const ImageGridWidgetExample({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'ImageGridWidget Example',
      home: const ImageGridWidgetExampleHome(),
      builder: (context, child) {
        ScreenUtil.init(context, designSize: const Size(375, 812));
        return child!;
      },
    );
  }
}

class ImageGridWidgetExampleHome extends StatelessWidget {
  const ImageGridWidgetExampleHome({super.key});

  // 测试图片数据
  final List<String> testImages = const [
    'https://picsum.photos/400/400?random=1',
    'https://picsum.photos/400/400?random=2',
    'https://picsum.photos/400/400?random=3',
    'https://picsum.photos/400/400?random=4',
    'https://picsum.photos/400/400?random=5',
    'https://picsum.photos/400/400?random=6',
    'https://picsum.photos/400/400?random=7',
    'https://picsum.photos/400/400?random=8',
    'https://picsum.photos/400/400?random=9',
    'https://picsum.photos/400/400?random=10',
    'https://picsum.photos/400/400?random=11',
    'https://picsum.photos/400/400?random=12',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ImageGridWidget 示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明文字
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ImageGridWidget 功能模式：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '• 默认模式：一行4个图片，超过显示+数量\n'
                    '• 自定义模式：支持N列布局，9宫格样式\n'
                    '• 支持自定义图片宽高和间距\n'
                    '• 保持向后兼容性\n'
                    '• 支持图片预览功能',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 示例1：默认模式（原有逻辑）
            _buildExampleSection(
              title: '示例1：默认模式',
              description: '一行4个图片，超过4个显示+数量遮罩',
              child: ImageGridWidget(
                images: testImages,
                onImageTap: (index) => _showMessage('点击了第${index + 1}张图片'),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例2：少于4个图片
            _buildExampleSection(
              title: '示例2：少于4个图片',
              description: '显示实际数量，无遮罩',
              child: ImageGridWidget(
                images: testImages.take(3).toList(),
                onImageTap: (index) => _showMessage('点击了第${index + 1}张图片'),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例3：自定义模式 - 3列
            _buildExampleSection(
              title: '示例3：自定义模式 - 3列',
              description: '启用9宫格布局，每行3个图片',
              child: ImageGridWidget(
                images: testImages,
                enableCustomLayout: true,
                itemsPerRow: 3,
                onImageTap: (index) => _showMessage('点击了第${index + 1}张图片'),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例4：自定义模式 - 2列
            _buildExampleSection(
              title: '示例4：自定义模式 - 2列',
              description: '每行2个图片，较大尺寸',
              child: ImageGridWidget(
                images: testImages.take(6).toList(),
                enableCustomLayout: true,
                itemsPerRow: 2,
                onImageTap: (index) => _showMessage('点击了第${index + 1}张图片'),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例5：自定义尺寸
            _buildExampleSection(
              title: '示例5：自定义尺寸',
              description: '固定宽高80x60，自定义间距',
              child: ImageGridWidget(
                images: testImages.take(9).toList(),
                enableCustomLayout: true,
                itemsPerRow: 3,
                itemWidth: 80.w,
                itemHeight: 60.h,
                spacing: 8.0,
                onImageTap: (index) => _showMessage('点击了第${index + 1}张图片'),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例6：单行显示
            _buildExampleSection(
              title: '示例6：单行显示',
              description: '每行5个图片，小尺寸',
              child: ImageGridWidget(
                images: testImages.take(5).toList(),
                enableCustomLayout: true,
                itemsPerRow: 5,
                spacing: 5.0,
                onImageTap: (index) => _showMessage('点击了第${index + 1}张图片'),
              ),
            ),

            SizedBox(height: 24.h),

            // 示例7：默认模式自定义宽高
            _buildExampleSection(
              title: '示例7：默认模式自定义宽高',
              description: '保持4个图片+遮罩，但使用自定义尺寸',
              child: ImageGridWidget(
                images: testImages,
                itemWidth: 70.w,
                itemHeight: 70.h,
                spacing: 8.0,
                onImageTap: (index) => _showMessage('点击了第${index + 1}张图片'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建示例区域
  Widget _buildExampleSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            description,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }

  /// 显示消息
  void _showMessage(String message) {
    Get.snackbar(
      '图片点击',
      message,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.r,
    );
  }
}

void main() {
  runApp(const ImageGridWidgetExample());
}
