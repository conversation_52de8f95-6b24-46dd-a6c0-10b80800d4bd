import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/record_type.dart';
import 'package:flutter_kexue/page/common_uistate/daily_record_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/record_uistate.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/view/DiaryRecordListView.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 记录列表图片更新示例
class DiaryRecordImageUpdateExample extends StatefulWidget {
  const DiaryRecordImageUpdateExample({super.key});

  @override
  State<DiaryRecordImageUpdateExample> createState() => _DiaryRecordImageUpdateExampleState();
}

class _DiaryRecordImageUpdateExampleState extends State<DiaryRecordImageUpdateExample> {
  late List<DailyRecordUIState> _recordList;

  @override
  void initState() {
    super.initState();
    _initializeRecordList();
  }

  /// 初始化记录列表
  void _initializeRecordList() {
    _recordList = [
      DailyRecordUIState(
        title: '今日记录',
        progress: 0.6,
        isExpand: true,
        list: [
          RecordUIState(
            type: RecordType.photo,
            title: '拍照打卡',
            system: false,
            content: '记录美好时刻',
            icon: '📷',
            image: null, // 初始没有图片
          ),
          RecordUIState(
            type: RecordType.diary,
            title: '日记记录',
            system: false,
            content: '今天心情不错',
            icon: '📝',
          ),
          RecordUIState(
            type: RecordType.photo,
            title: '运动打卡',
            system: false,
            content: '健身房锻炼',
            icon: '💪',
            image: null, // 初始没有图片
          ),
        ],
      ),
      DailyRecordUIState(
        title: '昨日记录',
        progress: 0.8,
        isExpand: false,
        list: [
          RecordUIState(
            type: RecordType.photo,
            title: '美食分享',
            system: false,
            content: '今天的午餐',
            icon: '🍽️',
            image: '/path/to/existing/image.jpg', // 已有图片
          ),
          RecordUIState(
            type: RecordType.diary,
            title: '学习笔记',
            system: false,
            content: '学习了Flutter开发',
            icon: '📚',
          ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('记录列表图片更新示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: Column(
        children: [
          // 说明区域
          Container(
            width: double.infinity,
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '功能说明：',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '• 点击相机图标选择图片\n• 选择后自动更新记录列表\n• 模拟上传到后台服务器\n• 支持重新选择图片',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.blue[700],
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),

          // 记录列表
          Expanded(
            child: DiaryRecordListView(
              recordList: _recordList,
              onImageUpdated: _onImageUpdated,
              onImageUpload: _onImageUpload,
            ),
          ),

          // 底部操作区域
          Container(
            padding: EdgeInsets.all(16.w),
            color: Colors.white,
            child: Column(
              children: [
                Text(
                  '操作记录：',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _resetAllImages,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('重置所有图片'),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _showRecordStatus,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('查看状态'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 图片更新回调
  void _onImageUpdated(int groupIndex, int itemIndex, String imagePath) {
    setState(() {
      // 更新对应位置的图片路径
      if (groupIndex < _recordList.length &&
          itemIndex < _recordList[groupIndex].list.length) {

        final oldRecord = _recordList[groupIndex].list[itemIndex];
        // 使用copyWith方法创建新的记录实例
        final updatedRecord = oldRecord.copyWith(image: imagePath);

        // 创建新的记录列表
        final updatedList = List<RecordUIState>.from(_recordList[groupIndex].list);
        updatedList[itemIndex] = updatedRecord;

        // 创建新的分组
        final updatedGroup = DailyRecordUIState(
          title: _recordList[groupIndex].title,
          progress: _recordList[groupIndex].progress,
          isExpand: _recordList[groupIndex].isExpand,
          list: updatedList,
        );

        // 更新记录列表
        final newRecordList = List<DailyRecordUIState>.from(_recordList);
        newRecordList[groupIndex] = updatedGroup;
        _recordList = newRecordList;
      }
    });

    print('图片已更新: 分组$groupIndex, 项目$itemIndex, 路径: $imagePath');
  }

  /// 图片上传回调
  void _onImageUpload(String imagePath) {
    print('开始上传图片: $imagePath');
    
    // 这里可以实现实际的上传逻辑
    // 例如：
    // uploadImageToServer(imagePath).then((serverUrl) {
    //   print('图片上传成功，服务器URL: $serverUrl');
    // }).catchError((error) {
    //   print('图片上传失败: $error');
    // });
  }

  /// 重置所有图片
  void _resetAllImages() {
    setState(() {
      _recordList = _recordList.map((group) {
        final updatedList = group.list.map((record) {
          if (record.type == RecordType.photo) {
            return record.copyWith(image: null); // 使用copyWith重置图片
          }
          return record;
        }).toList();
        
        return DailyRecordUIState(
          title: group.title,
          progress: group.progress,
          isExpand: group.isExpand,
          list: updatedList,
        );
      }).toList();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('所有图片已重置'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// 显示记录状态
  void _showRecordStatus() {
    int totalRecords = 0;
    int recordsWithImages = 0;
    
    for (final group in _recordList) {
      for (final record in group.list) {
        if (record.type == RecordType.photo) {
          totalRecords++;
          if (record.image != null) {
            recordsWithImages++;
          }
        }
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('记录状态'),
        content: Text(
          '拍照记录总数: $totalRecords\n'
          '已有图片记录: $recordsWithImages\n'
          '待拍照记录: ${totalRecords - recordsWithImages}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: const DiaryRecordImageUpdateExample(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
