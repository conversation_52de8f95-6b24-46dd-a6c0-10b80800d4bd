import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/activity_type.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 活动类型枚举使用示例
class ActivityTypeUsageExample extends StatefulWidget {
  const ActivityTypeUsageExample({super.key});

  @override
  State<ActivityTypeUsageExample> createState() => _ActivityTypeUsageExampleState();
}

class _ActivityTypeUsageExampleState extends State<ActivityTypeUsageExample> {
  ActivityType? selectedActivityType;
  RecordType? selectedRecordType;
  CheckInType? selectedCheckInType;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('活动类型枚举使用示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 活动类型选择
            _buildSectionTitle('主要活动类型'),
            _buildActivityTypeSection(),
            
            SizedBox(height: 24.h),
            
            // 记录类型选择
            _buildSectionTitle('记录类型'),
            _buildRecordTypeSection(),
            
            SizedBox(height: 24.h),
            
            // 打卡类型选择
            _buildSectionTitle('打卡类型'),
            _buildCheckInTypeSection(),
            
            SizedBox(height: 24.h),
            
            // 选择结果显示
            _buildResultSection(),
            
            SizedBox(height: 24.h),
            
            // 工具方法演示
            _buildUtilsSection(),
          ],
        ),
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  /// 构建活动类型选择区域
  Widget _buildActivityTypeSection() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: ActivityTypeUtils.getAllActivityTypes().map((type) {
          return RadioListTile<ActivityType>(
            title: Text(type.displayName),
            subtitle: Text('标识: ${type.identifier}'),
            value: type,
            groupValue: selectedActivityType,
            onChanged: (value) {
              setState(() {
                selectedActivityType = value;
              });
            },
          );
        }).toList(),
      ),
    );
  }

  /// 构建记录类型选择区域
  Widget _buildRecordTypeSection() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: ActivityTypeUtils.getAllRecordTypes().map((type) {
          return CheckboxListTile(
            title: Text(type.displayName),
            subtitle: Text('图标: ${type.iconName} | 标识: ${type.identifier}'),
            value: selectedRecordType == type,
            onChanged: (checked) {
              setState(() {
                selectedRecordType = checked == true ? type : null;
              });
            },
          );
        }).toList(),
      ),
    );
  }

  /// 构建打卡类型选择区域
  Widget _buildCheckInTypeSection() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: ActivityTypeUtils.getAllCheckInTypes().map((type) {
          return ListTile(
            title: Text(type.displayName),
            subtitle: Text('颜色: ${type.colorName} | 标识: ${type.identifier}'),
            leading: Container(
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                color: type.isPositive ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            trailing: selectedCheckInType == type 
                ? const Icon(Icons.check, color: Colors.blue)
                : null,
            onTap: () {
              setState(() {
                selectedCheckInType = selectedCheckInType == type ? null : type;
              });
            },
          );
        }).toList(),
      ),
    );
  }

  /// 构建选择结果显示区域
  Widget _buildResultSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '当前选择结果：',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),
          SizedBox(height: 8.h),
          Text('活动类型: ${selectedActivityType?.displayName ?? '未选择'}'),
          Text('记录类型: ${selectedRecordType?.displayName ?? '未选择'}'),
          Text('打卡类型: ${selectedCheckInType?.displayName ?? '未选择'}'),
          SizedBox(height: 8.h),
          if (selectedActivityType != null) ...[
            Text('活动类型判断:'),
            Text('  - 是记录类型: ${selectedActivityType!.isRecord}'),
            Text('  - 是打卡类型: ${selectedActivityType!.isCheckIn}'),
          ],
          if (selectedRecordType != null) ...[
            Text('记录类型判断:'),
            Text('  - 是拍照: ${selectedRecordType!.isPhoto}'),
            Text('  - 是日记: ${selectedRecordType!.isDiary}'),
            Text('  - 是多选: ${selectedRecordType!.isMultiple}'),
          ],
          if (selectedCheckInType != null) ...[
            Text('打卡类型判断:'),
            Text('  - 是正能量: ${selectedCheckInType!.isPositive}'),
            Text('  - 是持戒: ${selectedCheckInType!.isNegative}'),
          ],
        ],
      ),
    );
  }

  /// 构建工具方法演示区域
  Widget _buildUtilsSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '工具方法演示：',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.green[800],
            ),
          ),
          SizedBox(height: 8.h),
          Text('字符串转换测试:'),
          Text('  - "record" → ${ActivityTypeUtils.getActivityTypeFromString("record")?.displayName}'),
          Text('  - "photo" → ${ActivityTypeUtils.getRecordTypeFromString("photo")?.displayName}'),
          Text('  - "positive" → ${ActivityTypeUtils.getCheckInTypeFromString("positive")?.displayName}'),
          SizedBox(height: 8.h),
          Text('枚举数量统计:'),
          Text('  - 活动类型总数: ${ActivityTypeUtils.getAllActivityTypes().length}'),
          Text('  - 记录类型总数: ${ActivityTypeUtils.getAllRecordTypes().length}'),
          Text('  - 打卡类型总数: ${ActivityTypeUtils.getAllCheckInTypes().length}'),
        ],
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: const ActivityTypeUsageExample(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
