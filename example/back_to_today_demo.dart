import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';

/// 回到今天功能演示
class BackToTodayDemo extends StatefulWidget {
  const BackToTodayDemo({super.key});

  @override
  State<BackToTodayDemo> createState() => _BackToTodayDemoState();
}

class _BackToTodayDemoState extends State<BackToTodayDemo> {
  DateTime _currentDate = DateTime(2024, 1, 1); // 初始设置为2024年1月
  DateTime? _selectedDate;

  // 模拟有数据的日期
  final Set<DateTime> _datesWithData = {
    DateTime.now().subtract(const Duration(days: 2)),
    DateTime.now().subtract(const Duration(days: 1)),
    DateTime.now(),
    DateTime.now().add(const Duration(days: 1)),
    DateTime.now().add(const Duration(days: 2)),
  };

  @override
  Widget build(BuildContext context) {
    final today = DateTime.now();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('条件显示回今天按钮演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          // 模拟原页面的条件显示"回今天"按钮
          Builder(
            builder: (context) {
              final today = DateTime.now();
              final isCurrentMonth = _currentDate.year == today.year && _currentDate.month == today.month;

              // 只在非当月时显示按钮
              if (!isCurrentMonth) {
                return GestureDetector(
                  onTap: _handleBackToToday,
                  child: Container(
                    margin: const EdgeInsets.only(right: 16),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Text(
                      "回今天",
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              }

              // 当月时显示占位空间或其他内容
              return Container(
                margin: const EdgeInsets.only(right: 16),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 24,
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息卡片
            _buildStatusCard(today),
            
            const SizedBox(height: 20),
            
            // 操作说明
            _buildInstructionCard(),
            
            const SizedBox(height: 20),
            
            // 日历组件
            _buildCalendarCard(),
            
            const SizedBox(height: 20),
            
            // 测试按钮
            _buildTestButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(DateTime today) {
    final isCurrentMonth = _isCurrentMonth(today);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('今天: ${today.year}年${today.month}月${today.day}日'),
            Text('当前显示月份: ${_currentDate.year}年${_currentDate.month}月'),
            Text('选中日期: ${_selectedDate != null ? "${_selectedDate!.year}年${_selectedDate!.month}月${_selectedDate!.day}日" : "未选中"}'),
            const SizedBox(height: 12),

            // 显示是否为当月
            Row(
              children: [
                Icon(
                  isCurrentMonth ? Icons.check_circle : Icons.info,
                  color: isCurrentMonth ? Colors.green : Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isCurrentMonth ? '当前显示的是本月' : '当前显示的不是本月',
                  style: TextStyle(
                    color: isCurrentMonth ? Colors.green : Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // 显示按钮状态
            Row(
              children: [
                Icon(
                  isCurrentMonth ? Icons.visibility_off : Icons.visibility,
                  color: isCurrentMonth ? Colors.grey : Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isCurrentMonth ? '"回今天"按钮已隐藏' : '"回今天"按钮显示中',
                  style: TextStyle(
                    color: isCurrentMonth ? Colors.grey : Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionCard() {
    return Card(
      color: Colors.blue.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '使用说明',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text('1. 观察右上角"回今天"按钮的显示状态'),
            const Text('2. 当显示当前月份时，按钮会自动隐藏'),
            const Text('3. 当显示其他月份时，按钮会自动显示'),
            const Text('4. 点击"回今天"按钮会切换到当前月份'),
            const Text('5. 切换到当前月份后按钮会自动隐藏'),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '日历组件',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TDCalendar(
              selectedDate: _selectedDate,
              onMonthChanged: (monthFirstDay) {
                setState(() {
                  _currentDate = monthFirstDay;
                });
              },
              onDateTap: (date) {
                setState(() {
                  _selectedDate = date;
                });
                
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('选中了: ${date.year}年${date.month}月${date.day}日'),
                    duration: const Duration(seconds: 1),
                  ),
                );
              },
              datesWithData: _datesWithData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '测试按钮',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _currentDate = DateTime(2024, 1, 1);
                        _selectedDate = null;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('切换到1月'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _currentDate = DateTime(2024, 6, 1);
                        _selectedDate = null;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('切换到6月'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _handleBackToToday,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('🏠 回到今天 (测试按钮)'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理"回到今天"按钮点击事件
  void _handleBackToToday() {
    final today = DateTime.now();
    
    setState(() {
      // 更新当前年月到今天
      _currentDate = DateTime(today.year, today.month, 1);
      
      // 选中今天的日期
      _selectedDate = today;
    });
    
    // 显示提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已回到今天: ${today.year}年${today.month}月${today.day}日'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 检查当前显示的是否为本月
  bool _isCurrentMonth(DateTime today) {
    return _currentDate.year == today.year && _currentDate.month == today.month;
  }
}
