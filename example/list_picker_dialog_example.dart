import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/dialog/list_picker_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 列表选择弹窗示例
class ListPickerDialogExample extends StatelessWidget {
  const ListPickerDialogExample({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'ListPickerDialog Example',
      home: const ListPickerDialogExampleHome(),
      builder: (context, child) {
        ScreenUtil.init(context, designSize: const Size(375, 812));
        return child!;
      },
    );
  }
}

class ListPickerDialogExampleHome extends StatefulWidget {
  const ListPickerDialogExampleHome({super.key});

  @override
  State<ListPickerDialogExampleHome> createState() => _ListPickerDialogExampleHomeState();
}

class _ListPickerDialogExampleHomeState extends State<ListPickerDialogExampleHome> {
  String selectedResult = '未选择';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('列表选择弹窗示例'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明文字
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ListPickerDialog 功能：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '• 通用列表选择弹窗组件\n'
                    '• 支持自定义文案和颜色\n'
                    '• 支持任意数量的选项\n'
                    '• 点击事件回调到外部处理\n'
                    '• 按照ImagePickerDialog样式设计',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 选择结果显示
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    '选择结果：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      selectedResult,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.blue[600],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 示例按钮
            _buildExampleButton(
              title: '示例1：日记操作（图中样式）',
              description: '全部日记、删除日记、取消',
              onTap: () => _showDiaryActionDialog(),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              title: '示例2：基础选择',
              description: '选项A、选项B、选项C',
              onTap: () => _showBasicDialog(),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              title: '示例3：多彩选择',
              description: '不同颜色的选项',
              onTap: () => _showColorfulDialog(),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              title: '示例4：无取消按钮',
              description: '隐藏取消按钮',
              onTap: () => _showNoCancelDialog(),
            ),

            SizedBox(height: 16.h),

            _buildExampleButton(
              title: '示例5：5个选项',
              description: '演示更多选项',
              onTap: () => _showMoreOptionsDialog(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建示例按钮
  Widget _buildExampleButton({
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.sp,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 显示日记操作弹窗（图中样式）
  void _showDiaryActionDialog() {
    final items = [
      const ListPickerItem(
        text: '全部日记',
        data: 'all_diary',
      ),
      const ListPickerItem(
        text: '删除日记',
        textColor: Colors.red,
        data: 'delete_diary',
      ),
    ];

    context.showListPicker(
      items: items,
      onItemSelected: (index, item) {
        setState(() {
          selectedResult = '${item.text} (${item.data})';
        });
        _showMessage('选择了：${item.text}');
      },
    );
  }

  /// 显示基础选择弹窗
  void _showBasicDialog() {
    final items = [
      const ListPickerItem(text: '选项A', data: 'option_a'),
      const ListPickerItem(text: '选项B', data: 'option_b'),
      const ListPickerItem(text: '选项C', data: 'option_c'),
    ];

    ListPickerDialog.show(
      context: context,
      items: items,
      onItemSelected: (index, item) {
        setState(() {
          selectedResult = '${item.text} (索引: $index)';
        });
        _showMessage('选择了第${index + 1}项：${item.text}');
      },
    );
  }

  /// 显示多彩选择弹窗
  void _showColorfulDialog() {
    final items = [
      const ListPickerItem(
        text: '成功操作',
        textColor: Colors.green,
        data: 'success',
      ),
      const ListPickerItem(
        text: '警告操作',
        textColor: Colors.orange,
        data: 'warning',
      ),
      const ListPickerItem(
        text: '危险操作',
        textColor: Colors.red,
        data: 'danger',
      ),
      const ListPickerItem(
        text: '普通操作',
        data: 'normal',
      ),
    ];

    context.showListPicker(
      items: items,
      onItemSelected: (index, item) {
        setState(() {
          selectedResult = '${item.text} (${item.data})';
        });
        _showMessage('执行${item.text}');
      },
    );
  }

  /// 显示无取消按钮弹窗
  void _showNoCancelDialog() {
    final items = [
      const ListPickerItem(text: '必选项1', data: 1),
      const ListPickerItem(text: '必选项2', data: 2),
    ];

    context.showListPicker(
      items: items,
      showCancel: false,
      onItemSelected: (index, item) {
        setState(() {
          selectedResult = '${item.text} (${item.data})';
        });
        _showMessage('选择了：${item.text}');
      },
    );
  }

  /// 显示更多选项弹窗
  void _showMoreOptionsDialog() {
    final items = [
      const ListPickerItem(text: '第一个选项', data: 1),
      const ListPickerItem(text: '第二个选项', data: 2),
      const ListPickerItem(text: '第三个选项', data: 3),
      const ListPickerItem(text: '第四个选项', data: 4),
      const ListPickerItem(
        text: '第五个选项（红色）',
        textColor: Colors.red,
        data: 5,
      ),
    ];

    context.showListPicker(
      items: items,
      cancelText: '不选择',
      cancelTextColor: Colors.grey[500],
      onItemSelected: (index, item) {
        setState(() {
          selectedResult = '${item.text} (数据: ${item.data})';
        });
        _showMessage('选择了第${index + 1}个选项');
      },
    );
  }

  /// 显示消息
  void _showMessage(String message) {
    Get.snackbar(
      '操作结果',
      message,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      margin: EdgeInsets.all(16.w),
      borderRadius: 8.r,
    );
  }
}

void main() {
  runApp(const ListPickerDialogExample());
}
