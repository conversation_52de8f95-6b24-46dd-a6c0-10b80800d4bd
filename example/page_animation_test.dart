import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 页面切换动画测试
class PageAnimationTest extends StatefulWidget {
  const PageAnimationTest({super.key});

  @override
  State<PageAnimationTest> createState() => _PageAnimationTestState();
}

class _PageAnimationTestState extends State<PageAnimationTest> {
  DateTime _selectedDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('页面切换动画测试'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[100],
      body: Column(
        children: [
          // 控制按钮区域
          Container(
            padding: EdgeInsets.all(16.w),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '测试页面切换动画效果：',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 12.h),
                
                // 快速切换按钮
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: [
                    _buildQuickButton('上个月', () {
                      setState(() {
                        _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1, 1);
                      });
                    }),
                    _buildQuickButton('下个月', () {
                      setState(() {
                        _selectedDate = DateTime(_selectedDate.year, _selectedDate.month + 1, 1);
                      });
                    }),
                    _buildQuickButton('今年1月', () {
                      setState(() {
                        _selectedDate = DateTime(_selectedDate.year, 1, 1);
                      });
                    }),
                    _buildQuickButton('今年12月', () {
                      setState(() {
                        _selectedDate = DateTime(_selectedDate.year, 12, 1);
                      });
                    }),
                    _buildQuickButton('去年同月', () {
                      setState(() {
                        _selectedDate = DateTime(_selectedDate.year - 1, _selectedDate.month, 1);
                      });
                    }),
                    _buildQuickButton('明年同月', () {
                      setState(() {
                        _selectedDate = DateTime(_selectedDate.year + 1, _selectedDate.month, 1);
                      });
                    }),
                  ],
                ),
                
                SizedBox(height: 12.h),
                Text(
                  '当前选中：${_selectedDate.year}年${_selectedDate.month}月',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          // 日历区域
          Expanded(
            child: Container(
              margin: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: TDCalendar(
                  selectedDate: _selectedDate,
                  onMonthChanged: (monthFirstDay) {
                    print('月份切换到: ${monthFirstDay.year}年${monthFirstDay.month}月');
                  },
                  onDateTap: (date) {
                    setState(() {
                      _selectedDate = date;
                    });
                    print('选中日期: ${date.year}年${date.month}月${date.day}日');
                  },
                ),
              ),
            ),
          ),
          
          // 说明文字
          Container(
            padding: EdgeInsets.all(16.w),
            child: Text(
              '• 点击上方按钮测试页面切换动画\n• 观察月份切换时的平滑动画效果\n• 高度变化也会有动画过渡',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快速切换按钮
  Widget _buildQuickButton(String text, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        minimumSize: Size(0, 32.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.r),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.sp),
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: const PageAnimationTest(),
    builder: (context, child) {
      ScreenUtil.init(context, designSize: const Size(375, 812));
      return child!;
    },
  ));
}
