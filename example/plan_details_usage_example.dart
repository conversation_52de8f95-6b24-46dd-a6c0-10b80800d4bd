import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_details/plan_details_page.dart';
import 'package:flutter_kexue/page/page/plan/plan_details/entity/plan_details_props.dart';

/// 计划详情页面使用示例
class PlanDetailsUsageExample extends StatelessWidget {
  const PlanDetailsUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('计划详情页面示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _navigateToOwnPlan(context),
              child: const Text('查看自己的计划'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _navigateToOthersPlan(context),
              child: const Text('查看他人的计划'),
            ),
            const SizedBox(height: 40),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '功能说明：',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• 简介模块：可展开/收起的计划介绍'),
                  Text('• 右上角按钮：收藏和点赞功能'),
                  Text('• 详情模块：显示计划的具体内容'),
                  Text('• 底部按钮：自己的计划显示"分享"，他人的显示"使用此计划"'),
                  Text('• 响应式设计：支持数据动态更新'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 跳转到自己的计划详情
  void _navigateToOwnPlan(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlanDetailsPage(
          props: const PlanDetailsProps(
            planId: '1',
            isOwnPlan: true,
          ),
        ),
      ),
    );
  }

  /// 跳转到他人的计划详情
  void _navigateToOthersPlan(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlanDetailsPage(
          props: const PlanDetailsProps(
            planId: '2',
            isOwnPlan: false,
          ),
        ),
      ),
    );
  }
}

/// 在应用中使用示例
void main() {
  runApp(MaterialApp(
    home: const PlanDetailsUsageExample(),
    theme: ThemeData(
      primarySwatch: Colors.green,
    ),
  ));
}

/// 使用说明：
/// 
/// 1. 页面结构：
///    - 顶部：计划标题 + 作者信息 + 收藏/点赞按钮
///    - 简介模块：可展开/收起的计划介绍
///    - 详情模块：计划的具体内容列表
///    - 底部：悬浮操作按钮
/// 
/// 2. 交互功能：
///    - 点击"查看更多/收起"：展开/收起简介内容
///    - 点击收藏按钮：切换收藏状态，数量会相应变化
///    - 点击点赞按钮：切换点赞状态，数量会相应变化
///    - 点击底部按钮：分享计划或使用计划
/// 
/// 3. 自适应显示：
///    - 自己的计划：底部显示"分享"按钮
///    - 他人的计划：底部显示"使用此计划"按钮
///    - 收藏/点赞状态会实时更新
/// 
/// 4. 数据结构：
///    ```dart
///    PlanDetailsProps(
///      planId: '1',
///      isOwnPlan: true, // true=自己的计划，false=他人的计划
///    )
///    ```
