import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/dialog/check_in_suc_dialog.dart';

/// 打卡成功对话框使用示例
class CheckInDialogExample extends StatelessWidget {
  const CheckInDialogExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('打卡成功对话框示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _showCheckInDialog(context),
              child: const Text('显示打卡成功对话框'),
            ),
            const SizedBox(height: 20),
            const Text(
              '点击按钮查看对话框效果',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示打卡成功对话框
  void _showCheckInDialog(BuildContext context) {
    CheckInSuccessDialog.show(
      context: context,
      dateTime: DateTime.now(),
      onConfirm: () {
        print('用户确认了打卡');
        // 这里可以处理确认后的逻辑
        // 比如：保存数据、跳转页面等
      },
    );
  }
}
