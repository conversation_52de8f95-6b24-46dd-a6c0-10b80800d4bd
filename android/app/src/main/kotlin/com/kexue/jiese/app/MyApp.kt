package com.kexue.jiese.app

import io.flutter.app.FlutterApplication
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor.DartEntrypoint

class MyApp : FlutterApplication() {
    override fun onCreate() {
        println("HTApp onCreate")
        super.onCreate()

        // 初始化 BackgroundFlutterEngine
        val flutterEngine = FlutterEngine(this)
        flutterEngine.dartExecutor.executeDartEntrypoint(
            DartEntrypoint.createDefault()
        )

        // 将 FlutterEngine 缓存起来
        FlutterEngineCache.getInstance().put("my_engine_id", flutterEngine)



    }
}