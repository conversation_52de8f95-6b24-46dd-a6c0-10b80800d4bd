import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'entity/{NAME}[-s]_props.dart';
import 'vm/{NAME}[-s]_viewmodel.dart';

/// @date {year}/{month}/{day}
/// @param props 页面路由参数
/// @returns
/// @description {NAME}[-C]页面入口
class {NAME}[-C]Page extends StatelessWidget {
  {NAME}[-C]Page({super.key, this.props});

  final {NAME}[-C]Props? props;
  final {NAME}[-C]ViewModel viewModel = {NAME}[-C]ViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("{NAME}[-C]"), centerTitle: true),
      body: contentView(),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handle{NAME}[-C]VMEvent(props.vm)
    return Center(
      child: Text(""),
    );
  }
}
