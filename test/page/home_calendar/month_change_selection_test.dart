import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Month Change Selection Logic Tests', () {
    test('should select today when switching to current month', () {
      final today = DateTime.now();
      
      // 模拟切换到当前月份的逻辑
      final isCurrentMonth = today.year == today.year && today.month == today.month;
      expect(isCurrentMonth, isTrue);
      
      DateTime selectedDate;
      if (isCurrentMonth) {
        selectedDate = today;
      } else {
        selectedDate = DateTime(today.year, today.month, 1);
      }
      
      // 验证选中的是今天
      expect(selectedDate.year, equals(today.year));
      expect(selectedDate.month, equals(today.month));
      expect(selectedDate.day, equals(today.day));
    });

    test('should select first day when switching to different month', () {
      final today = DateTime.now();
      
      // 模拟切换到不同月份（下个月）
      final nextMonth = today.month == 12 ? 1 : today.month + 1;
      final nextYear = today.month == 12 ? today.year + 1 : today.year;
      
      final isCurrentMonth = nextYear == today.year && nextMonth == today.month;
      expect(isCurrentMonth, isFalse);
      
      DateTime selectedDate;
      if (isCurrentMonth) {
        selectedDate = today;
      } else {
        selectedDate = DateTime(nextYear, nextMonth, 1);
      }
      
      // 验证选中的是第一天
      expect(selectedDate.year, equals(nextYear));
      expect(selectedDate.month, equals(nextMonth));
      expect(selectedDate.day, equals(1));
    });

    test('should select first day when switching to previous month', () {
      final today = DateTime.now();
      
      // 模拟切换到不同月份（上个月）
      final prevMonth = today.month == 1 ? 12 : today.month - 1;
      final prevYear = today.month == 1 ? today.year - 1 : today.year;
      
      final isCurrentMonth = prevYear == today.year && prevMonth == today.month;
      expect(isCurrentMonth, isFalse);
      
      DateTime selectedDate;
      if (isCurrentMonth) {
        selectedDate = today;
      } else {
        selectedDate = DateTime(prevYear, prevMonth, 1);
      }
      
      // 验证选中的是第一天
      expect(selectedDate.year, equals(prevYear));
      expect(selectedDate.month, equals(prevMonth));
      expect(selectedDate.day, equals(1));
    });

    test('should handle year boundary correctly', () {
      final today = DateTime.now();
      
      // 测试跨年的情况
      final differentYear = today.year - 1;
      final sameMonth = today.month;
      
      final isCurrentMonth = differentYear == today.year && sameMonth == today.month;
      expect(isCurrentMonth, isFalse);
      
      DateTime selectedDate;
      if (isCurrentMonth) {
        selectedDate = today;
      } else {
        selectedDate = DateTime(differentYear, sameMonth, 1);
      }
      
      // 验证选中的是第一天（因为年份不同）
      expect(selectedDate.year, equals(differentYear));
      expect(selectedDate.month, equals(sameMonth));
      expect(selectedDate.day, equals(1));
    });

    test('should format date string correctly', () {
      final testDate = DateTime(2024, 3, 15);
      final expectedString = "${testDate.year}年${testDate.month}月${testDate.day}日";
      
      expect(expectedString, equals("2024年3月15日"));
    });

    test('should calculate weekday correctly', () {
      final testDate = DateTime(2024, 3, 15); // 这是一个周五
      final weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      final weekday = weekdays[testDate.weekday % 7];
      
      expect(weekday, equals('周五'));
    });

    test('should handle first day of month weekday calculation', () {
      final firstDay = DateTime(2024, 3, 1); // 2024年3月1日
      final weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      final weekday = weekdays[firstDay.weekday % 7];
      
      // 验证星期计算正确
      expect(weekdays, contains(weekday));
      expect(weekday, startsWith('周'));
    });
  });

  group('Edge Cases Tests', () {
    test('should handle December to January transition', () {
      final december = DateTime(2024, 12, 15);
      final today = DateTime.now();
      
      // 如果今天不是2024年12月，那么12月不是当前月
      final isCurrentMonth = december.year == today.year && december.month == today.month;
      
      DateTime selectedDate;
      if (isCurrentMonth) {
        selectedDate = today;
      } else {
        selectedDate = DateTime(december.year, december.month, 1);
      }
      
      if (!isCurrentMonth) {
        expect(selectedDate.day, equals(1));
        expect(selectedDate.month, equals(12));
        expect(selectedDate.year, equals(2024));
      }
    });

    test('should handle leap year February', () {
      final leapYearFeb = DateTime(2024, 2, 1); // 2024是闰年
      final today = DateTime.now();
      
      final isCurrentMonth = leapYearFeb.year == today.year && leapYearFeb.month == today.month;
      
      DateTime selectedDate;
      if (isCurrentMonth) {
        selectedDate = today;
      } else {
        selectedDate = DateTime(leapYearFeb.year, leapYearFeb.month, 1);
      }
      
      if (!isCurrentMonth) {
        expect(selectedDate.day, equals(1));
        expect(selectedDate.month, equals(2));
        expect(selectedDate.year, equals(2024));
      }
    });
  });
}
