import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/home_calendar_page.dart';
import 'package:get/get.dart';

void main() {
  group('Back to Today Button Tests', () {
    setUp(() {
      // 初始化GetX
      Get.testMode = true;
    });

    tearDown(() {
      // 清理GetX
      Get.reset();
    });

    testWidgets('should show back to today button in app bar', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: HomeCalendarPage(),
        ),
      );

      // 等待组件渲染完成
      await tester.pumpAndSettle();

      // 验证"回今天"按钮存在
      expect(find.text('回今天'), findsOneWidget);
    });

    testWidgets('should handle back to today button tap', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: HomeCalendarPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找并点击"回今天"按钮
      final backToTodayButton = find.text('回今天');
      expect(backToTodayButton, findsOneWidget);

      await tester.tap(backToTodayButton);
      await tester.pumpAndSettle();

      // 验证按钮点击后没有异常
      expect(find.text('回今天'), findsOneWidget);
    });

    testWidgets('should show back to today button with proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: HomeCalendarPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 验证按钮存在并且有正确的样式
      final backToTodayButton = find.text('回今天');
      expect(backToTodayButton, findsOneWidget);

      // 验证按钮是可点击的（被GestureDetector包装）
      final gestureDetector = find.ancestor(
        of: backToTodayButton,
        matching: find.byType(GestureDetector),
      );
      expect(gestureDetector, findsOneWidget);
    });
  });

  group('Back to Today Functionality Tests', () {
    test('should calculate today correctly', () {
      final today = DateTime.now();
      
      // 验证今天的基本信息
      expect(today.year, greaterThan(2020));
      expect(today.month, inInclusiveRange(1, 12));
      expect(today.day, inInclusiveRange(1, 31));
    });

    test('should format today date string correctly', () {
      final today = DateTime.now();
      final expectedDateString = "${today.year}年${today.month}月${today.day}日";
      
      // 验证日期字符串格式
      expect(expectedDateString, contains('年'));
      expect(expectedDateString, contains('月'));
      expect(expectedDateString, contains('日'));
    });

    test('should calculate weekday correctly', () {
      final today = DateTime.now();
      final weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      final expectedWeekday = weekdays[today.weekday % 7];
      
      // 验证星期计算
      expect(weekdays, contains(expectedWeekday));
      expect(expectedWeekday, startsWith('周'));
    });
  });

  group('UI State Update Tests', () {
    testWidgets('should update calendar when back to today is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: HomeCalendarPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击"回今天"按钮
      await tester.tap(find.text('回今天'));
      await tester.pumpAndSettle();

      // 验证页面没有错误
      expect(tester.takeException(), isNull);
    });
  });
}
