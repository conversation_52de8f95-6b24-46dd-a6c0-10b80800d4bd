import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_item.dart';

void main() {
  group('Selected Date Tests', () {
    testWidgets('should show selected date with background color', (WidgetTester tester) async {
      DateTime? tappedDate;
      final selectedDate = DateTime(2024, 3, 15);
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TDCalendar(
              selectedDate: selectedDate ?? DateTime(2024, 3, 1),
              onDateTap: (date) {
                tappedDate = date;
              },
            ),
          ),
        ),
      );

      // 等待组件渲染完成
      await tester.pumpAndSettle();

      // 验证日历组件存在
      expect(find.byType(TDCalendar), findsOneWidget);
      expect(find.byType(CalendarDayItem), findsWidgets);
    });

    testWidgets('should handle date selection change', (WidgetTester tester) async {
      DateTime? selectedDate;
      DateTime? tappedDate;
      
      await tester.pumpWidget(
        StatefulBuilder(
          builder: (context, setState) {
            return MaterialApp(
              home: Scaffold(
                body: TDCalendar(
                  selectedDate: selectedDate ?? DateTime(2024, 3, 1),
                  onDateTap: (date) {
                    setState(() {
                      selectedDate = date;
                      tappedDate = date;
                    });
                  },
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 查找并点击日期项
      final dayItems = find.byType(CalendarDayItem);
      expect(dayItems, findsWidgets);
      
      // 点击第一个可见的日期项
      await tester.tap(dayItems.first);
      await tester.pumpAndSettle();
      
      // 验证回调被调用
      expect(tappedDate, isNotNull);
    });

    testWidgets('should handle null selected date', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TDCalendar(
              selectedDate: DateTime(2024, 3, 1), // 默认选中第一天
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.byType(TDCalendar), findsOneWidget);
    });

    testWidgets('should show different background for selected vs today', (WidgetTester tester) async {
      final today = DateTime.now();
      final selectedDate = DateTime(today.year, today.month, today.day == 1 ? 2 : 1);
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TDCalendar(
              selectedDate: selectedDate,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.byType(TDCalendar), findsOneWidget);
    });
  });

  group('CalendarDayData Tests', () {
    test('should create calendar day data with selected state', () {
      final date = DateTime(2024, 3, 15);
      final dayData = CalendarDayData(
        date: date,
        isCurrentDay: true,
        isToday: false,
        hasData: false,
        isSelected: true,
      );

      expect(dayData.date, equals(date));
      expect(dayData.isCurrentDay, isTrue);
      expect(dayData.isToday, isFalse);
      expect(dayData.hasData, isFalse);
      expect(dayData.isSelected, isTrue);
    });

    test('should handle both selected and today states', () {
      final today = DateTime.now();
      final dayData = CalendarDayData(
        date: today,
        isCurrentDay: true,
        isToday: true,
        hasData: true,
        isSelected: true,
      );

      expect(dayData.isToday, isTrue);
      expect(dayData.isSelected, isTrue);
      expect(dayData.hasData, isTrue);
    });
  });
}
