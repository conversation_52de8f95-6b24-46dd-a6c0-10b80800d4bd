import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar_body.dart';

void main() {
  group('Calendar Adaptive Height Tests', () {
    testWidgets('should calculate correct height for different months', (WidgetTester tester) async {
      // 测试不同月份的高度计算
      
      // 2024年2月 - 通常需要4-5行
      final feb2024 = DateTime(2024, 2, 1);
      
      // 2024年3月 - 通常需要5-6行  
      final mar2024 = DateTime(2024, 3, 1);
      
      // 创建日历组件
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                TdCalendarBody(
                  selectedDate: feb2024,
                  onMonthChanged: (monthFirstDay) {},
                ),
              ],
            ),
          ),
        ),
      );

      // 等待初始动画完成
      await tester.pumpAndSettle();

      // 验证日历组件存在
      expect(find.byType(TdCalendarBody), findsOneWidget);
    });

    test('should calculate month rows correctly', () {
      // 测试月份行数计算逻辑
      
      // 2024年2月1日是周四，2月有29天（闰年）
      // 第一行：周日(28) 周一(29) 周二(30) 周三(31) 周四(1) 周五(2) 周六(3)
      // 第二行：周日(4) 周一(5) 周二(6) 周三(7) 周四(8) 周五(9) 周六(10)
      // 第三行：周日(11) 周一(12) 周二(13) 周三(14) 周四(15) 周五(16) 周六(17)
      // 第四行：周日(18) 周一(19) 周二(20) 周三(21) 周四(22) 周五(23) 周六(24)
      // 第五行：周日(25) 周一(26) 周二(27) 周三(28) 周四(29) 周五(1) 周六(2)
      final feb2024 = DateTime(2024, 2, 1);
      final feb2024Rows = _calculateMonthRows(feb2024);
      expect(feb2024Rows, equals(5));

      // 2024年3月1日是周五，3月有31天
      // 需要6行
      final mar2024 = DateTime(2024, 3, 1);
      final mar2024Rows = _calculateMonthRows(mar2024);
      expect(mar2024Rows, equals(6));

      // 2024年4月1日是周一，4月有30天
      // 需要5行
      final apr2024 = DateTime(2024, 4, 1);
      final apr2024Rows = _calculateMonthRows(apr2024);
      expect(apr2024Rows, equals(5));

      // 2024年5月1日是周三，5月有31天
      // 需要5行
      final may2024 = DateTime(2024, 5, 1);
      final may2024Rows = _calculateMonthRows(may2024);
      expect(may2024Rows, equals(5));
    });

    test('should calculate height correctly', () {
      const rowHeight = 40.0;
      
      // 2月需要5行
      final feb2024 = DateTime(2024, 2, 1);
      final feb2024Height = _calculateMonthRows(feb2024) * rowHeight;
      expect(feb2024Height, equals(200.0));

      // 3月需要6行
      final mar2024 = DateTime(2024, 3, 1);
      final mar2024Height = _calculateMonthRows(mar2024) * rowHeight;
      expect(mar2024Height, equals(240.0));
    });
  });
}

/// 计算月份需要的行数（与日历组件中的逻辑保持一致）
int _calculateMonthRows(DateTime monthDate) {
  // 获取月份第一天
  final firstDay = DateTime(monthDate.year, monthDate.month, 1);
  
  // 获取月份最后一天
  final lastDay = DateTime(monthDate.year, monthDate.month + 1, 0);
  
  // 获取第一天是星期几（0=周日，1=周一...6=周六）
  final firstWeekday = firstDay.weekday % 7;
  
  // 计算总天数（包括上个月填充的天数）
  final totalDays = firstWeekday + lastDay.day;
  
  // 计算需要的行数
  return (totalDays / 7).ceil();
}
