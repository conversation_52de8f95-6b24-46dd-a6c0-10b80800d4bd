import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';

void main() {
  group('Dynamic Height Calculation Tests', () {
    testWidgets('should calculate row height based on available width', (WidgetTester tester) async {
      // 测试不同宽度下的行高计算
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 350, // 设置固定宽度
              child: TDCalendar(
                selectedDate: DateTime(2024, 3, 1),
                padding: const EdgeInsets.symmetric(horizontal: 14),
              ),
            ),
          ),
        ),
      );

      // 等待组件渲染完成
      await tester.pumpAndSettle();

      // 验证日历组件存在
      expect(find.byType(TDCalendar), findsOneWidget);
    });

    testWidgets('should handle different padding values', (WidgetTester tester) async {
      // 测试不同padding值的处理
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 400,
              child: TDCalendar(
                selectedDate: DateTime(2024, 3, 1),
                padding: const EdgeInsets.symmetric(horizontal: 20),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.byType(TDCalendar), findsOneWidget);
    });

    testWidgets('should handle zero padding', (WidgetTester tester) async {
      // 测试零padding的处理
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 300,
              child: TDCalendar(
                selectedDate: DateTime(2024, 3, 1),
                padding: EdgeInsets.zero,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.byType(TDCalendar), findsOneWidget);
    });

    testWidgets('should adapt to different screen widths', (WidgetTester tester) async {
      // 测试不同屏幕宽度的适配
      
      // 小屏幕
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 250,
              child: TDCalendar(
                selectedDate: DateTime(2024, 3, 1),
                padding: const EdgeInsets.symmetric(horizontal: 10),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.byType(TDCalendar), findsOneWidget);

      // 大屏幕
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 500,
              child: TDCalendar(
                selectedDate: DateTime(2024, 3, 1),
                padding: const EdgeInsets.symmetric(horizontal: 30),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.byType(TDCalendar), findsOneWidget);
    });
  });

  group('Row Height Calculation Logic Tests', () {
    test('should calculate correct row height for different widths', () {
      // 测试行高计算逻辑
      
      // 宽度350，padding 14*2=28，可用宽度322，行高 = 322/7 ≈ 46
      const width1 = 350.0;
      const padding1 = EdgeInsets.symmetric(horizontal: 14);
      final availableWidth1 = width1 - padding1.left - padding1.right;
      final rowHeight1 = availableWidth1 / 7;
      expect(rowHeight1, closeTo(46.0, 0.1));

      // 宽度400，padding 20*2=40，可用宽度360，行高 = 360/7 ≈ 51.4
      const width2 = 400.0;
      const padding2 = EdgeInsets.symmetric(horizontal: 20);
      final availableWidth2 = width2 - padding2.left - padding2.right;
      final rowHeight2 = availableWidth2 / 7;
      expect(rowHeight2, closeTo(51.4, 0.1));

      // 宽度300，无padding，行高 = 300/7 ≈ 42.9
      const width3 = 300.0;
      const padding3 = EdgeInsets.zero;
      final availableWidth3 = width3 - padding3.left - padding3.right;
      final rowHeight3 = availableWidth3 / 7;
      expect(rowHeight3, closeTo(42.9, 0.1));
    });

    test('should calculate correct total height for different months', () {
      // 测试不同月份的总高度计算
      
      const rowHeight = 50.0;
      
      // 2024年2月需要5行
      final feb2024Rows = _calculateMonthRows(DateTime(2024, 2, 1));
      final feb2024Height = feb2024Rows * rowHeight;
      expect(feb2024Height, equals(250.0));

      // 2024年3月需要6行
      final mar2024Rows = _calculateMonthRows(DateTime(2024, 3, 1));
      final mar2024Height = mar2024Rows * rowHeight;
      expect(mar2024Height, equals(300.0));
    });
  });
}

/// 计算月份需要的行数（与日历组件中的逻辑保持一致）
int _calculateMonthRows(DateTime monthDate) {
  // 获取月份第一天
  final firstDay = DateTime(monthDate.year, monthDate.month, 1);
  
  // 获取月份最后一天
  final lastDay = DateTime(monthDate.year, monthDate.month + 1, 0);
  
  // 获取第一天是星期几（0=周日，1=周一...6=周六）
  final firstWeekday = firstDay.weekday % 7;
  
  // 计算总天数（包括上个月填充的天数）
  final totalDays = firstWeekday + lastDay.day;
  
  // 计算需要的行数
  return (totalDays / 7).ceil();
}
