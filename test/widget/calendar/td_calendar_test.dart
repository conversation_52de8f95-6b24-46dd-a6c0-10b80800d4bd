import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_item.dart';

void main() {
  group('TDCalendar Tests', () {
    testWidgets('should display calendar with current month', (WidgetTester tester) async {
      final currentDate = DateTime(2024, 3, 15);
      bool monthChanged = false;
      DateTime? tappedDate;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TDCalendar(
              selectedDate: currentDate,
              onMonthChanged: (monthFirstDay) {
                monthChanged = true;
              },
              onDateTap: (date) {
                tappedDate = date;
              },
            ),
          ),
        ),
      );

      // 验证日历组件存在
      expect(find.byType(TDCalendar), findsOneWidget);
      
      // 验证星期标题存在
      expect(find.text('日'), findsOneWidget);
      expect(find.text('一'), findsOneWidget);
      expect(find.text('六'), findsOneWidget);
    });

    testWidgets('should handle date tap', (WidgetTester tester) async {
      DateTime? tappedDate;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TDCalendar(
              selectedDate: DateTime(2024, 3, 15),
              onDateTap: (date) {
                tappedDate = date;
              },
            ),
          ),
        ),
      );

      // 查找并点击日期项
      final dayItems = find.byType(CalendarDayItem);
      expect(dayItems, findsWidgets);
      
      // 点击第一个日期项
      await tester.tap(dayItems.first);
      await tester.pump();
      
      // 验证回调被调用
      expect(tappedDate, isNotNull);
    });

    testWidgets('should show data indicators for dates with data', (WidgetTester tester) async {
      final datesWithData = <DateTime>{
        DateTime(2024, 3, 15),
        DateTime(2024, 3, 20),
      };
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TDCalendar(
              selectedDate: DateTime(2024, 3, 15),
              datesWithData: datesWithData,
            ),
          ),
        ),
      );

      // 验证日历组件存在
      expect(find.byType(TDCalendar), findsOneWidget);
      expect(find.byType(CalendarDayItem), findsWidgets);
    });
  });

  group('CalendarDayData Tests', () {
    test('should create calendar day data correctly', () {
      final date = DateTime(2024, 3, 15);
      final dayData = CalendarDayData(
        date: date,
        isCurrentDay: true,
        isToday: false,
        hasData: true,
      );

      expect(dayData.date, equals(date));
      expect(dayData.isCurrentDay, isTrue);
      expect(dayData.isToday, isFalse);
      expect(dayData.hasData, isTrue);
    });
  });
}
