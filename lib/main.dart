import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/page/us/main_us.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/route_util/observer/route_observer.dart';
import 'package:flutter_kexue/utils/route_util/route_api/route_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
void main() {
  WidgetsFlutterBinding.ensureInitialized();

  Get.put(MainUS());

  // 强制竖屏
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]).then((_) async {
    runApp(const MyApp());
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          title: '棵学戒社',
          theme: ThemeData(
            appBarTheme: const AppBarTheme(
                surfaceTintColor: Colors.transparent
            ),
            primaryColor: const Color(0xFF4ACE7F),
            scaffoldBackgroundColor: const Color(0xFFF6F6F6),
            visualDensity: VisualDensity.adaptivePlatformDensity,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            textTheme: TextTheme(
              titleLarge: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF222222),
              ),
              bodyMedium: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
              labelMedium: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF999999),
              ),
            ),
            cardTheme: CardThemeData(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            textSelectionTheme: TextSelectionThemeData(
              cursorColor: const Color(0xFF4ACE7F), // 全局设置光标颜色
            ),
          ),
          debugShowCheckedModeBanner: false,
          initialRoute: AppPages.initial,
          getPages: AppPages.routes,
          builder: (BuildContext context, Widget? child) {
            return FlutterSmartDialog(child: child); // 初始化 SmartDialog
          },
          navigatorObservers: [globalRouteObserver, pageRouteObserver,FlutterSmartDialog.observer],//按顺序执行回调
          navigatorKey: RouteCore.routeGlobalKey,
        );
      },
    );
  }
}

