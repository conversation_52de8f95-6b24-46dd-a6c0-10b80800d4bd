import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/login/login_input_page/entity/login_input_code_props.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:get/get.dart';

/// 测试 ActionSheet List 页面
class TestPage extends StatefulWidget {
  const TestPage({super.key});

  @override
  State<TestPage> createState() => _TestPage();
}

class _TestPage extends State<TestPage> {
  /// 处理功能菜单点击
  void _onFunctionMenuPressed(String title) {
    // TODO: 根据title跳转到对应的功能页面
    switch (title) {
      case '跳转登录':
        Get.toNamed(Routes.loginPhone);
        break;
      case '跳转验证码':
        {
          final props = LoginInputCodeProps(
            tel: "18200381732",
            code: '1234',
            autoGetCode: false,
          );
          Get.toNamed(Routes.loginInputCode, arguments: props);
        }
        break;
      case '日记编辑':
        Get.toNamed(Routes.diaryEditor);
        break;
      case '习惯管理':
        Get.toNamed(Routes.habitManager);
        break;
      case '记录管理':
        Get.toNamed(Routes.recordManager);
      case '多选记录管理':
        Get.toNamed(Routes.recordMultipleManager);
        break;
      case '计划首页':
        Get.toNamed(Routes.planHome);
        break;
      case '计划推荐':
        Get.toNamed(Routes.planRecommend);
        break;
      case '计划添加':
        Get.toNamed(Routes.planAddGoal);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试页面集合'),
        backgroundColor: ColorsUtil.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        spacing: 10,
        children: [_buildFunctionMenu()],
      ),
    );
  }

  /// 构建功能菜单
  Widget _buildFunctionMenu() {
    final menuItems = [
      // {'title': '文章', 'icon': Icons.article_outlined, 'badge': '4'},
      // {'title': '白噪声', 'icon': Icons.music_note_outlined, 'badge': null},
      // {'title': '排行榜', 'icon': Icons.bar_chart_outlined, 'badge': null},
      // {'title': '目标', 'icon': Icons.track_changes_outlined, 'badge': null},
      {'title': '跳转登录', 'icon': Icons.grid_view_outlined, 'badge': null},
      {'title': '跳转验证码', 'icon': Icons.grid_view_outlined, 'badge': null},
      {'title': '日记编辑', 'icon': Icons.edit_note_outlined, 'badge': null},
      {'title': '习惯管理', 'icon': Icons.track_changes_outlined, 'badge': null},
      {'title': '记录管理', 'icon': Icons.track_changes_outlined, 'badge': null},
      {'title': '多选记录管理', 'icon': Icons.track_changes_outlined, 'badge': null},
      {'title': '计划首页', 'icon': Icons.track_changes_outlined, 'badge': null},
      {'title': '计划推荐', 'icon': Icons.track_changes_outlined, 'badge': null},
      {'title': '计划添加', 'icon': Icons.track_changes_outlined, 'badge': null},
    ];

    return Container(
      padding: const EdgeInsets.fromLTRB(24, 8, 24, 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          children: menuItems
              .map((item) => _buildFunctionMenuItem(
                    title: item['title'] as String,
                    icon: item['icon'] as IconData,
                  ))
              .toList()),
    );
  }

  /// 构建单个功能菜单项
  Widget _buildFunctionMenuItem({
    required String title,
    required IconData icon,
  }) {
    return GestureDetector(
      onTap: () => _onFunctionMenuPressed(title),
      child: Column(
        children: [
          Icon(
            icon,
            color: ColorsUtil.primaryColor,
            size: 30,
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: ColorsUtil.textBlack,
            ),
          ),
        ],
      ),
    );
  }
}
