import 'package:flutter_kexue/page/binding/main_binding.dart';
import 'package:flutter_kexue/page/login/login_input_page/login_input_code_page.dart';
import 'package:flutter_kexue/page/login/login_page/login_page.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/home_calendar_page.dart';
import 'package:flutter_kexue/page/main_page/main_page.dart';
import 'package:flutter_kexue/page/splash/splash_page.dart';
import 'package:flutter_kexue/page/page/check_in_record/check_in_record_page.dart';
import 'package:flutter_kexue/page/page/diary_details/diary_details_page.dart';
import 'package:flutter_kexue/page/page/diary_editor/diary_editor_page.dart';
import 'package:flutter_kexue/page/page/habit_manager/habit_manager_page.dart';
import 'package:flutter_kexue/page/page/image_preview/image_preview_page.dart';
import 'package:flutter_kexue/page/page/multiple_record_edit/multiple_record_edit_page.dart';
import 'package:flutter_kexue/page/page/multiple_record_manager/multiple_record_manager_page.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/plan_add_goal_page.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_or_edit/plan_add_or_edit_page.dart';
import 'package:flutter_kexue/page/page/plan/plan_details/plan_details_page.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/plan_home_page.dart';
import 'package:flutter_kexue/page/page/plan/plan_manager/plan_manager_page.dart';
import 'package:flutter_kexue/page/page/plan/plan_recommend/plan_recommend_page.dart';
import 'package:flutter_kexue/page/page/record_add_or_edit/record_add_or_edit_page.dart';
import 'package:flutter_kexue/page/page/record_details/record_details_page.dart';
import 'package:flutter_kexue/page/page/record_manager/record_manager_page.dart';
import 'package:flutter_kexue/page/page/statistics_flow/statistics_flow_page.dart';
import 'package:flutter_kexue/pages/test_page.dart';
import 'package:get/get.dart';

abstract class Routes {
  static const splash = '/splash';
  static const main = '/';
  static const home = '/home';
  static const loginPhone = '/login/login_phone';
  static const loginInputCode = '/login/input_code';

  ///编辑日记
  static const diaryEditor = '/diary/editor';

  ///日记详情
  static const diaryDetails = '/diary/details';

  ///习惯管理
  static const habitManager = '/habit/manager';

  ///流水统计
  static const statisticsFlow = '/statistics/flow';

  ///打卡记录
  static const checkInRecord = '/check_in/record';

  ///记录管里
  static const recordManager = '/record/manager';

  ///多条记录管里
  static const recordMultipleManager = '/record/multipleManager';

  ///多条记录编辑
  static const recordMultipleEdit = '/record/recordMultipleEdit';

  ///新增记录
  static const recordAddOrEdit = '/record/add_or_edit';

  ///查看记录详情
  static const recordDetails = '/record/details';

  ///计划首页
  static const planHome = '/plan/home';

  ///计划详情
  static const planDetails = '/plan/details';

  ///计划列表
  static const planRecommend = '/plan/planRecommend';

  ///计划添加
  static const planAddGoal = '/plan/planAddGoal';

  ///计划（体系）管里
  static const planManager = '/plan/planManager';

  ///计划（体系）管里
  static const planAddOrEdit = '/plan/planAddOrEdit';

  ///图片预览
  static const imagePreview = '/image/preview';

  ///测试页面
  static const test = '/test';
}

class AppPages {
  // 初始路由
  static const initial = Routes.splash;

  static final routes = [
    GetPage(
      name: Routes.splash,
      page: () => const SplashPage(),
      bindings: [
        SplashBinding(),
      ],
    ),
    GetPage(
      name: Routes.main,
      page: () => MainPage(),
      bindings: [
        MainBinding(),
      ],
    ),
    GetPage(
      name: Routes.home,
      page: () => HomeCalendarPage(),
      bindings: [
        MainBinding(),
      ],
    ),
    GetPage(
      name: Routes.loginPhone,
      page: () => LoginPage(),
    ),
    GetPage(
      name: Routes.loginInputCode,
      page: () => LoginInputCodePage(),
    ),
    GetPage(
      name: Routes.diaryEditor,
      page: () => DiaryEditorPage(),
    ),
    GetPage(
      name: Routes.diaryDetails,
      page: () => DiaryDetailsPage(),
    ),
    GetPage(
      name: Routes.habitManager,
      page: () => HabitManagerPage(),
    ),
    GetPage(
      name: Routes.statisticsFlow,
      page: () => StatisticsFlowPage(),
    ),
    GetPage(
      name: Routes.checkInRecord,
      page: () => CheckInRecordPage(),
    ),
    GetPage(
      name: Routes.recordManager,
      page: () => RecordManagerPage(),
    ),
    GetPage(
      name: Routes.recordMultipleManager,
      page: () => MultipleRecordManagerPage(),
    ),
    GetPage(
      name: Routes.recordMultipleEdit,
      page: () => MultipleRecordEditPage(),
    ),
    GetPage(
      name: Routes.recordAddOrEdit,
      page: () => RecordAddOrEditPage(),
    ),
    GetPage(
      name: Routes.recordDetails,
      page: () => RecordDetailsPage(),
    ),
    GetPage(
      name: Routes.imagePreview,
      page: () => const ImagePreviewPage(),
    ),
    GetPage(
      name: Routes.planHome,
      page: () => PlanHomePage(),
    ),
    GetPage(
      name: Routes.planDetails,
      page: () => PlanDetailsPage(),
    ),
    GetPage(
      name: Routes.planRecommend,
      page: () => PlanRecommendPage(),
    ),
    GetPage(
      name: Routes.planAddGoal,
      page: () => PlanAddGoalPage(),
    ),
    GetPage(
      name: Routes.planManager,
      page: () => PlanManagerPage(),
    ),
    GetPage(
      name: Routes.planAddOrEdit,
      page: () => PlanAddOrEditPage(),
    ),
    GetPage(
      name: Routes.test,
      page: () => TestPage(),
    ),
  ];

// 使用方法
// Get.toNamed(Routes.worker);
// Get.toNamed(Routes.group);
// Get.toNamed(Routes.worker, arguments: {"name": 'xxx'});
}
