import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/system_uistate.dart';

class DailyClockUIState {
  ///标题名其实就是体系
  final SystemUIState systemUIState;

  ///总的进度
  final double progress;

  ///展开折叠状态
  final bool isExpand;

  ///打卡列表
  final List<ClockUIState> list;

  DailyClockUIState({
    required this.systemUIState,
    this.progress = 0,
    this.isExpand = true,
    this.list = const [],
  });
}
