import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/common_uistate/system_uistate.dart';

class ClockUIState {
  ///标题名其实就是体系
  final SystemUIState? systemUIState;

  ///总的进度
  final double progress;

  ///目标名称
  final String target;

  ///目标打卡类型
  final TargetType goalType;

  /// 目标天数(破戒需要使用)
  final int goalDay;

  /// 开始时间
  final DateTime? startDate;

  /// 结束时间
  final DateTime? endDate;

  /// 累计几次完成(破戒需要使用)
  final int totalCount;

  /// 破戒打卡了多少次(破戒需要使用)
  final int nativeCount;

  ///每周打卡频率
  final int checkInFrequency;

  /// 每日打卡次数
  final int dailyCount;

  /// 每日要打卡总次数
  final int dailyTotalCount;

  /// 每日完成时弹出弹窗(破戒需要使用)
  final bool showDailyMotivation;

  /// 目标激励语(破戒需要使用)
  final String? motivation;

  final List<String> images;

  ClockUIState({
    required this.target,
    required this.goalType,
    this.systemUIState,
    this.progress = 0,
    this.goalDay = 0,
    this.nativeCount = 0,
    this.startDate,
    this.endDate,
    this.totalCount = 0,
    this.checkInFrequency = 0,
    this.dailyCount = 0,
    this.dailyTotalCount = 0,
    this.showDailyMotivation = false,
    this.motivation,
    this.images = const [],
  });

  copyWith({
     String? target,
     TargetType? goalType,
    SystemUIState? systemUIState,
    double? progress,
    int? goalDay,
    int? nativeCount,
    DateTime? startDate,
    DateTime? endDate,
    int? totalCount,
    int? checkInFrequency,
    int? dailyCount,
    int? dailyTotalCount,
    bool? showDailyMotivation,
    String? motivation,
    List<String>? images}) {
    return ClockUIState(
      target: target ?? this.target,
      goalType: goalType ?? this.goalType,
      systemUIState: systemUIState ?? this.systemUIState,
      progress: progress ?? this.progress,
      goalDay: goalDay ?? this.goalDay,
      nativeCount: nativeCount ?? this.nativeCount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalCount: totalCount ?? this.totalCount,
      checkInFrequency: checkInFrequency ?? this.checkInFrequency,
      dailyCount: dailyCount ?? this.dailyCount,
      dailyTotalCount: dailyTotalCount ?? this.dailyTotalCount,
      showDailyMotivation: showDailyMotivation ?? this.showDailyMotivation,
      motivation: motivation ?? this.motivation,
      images: images ?? this.images
    );
  }
}
