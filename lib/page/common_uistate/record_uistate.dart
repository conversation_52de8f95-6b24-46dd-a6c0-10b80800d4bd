import 'package:flutter_kexue/data/enums/record_type.dart';

class RecordUIState {
  final RecordType type;
  final bool system;
  final String title;
  final String? content;
  final String? icon;
  final String? image;

  RecordUIState({
    required this.type,
    required this.title,
    this.system = false,
    this.content,
    this.icon,
    this.image,
  });

  /// 创建一个新的RecordUIState实例，可以选择性地更新某些字段
  RecordUIState copyWith({
    RecordType? type,
    bool? system,
    String? title,
    String? content,
    String? icon,
    String? image,
  }) {
    return RecordUIState(
      type: type ?? this.type,
      system: system ?? this.system,
      title: title ?? this.title,
      content: content ?? this.content,
      icon: icon ?? this.icon,
      image: image ?? this.image,
    );
  }

  /// 判断图片是否为网络图片
  bool get isNetworkImage {
    return image != null &&
           (image!.startsWith('http://') || image!.startsWith('https://'));
  }

  /// 判断图片是否为本地文件
  bool get isLocalImage {
    return image != null && !isNetworkImage;
  }
}
