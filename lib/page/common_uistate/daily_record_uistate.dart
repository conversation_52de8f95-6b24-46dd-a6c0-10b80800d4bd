import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/record_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/system_uistate.dart';

class DailyRecordUIState {
  ///标题名
  final String title;

  ///总的进度
  final double progress;

  ///展开折叠状态
  final bool isExpand;

  ///打卡列表
  final List<RecordUIState> list;

  DailyRecordUIState({
    this.title = '记录',
    this.progress = 0,
    this.isExpand = true,
    this.list = const [],
  });
}
