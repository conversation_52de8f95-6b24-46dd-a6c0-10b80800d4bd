import 'package:flutter_kexue/data/enums/activity_type.dart';
import 'package:flutter_kexue/page/common_uistate/daily_clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/daily_record_uistate.dart';

class ActivityUIState {
  final ActivityType type;
  final List<DailyRecordUIState> dailyRecordList;
  final List<DailyClockUIState> dailyClockList;


  ActivityUIState({
    required this.type,
    this.dailyClockList = const [],
    this.dailyRecordList = const [],
  });
}
