import 'package:flutter/material.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/store_util/shared_prefs.dart';
import 'package:flutter_kexue/utils/user_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 启动页面 - 用于判断登录状态并跳转到相应页面
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimation();
    _checkLoginStatus();
  }

  /// 初始化动画
  void _initAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
    ));

    _animationController.forward();
  }

  /// 检查登录状态
  Future<void> _checkLoginStatus() async {
    try {
      // 等待动画播放一段时间，提供更好的用户体验
      await Future.delayed(const Duration(milliseconds: 1000));
      await SharedPreferencesHelper().init();
      // 检查登录状态
      final isLoggedIn = await UserManager.instance.checkLoginStatus();

      if (mounted) {
        if (isLoggedIn) {
          // 已登录，跳转到主页面
          _navigateToMain();
        } else {
          // 未登录，跳转到登录页面
          _navigateToLogin();
        }
      }
    } catch (e) {
      debugPrint('检查登录状态失败: $e');
      
      if (mounted) {
        // 出错时默认跳转到登录页面
        _navigateToLogin();
      }
    }
  }

  /// 跳转到主页面
  void _navigateToMain() {
    Get.offAllNamed(Routes.home);
  }

  /// 跳转到登录页面
  void _navigateToLogin() {
    Get.offAllNamed(Routes.loginPhone);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF4ACE7F), // 使用主题色作为背景
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF4ACE7F),
                  Color(0xFF3BA86B),
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo区域
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      width: 120.w,
                      height: 120.w,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(24.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          '棵学',
                          style: TextStyle(
                            fontSize: 32.sp,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4ACE7F),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                SizedBox(height: 24.h),

                // 应用名称
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    '棵学戒社',
                    style: TextStyle(
                      fontSize: 28.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),

                SizedBox(height: 8.h),

                // 副标题
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    '科学戒断，健康生活',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ),

                SizedBox(height: 80.h),

                // 加载指示器
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: SizedBox(
                    width: 24.w,
                    height: 24.w,
                    child: const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 2,
                    ),
                  ),
                ),

                SizedBox(height: 16.h),

                // 加载文本
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    '正在初始化...',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// 启动页面控制器
class SplashController extends GetxController {
  /// 检查登录状态并跳转
  Future<void> checkAndNavigate() async {
    try {
      // 获取用户信息
      final userInfo = await UserManager.instance.getUserInfo(forceRefresh: true);
      
      if (userInfo != null && UserManager.instance.isLoggedIn) {
        // 已登录，跳转到主页面
        Get.offAllNamed(Routes.main);
      } else {
        // 未登录，跳转到登录页面
        Get.offAllNamed(Routes.loginPhone);
      }
    } catch (e) {
      debugPrint('启动页面检查登录状态失败: $e');
      // 出错时默认跳转到登录页面
      Get.offAllNamed(Routes.loginPhone);
    }
  }

  /// 手动跳转到主页面（用于测试）
  void navigateToMain() {
    Get.offAllNamed(Routes.main);
  }

  /// 手动跳转到登录页面（用于测试）
  void navigateToLogin() {
    Get.offAllNamed(Routes.loginPhone);
  }
}

/// 启动页面绑定
class SplashBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<SplashController>(() => SplashController());
  }
}
