import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/home_calendar_page.dart';
import 'package:flutter_kexue/page/main_page/mine_page/mine_page.dart';
import 'package:flutter_kexue/page/main_page/home_page/home/<USER>';
import 'package:flutter_kexue/page/us/main_us.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class MainPage extends GetView<MainUS> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      HomePage(),
      HomeCalendarPage(),
      MinePage(),
    ];

    return Scaffold(
      body: Obx(() => IndexedStack(
            index: controller.currentIndex.value,
            children: pages,
          )),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: Color(0x80CCCCCC),
                width: 0.5,
              ),
            )),
        child: Obx(
          () => BottomNavigationBar(
            backgroundColor: Colors.white,
            type: BottomNavigationBarType.fixed,
            currentIndex: controller.currentIndex.value,
            onTap: controller.changeTabIndex,
            selectedItemColor: ColorsUtil.primaryColor,
            unselectedItemColor: Color(0xFF5A5A66),
            selectedFontSize: 14,
            unselectedFontSize: 14,
            items: [
              BottomNavigationBarItem(
                label: '首页',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 0
                          ? const AssetImage(
                              'assets/images/tabbar/icon_tabbar_project_selected.webp')
                          : const AssetImage(
                              'assets/images/tabbar/icon_tabbar_project_nomal.webp'),
                    )),
              ),
              BottomNavigationBarItem(
                label: '中间',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 1
                          ? const AssetImage(
                              'assets/images/tabbar/icon_tabbar_statistic_selected.webp')
                          : const AssetImage(
                              'assets/images/tabbar/icon_tabbar_statistic_normal.webp'),
                    )),
              ),
              BottomNavigationBarItem(
                label: '我的',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 3
                          ? const AssetImage(
                              'assets/images/tabbar/icon_tabbar_mine_selected.webp')
                          : const AssetImage(
                              'assets/images/tabbar/icon_tabbar_mine_normal.webp'),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
