import "package:get/get.dart";
import "../ui_rep/mine_ui_rep.dart";
import "protocol/mine_ui_state.dart";

/// @date 2025/06/28
/// @description Mine页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class MineViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = MineUIState().obs;
  var uiRep = MineUIRep();

  MineViewModel() {
    fetchData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      var result = await uiRep.fetchData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.data = "failed";
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(MineUIRepEntity entity) {
    uiState.value.data = entity.data;
  }

  /// 处理功能菜单点击
  void onFunctionMenuPressed(String title) {
    print('功能菜单点击: $title');
    // TODO: 根据title跳转到对应的功能页面
    switch (title) {
      case '发表':
        // Get.toNamed('/publish');
        break;
      case '收藏':
        // Get.toNamed('/favorites');
        break;
      case '成就':
        // Get.toNamed('/achievements');
        break;
      case '目标':
        // Get.toNamed('/goals');
        break;
    }
  }

  /// 处理列表项点击
  void onListItemPressed(String title) {
    print('列表项点击: $title');
    // TODO: 根据title跳转到对应的功能页面
    switch (title) {
      case '已完成输入密码':
        // 处理密码设置
        break;
      case '启动时自动登录':
        // 处理自动登录设置
        break;
      case '首页持续天数':
        // 处理持续天数设置
        break;
      case '功能使用指导':
        // 显示使用指导
        break;
      case '好评推荐':
        // 跳转到应用商店评价
        break;
      case '帮助与反馈':
        // 显示帮助与反馈页面
        break;
      case '系统设置':
        // 跳转到系统设置
        break;
      case '新手指南':
        // 显示新手指南
        break;
      case '条款与隐私':
        // 显示条款与隐私政策
        break;
      case '关于我们':
        // 显示关于我们页面
        break;
    }
  }

  /// 处理退出登录
  void onLogoutPressed() {
    print('退出登录');
    // TODO: 显示确认对话框，然后执行退出登录逻辑
    Get.defaultDialog(
      title: '退出登录',
      middleText: '确定要退出登录吗？',
      textConfirm: '确定',
      textCancel: '取消',
      onConfirm: () {
        Get.back();
        // 执行退出登录逻辑
        _performLogout();
      },
      onCancel: () {
        Get.back();
      },
    );
  }

  /// 执行退出登录逻辑
  void _performLogout() {
    // TODO: 清除用户数据，跳转到登录页面
    // Get.offAllNamed('/login');
    print('执行退出登录逻辑');
  }
}
