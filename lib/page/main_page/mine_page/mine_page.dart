import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:get/get.dart';
import 'entity/mine_props.dart';
import 'vm/mine_viewmodel.dart';

/// @date 2025/06/28
/// @param props 页面路由参数
/// @returns
/// @description Mine页面入口
class MinePage extends StatelessWidget {
  MinePage({super.key, this.props});

  final MineProps? props;
  final MineViewModel viewModel = MineViewModel();

  @override
  Widget build(BuildContext context) {
    // 设置系统栏样式
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      // 状态栏背景透明
      statusBarIconBrightness: Brightness.dark,
      // 状态栏图标（如电量、信号）为黑色
      statusBarBrightness: Brightness.light,
      // iOS 的状态栏字体颜色模式（可选）
      systemNavigationBarColor: Colors.white,
      // 底部导航栏颜色（可选）
      systemNavigationBarIconBrightness: Brightness.dark, // 底部导航栏图标颜色
    ));

    return Scaffold(
      // 移除默认的 AppBar
      body: Stack(
        children: [
          // 渐变背景层
          Container(
            height: 350, // 设置渐变层的高度为200
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFD3F4EF), // 顶部颜色
                  Color(0xFFF6F6F6), // 底部颜色
                ],
              ),
            ),
          ),

          // 自定义内容层（包含虚拟状态栏高度）
          SafeArea(
            child: Column(
              children: [
                // 自定义 AppBar 区域
                _buildCustomAppBar(context),
                // 主体内容
                Expanded(child: contentView()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomAppBar(BuildContext context) {
    return Container(
      height: 40,
      color: Colors.transparent,
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(14, 0, 14, 0),
        child: Text(
          "棵学戒社",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w900,
            color: ColorsUtil.textBlack,
          ),
        ),
      ),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 顶部用户信息模块
          _buildUserInfoSection(),

          const SizedBox(height: 12),

          // 会员信息模块
          _buildMembershipSection(),

          const SizedBox(height: 12),

          // 功能菜单模块
          _buildFunctionMenu(),

          const SizedBox(height: 12),

          // 学习相关列表
          _buildLearningSection(),

          const SizedBox(height: 12),

          // 功能相关列表
          _buildFunctionSection(),

          const SizedBox(height: 12),

          // 系统相关列表
          _buildSystemSection(),

          const SizedBox(height: 8),

          Text("备案号xxxxxxxxxxxxxx",style: TextStyle(color: ColorsUtil.garyB1,fontSize: 12)),

          const SizedBox(height: 14),

          // 退出登录按钮
          _buildLogoutButton(),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  /// 构建顶部用户信息模块
  Widget _buildUserInfoSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 0, 14, 0),
      padding: const EdgeInsets.all(14),
      child: Row(
        children: [
          // 用户头像
          Container(
            width: 68,
            height: 68,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: ColorsUtil.primaryColor, width: 2),
            ),
            child: ClipOval(
              child: Image.asset(
                'assets/images/icon_logo.png',
                width: 68,
                height: 68,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: ColorsUtil.primaryColor.withOpacity(0.1),
                    child: Icon(
                      Icons.person,
                      size: 30,
                      color: ColorsUtil.primaryColor,
                    ),
                  );
                },
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '海纳百川',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '不是无选择的接纳，而是以深不可测的格局，将浑浊与清澈一并揽入怀中的...',
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.gary58,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // 右箭头
          Icon(
            Icons.chevron_right,
            color: ColorsUtil.gary58,
            size: 24,
          ),
        ],
      ),
    );
  }

  /// 构建会员信息模块
  Widget _buildMembershipSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 14),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Row(
        children: [
          // 皇冠图标
          const Icon(
            Icons.workspace_premium,
            color: Colors.yellow,
            size: 20,
          ),

          const SizedBox(width: 12),

          // 会员信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '升级到高级会员',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '支持棵学戒走得更远(去广告)',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFFCECECE),
                  ),
                ),
              ],
            ),
          ),

          // 升级按钮
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '去升级',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: ColorsUtil.yellow,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建功能菜单模块
  Widget _buildFunctionMenu() {
    final menuItems = [
      {'title': '发表', 'icon': Icons.edit_outlined, 'badge': null},
      {'title': '收藏', 'icon': Icons.bookmark_outline, 'badge': null},
      {'title': '成就', 'icon': Icons.emoji_events_outlined, 'badge': null},
      {'title': '目标', 'icon': Icons.track_changes_outlined, 'badge': null},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 14),
      padding: const EdgeInsets.fromLTRB(24, 20, 24, 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: menuItems
            .map((item) => _buildFunctionMenuItem(
                  title: item['title'] as String,
                  icon: item['icon'] as IconData,
                ))
            .toList(),
      ),
    );
  }

  /// 构建单个功能菜单项
  Widget _buildFunctionMenuItem({
    required String title,
    required IconData icon,
  }) {
    return GestureDetector(
      onTap: () => viewModel.onFunctionMenuPressed(title),
      child: Column(
        children: [
          Icon(
            icon,
            color: ColorsUtil.primaryColor,
            size: 25,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: ColorsUtil.textBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建学习相关列表
  Widget _buildLearningSection() {
    final learningItems = [
      {
        'title': '启动时输入密码',
        'subtitle': '已开启',
        'icon': Icons.lock_outline,
        'color': ColorsUtil.primaryColor,
        'showArrow': true,
      },
      {
        'title': '启动时自动签到',
        'subtitle': '未开启',
        'icon': Icons.login_outlined,
        'color': ColorsUtil.yellow,
        'showArrow': true,
      },
      {
        'title': '首页持续天数',
        'subtitle': '置顶',
        'icon': Icons.calendar_today_outlined,
        'color': ColorsUtil.primaryColor,
        'showArrow': true,
      },
    ];

    return _buildListSection(learningItems);
  }

  /// 构建功能相关列表
  Widget _buildFunctionSection() {
    final functionItems = [
      {
        'title': '功能反馈和投票',
        'subtitle': '有奖',
        'icon': Icons.help_outline,
        'color': ColorsUtil.yellow,
        'showArrow': true,
      },
      {
        'title': '好评棵学戒',
        'subtitle': '',
        'icon': Icons.star_outline,
        'color': ColorsUtil.primaryColor,
        'showArrow': true,
      },
      {
        'title': '帮助与反馈',
        'subtitle': '',
        'icon': Icons.feedback_outlined,
        'color': ColorsUtil.primaryColor,
        'showArrow': true,
      },
    ];

    return _buildListSection(functionItems);
  }

  /// 构建系统相关列表
  Widget _buildSystemSection() {
    final systemItems = [
      {
        'title': '系统设置',
        'subtitle': '',
        'icon': Icons.settings_outlined,
        'color': ColorsUtil.primaryColor,
        'showArrow': true,
      },
      {
        'title': '新手指南',
        'subtitle': '',
        'icon': Icons.school_outlined,
        'color': ColorsUtil.primaryColor,
        'showArrow': true,
      },
      {
        'title': '联系客服',
        'subtitle': '',
        'icon': Icons.privacy_tip_outlined,
        'color': ColorsUtil.primaryColor,
        'showArrow': true,
      },
      {
        'title': '关于我们',
        'subtitle': 'v2.3.0',
        'icon': Icons.info_outline,
        'color': ColorsUtil.textBlack,
        'showArrow': true,
      },
    ];

    return _buildListSection(systemItems);
  }

  /// 构建通用列表区域
  Widget _buildListSection(List<Map<String, dynamic>> items) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isLast = index == items.length - 1;

          return _buildListItem(
            title: item['title'] as String,
            subtitle: item['subtitle'] as String,
            icon: item['icon'] as IconData,
            color: item['color'] as Color,
            showArrow: item['showArrow'] as bool,
            isLast: isLast,
          );
        }).toList(),
      ),
    );
  }

  /// 构建单个列表项
  Widget _buildListItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool showArrow,
    required bool isLast,
  }) {
    return GestureDetector(
      onTap: () => viewModel.onListItemPressed(title),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          border: isLast
              ? null
              : Border(
                  bottom: BorderSide(
                    color: const Color(0xFFE5E5E5),
                    width: 0.5,
                  ),
                ),
        ),
        child: Row(
          children: [
            // 图标
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color:  ColorsUtil.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 18,
              ),
            ),

            const SizedBox(width: 12),

            // 标题
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  color: ColorsUtil.textBlack,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // 副标题
            if (subtitle.isNotEmpty) ...[
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: color,
                ),
              ),
              const SizedBox(width: 8),
            ],

            // 箭头
            if (showArrow)
              Icon(
                Icons.chevron_right,
                color: ColorsUtil.gary58,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  /// 构建退出登录按钮
  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: GestureDetector(
        onTap: () => viewModel.onLogoutPressed(),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(7),
            boxShadow: ShadowsUtil.cardShadow,
          ),
          child: Text(
            '退出登录',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
