import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:get/get.dart';
import 'entity/home_props.dart';
import 'vm/home_viewmodel.dart';
import 'vm/protocol/home_ui_state.dart';

/// @date 2025/06/24
/// @param props 页面路由参数
/// @returns
/// @description Home页面入口
class HomePage extends StatelessWidget {
  HomePage({super.key, this.props});

  final HomeProps? props;
  final HomeViewModel viewModel = HomeViewModel();

  @override
  Widget build(BuildContext context) {
    // 设置系统栏样式
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      // 状态栏背景透明
      statusBarIconBrightness: Brightness.dark,
      // 状态栏图标（如电量、信号）为黑色
      statusBarBrightness: Brightness.light,
      // iOS 的状态栏字体颜色模式（可选）
      systemNavigationBarColor: Colors.white,
      // 底部导航栏颜色（可选）
      systemNavigationBarIconBrightness: Brightness.dark, // 底部导航栏图标颜色
    ));

    return Scaffold(
      // 移除默认的 AppBar
      body: Stack(
        children: [
          // 渐变背景层
          Container(
            height: 350, // 设置渐变层的高度为200
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFD3F4EF), // 顶部颜色
                  Color(0xFFF6F6F6), // 底部颜色
                ],
              ),
            ),
          ),

          // 自定义内容层（包含虚拟状态栏高度）
          SafeArea(
            child: Column(
              children: [
                // 自定义 AppBar 区域
                _buildCustomAppBar(context),
                // 主体内容
                Expanded(child: contentView()),
              ],
            ),
          ),

          // 底部悬浮按钮
          _buildFloatingButton(),
          _buildTestButton(),
        ],
      ),
    );
  }

  Widget _buildCustomAppBar(BuildContext context) {
    return Container(
      height: 40,
      color: Colors.transparent,
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(14, 0, 14, 0),
        child: Text(
          "棵学戒社",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w900,
            color: ColorsUtil.textBlack,
          ),
        ),
      ),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Column(
      children: [
        // 顶部固定区域
        _buildTopSection(),

        // 可滚动内容区域
        Expanded(
          child: _buildListView(),
        ),
      ],
    );
  }

  /// 构建顶部区域
  Widget _buildTopSection() {
    return Container(
      padding: EdgeInsets.fromLTRB(14, 0, 14, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图片区域
          _buildImageSection(),

          const SizedBox(height: 10),

          // 签名
          _buildQuotation(),
        ],
      ),
    );
  }

  /// 构建图片区域
  Widget _buildImageSection() {
    return SizedBox(
      height: 160,
      width: double.infinity,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Image.asset(
              'assets/images/ic_home_banner.png',
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.fill,
            ),
          ),
          // 文案内容
          Padding(
            padding: const EdgeInsets.fromLTRB(14, 10, 14, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "能量：",
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                Text(
                  "${viewModel.uiState.value.statisticsData?.jsTotal ?? 0}",
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                Text(
                  "${viewModel.uiState.value.statisticsData?.jsTitle}",
                  style: TextStyle(
                    fontSize: 16,
                    color: ColorsUtil.textBlack,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          Positioned(
            bottom: 2, // Positions at the bottom
            left: 12, // Positions at the left
            child: Container(
              height: 20,
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.white, width: 0.5),
              ),
              child: Text(
                "戒第8天",
                style: TextStyle(fontSize: 12, color: Colors.white),
              ),
            ),
          ),
          Positioned(
            bottom: 2, // Positions at the bottom
            right: 12, // Positions at the left
            child: Container(
              height: 20,
              alignment: Alignment.center,
              // Centers text horizontally and vertically
              child: Text(
                "365",
                style: TextStyle(fontSize: 12, color: Colors.white),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 构建签名
  Widget _buildQuotation() {
    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      padding: const EdgeInsets.fromLTRB(14, 10, 14, 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      constraints: const BoxConstraints(minHeight: 70),
      child: Text(
        "“${viewModel.uiState.value.statisticsData?.jsQuotation ?? ''}”",
        style: TextStyle(
          fontSize: 14,
          color: ColorsUtil.textBlack,
        ),
      ),
    );
  }

  /// 构建可滚动内容区域
  Widget _buildListView() {
    return Container(
      padding: const EdgeInsets.fromLTRB(14, 10, 14, 0),
      child: ListView.separated(
        itemCount: viewModel.uiState.value.jsRecordList.length,
        separatorBuilder: (context, index) => const SizedBox(height: 10),
        itemBuilder: (context, index) {
          final record = viewModel.uiState.value.jsRecordList[index];
          return _buildListItemView(record);
        },
      ),
    );
  }

  /// 构建可滚动内容区域
  Widget _buildListItemView(JsRecordUIState record) {
    return Column(
      children: [
        // 月份列表标题栏
        _buildMonthlyListHeader(record),

        const SizedBox(height: 10),
        // 月份记录列表
        _buildMonthlyRecordsList(record.jsRecordList),
      ],
    );
  }

  /// 构建月份列表标题栏
  Widget _buildMonthlyListHeader(JsRecordUIState uiState) {
    return Row(
      children: [
        Text(
          uiState.day,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: ColorsUtil.textBlack,
          ),
        ),
        SizedBox(width: 15),
        Text(
          uiState.weekDay,
          style: TextStyle(
            fontSize: 16,
            color: ColorsUtil.garyB1,
          ),
        ),
      ],
    );
  }

  /// 构建月份记录列表
  Widget _buildMonthlyRecordsList(List<JsRecordItemUIState> jsRecordList) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: jsRecordList.length,
        separatorBuilder: (context, index) => Divider(
          height: 12,
          thickness: 12,
          color: Colors.transparent,
        ),
        itemBuilder: (context, index) {
          final record = jsRecordList[index];
          return _buildDiaryItemView(record);
        },
      ),
    );
  }

  /// 构建单个记录项
  Widget _buildDiaryItemView(JsRecordItemUIState uiState) {
    return GestureDetector(
      onTap: () => {},
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧标题
          Text(
            uiState.title,
            style: TextStyle(
              fontSize: 16,
              color: uiState.color,
            ),
          ),

          // 右侧次数
          Text(
            (uiState.count > 0) ? "${uiState.count}次" : "${uiState.subTitle}",
            style: TextStyle(
              fontSize: 14,
              color: uiState.subColor ?? uiState.color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部悬浮按钮
  Widget _buildFloatingButton() {
    return Positioned(
      bottom: 10,
      left: 0,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: viewModel.onAddRecordPressed,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 8),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: ShadowsUtil.cardShadow,
            ),
            child: Text(
              "记一笔",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建测试悬浮按钮
  Widget _buildTestButton() {
    return Positioned(
      bottom: 10,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: () => {Get.toNamed(Routes.test)},
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: ShadowsUtil.cardShadow,
            ),
            child: Text(
              "测试页面",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
