import 'dart:ui';

/// @date 2025/06/24
/// @description Home页UI状态
class HomeUIState {
  HomeUIState({
    this.isShowError,
    this.data,
    this.jsRecordList = const [],
    this.statisticsData,
  });

  bool? isShowError = false;
  String? data = "";

  /// 月度记录列表
  List<JsRecordUIState> jsRecordList = [];

  /// 统计数据
  StatisticsData? statisticsData;
}

/// 每天的记录
class JsRecordUIState {
  final String day;
  final String weekDay;
  List<JsRecordItemUIState> jsRecordList = [];

  JsRecordUIState({
    required this.day,
    required this.weekDay,
    required this.jsRecordList,
  });
}

class JsRecordItemUIState {
  final String title;
  final int count;
  final Color color;
  final Color? subColor;
  final String? subTitle;

  JsRecordItemUIState({
    required this.title,
    required this.count,
    required this.color,
    this.subColor,
    this.subTitle,
  });
}

// 统计数据模型
class StatisticsData {
  final int jsTotal;
  final String jsTitle;
  final int jsDays;
  final int jsCountDays;
  final String? jsQuotation;

  StatisticsData({
    required this.jsTotal,
    required this.jsTitle,
    required this.jsDays,
    required this.jsCountDays,
    this.jsQuotation,
  });
}
