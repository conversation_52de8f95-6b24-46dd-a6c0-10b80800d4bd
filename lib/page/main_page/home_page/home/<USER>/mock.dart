import 'dart:ui';

import 'package:flutter_kexue/page/main_page/home_page/home/<USER>/protocol/home_ui_state.dart';

class Mock{

  static String _getWeekdayName(int weekday) {
    const Map<int, String> weekdays = {
      1: '周一',
      2: '周二',
      3: '周三',
      4: '周四',
      5: '周五',
      6: '周六',
      7: '周日',
    };
    return weekdays[weekday] ?? '';
  }


  static generateMockData() {
    final now = DateTime.now();
    final List<JsRecordUIState> records = [];

    for (int i = 0; i < 30; i++) {
      final date = DateTime(now.year, now.month, now.day - i);
      final String day;

      if (i == 0) {
        day = "今天";
      } else if (i == 1) {
        day = "昨天";
      } else {
        day = "${date.month}月${date.day}日";
      }

      final String weekDay = Mock._getWeekdayName(date.weekday);

      // 每天添加一些示例记录
      final List<JsRecordItemUIState> items = [
        JsRecordItemUIState(
          title: "日行一善",
          color: Color(0xFF4ACE7F),
          subTitle: "今天随手帮助了他人",
          subColor: Color(0xFFB1BCCB),
          count: 0,
        ),
        JsRecordItemUIState(
          title: "看黄",
          color: Color(0xFFE99D42),
          count: 2,
        ),
        JsRecordItemUIState(
          title: "破戒",
          color: Color(0xFFBD3124),
          count: 4,
        ),
      ];

      records.add(JsRecordUIState(day: day, weekDay: weekDay, jsRecordList: items));
    }
    return records;
  }
}