import "package:flutter_kexue/page/main_page/home_page/home/<USER>/mock.dart";
import "package:flutter_kexue/routes/routes.dart";
import "package:get/get.dart";
import "../ui_rep/home_ui_rep.dart";
import "protocol/home_ui_state.dart";

/// @date 2025/06/24
/// @description Home页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class HomeViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = HomeUIState().obs;
  var uiRep = HomeUIRep();

  HomeViewModel() {
    _initializeData();
    fetchData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 初始化基础数据
  void _initializeData() {
    // 初始化统计数据
    uiState.value.statisticsData = StatisticsData(
      jsTotal: 80,
      jsTitle: "小树苗",
      jsDays: 8,
      jsCountDays: 368,
      jsQuotation: "“你若盛开，我们若盛开”\n “你若盛开，我们若盛开”",
    );

    // 初始化月度记录
    uiState.value.jsRecordList = Mock.generateMockData();
  }

  /// 获取星期名称
  String _getWeekdayName(int weekday) {
    const weekdays = ['一', '二', '三', '四', '五', '六', '日'];
    return '星期${weekdays[weekday - 1]}';
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      await uiRep.fetchData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.data = "failed";
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(HomeUIRepEntity entity) {
    uiState.value.data = entity.data;
  }

  /// 处理记一笔按钮点击
  void onAddRecordPressed() {
    // 跳转到新增记录页面
    Get.toNamed(Routes.recordAddOrEdit);
  }

  /// 处理统计卡片点击
  void onStatisticsCardPressed(int index) {
    // TODO: 根据index跳转到对应的统计页面
    // Get.toNamed('/statistics/$index');
  }
}
