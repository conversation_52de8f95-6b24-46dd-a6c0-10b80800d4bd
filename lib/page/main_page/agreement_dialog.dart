// agreement_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';

class AgreementDialog extends StatelessWidget {
  final VoidCallback onAgree;

  const AgreementDialog({super.key, required this.onAgree});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      margin: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 内容区域
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 24, 24, 20),
            child: RichText(
              textAlign: TextAlign.left,
              text: TextSpan(
                style: TextStyle(
                  fontSize: 14,
                  color: ColorsUtil.gary85,
                  height: 1.5,
                ),
                children: [
                  const TextSpan(text: "请阅读并同意 "),
                  TextSpan(
                    text: "《棵学戒社用户协议》",
                    style: TextStyle(
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const TextSpan(text: " 和 "),
                  TextSpan(
                    text: "《隐私政策》",
                    style: TextStyle(
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const TextSpan(text: "，允许棵学戒社统一管理本人账号信息"),
                ],
              ),
            ),
          ),

          // 分隔线
          Container(
            height: 0.5,
            color: Colors.grey.shade300,
          ),

          // 按钮区域
          Row(
            children: [
              // 拒绝按钮
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    SmartDialog.dismiss();
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    height: 48,
                    alignment: Alignment.center,
                    child: const Text(
                      "拒绝",
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),

              // 分隔线
              Container(
                width: 0.5,
                height: 48,
                color: Colors.grey.shade300,
              ),

              // 同意按钮
              Expanded(
                child: GestureDetector(
                  onTap: onAgree,
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    height: 48,
                    alignment: Alignment.center,
                    child: Text(
                      "同意",
                      style: TextStyle(
                        fontSize: 16,
                        color: ColorsUtil.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
