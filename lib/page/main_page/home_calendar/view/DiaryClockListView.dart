import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/daily_clock_uistate.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/common_dialog.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/dialog/check_in_suc_dialog.dart';
import 'package:flutter_kexue/widget/image_grid_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///日历底部打卡列表
class DiaryClockListView extends StatefulWidget {
  const DiaryClockListView({
    super.key,
    required this.dailyClockList,
    this.onClockItemTap,
    this.onImageTap,
    this.onAddButtonTap,
    this.onDeleteClockTap,
    this.onIncrementCount,
    this.onDecrementCount,
  });

  final List<DailyClockUIState> dailyClockList;

  /// 点击打卡项回调
  final Function(ClockUIState uiState, int groupIndex, int itemIndex)?
      onClockItemTap;

  /// 点击图片回调
  final Function(
          ClockUIState uiState, int imageIndex, int groupIndex, int itemIndex)?
      onImageTap;

  /// 点击添加按钮回调（标题栏的+号）
  final Function(int groupIndex)? onAddButtonTap;

  /// 删除打卡记录回调
  final Function(ClockUIState uiState, int groupIndex, int itemIndex)?
      onDeleteClockTap;

  /// 增加计数回调
  final Function(ClockUIState uiState, int groupIndex, int itemIndex)?
      onIncrementCount;

  /// 减少计数回调
  final Function(ClockUIState uiState, int groupIndex, int itemIndex)?
      onDecrementCount;

  @override
  State<DiaryClockListView> createState() => _DiaryClockListViewState();
}

class _DiaryClockListViewState extends State<DiaryClockListView>
    with TickerProviderStateMixin {
  // 用于跟踪每个分组的展开/收起状态
  final Map<int, bool> _expandedStates = {};

  // 动画控制器
  final Map<int, AnimationController> _animationControllers = {};
  final Map<int, Animation<double>> _animations = {};

  @override
  void initState() {
    super.initState();
    // 初始化动画控制器和状态
    for (int i = 0; i < widget.dailyClockList.length; i++) {
      _expandedStates[i] = true; // 默认展开
      _animationControllers[i] = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      );
      _animations[i] = CurvedAnimation(
        parent: _animationControllers[i]!,
        curve: Curves.easeInOut,
      );
      // 初始状态为展开
      _animationControllers[i]!.value = 1.0;
    }
  }

  @override
  void dispose() {
    // 释放动画控制器
    for (var controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildDiaryClockList(context);
  }

  /// 切换展开/收起状态
  void _toggleExpansion(int index) {
    setState(() {
      final currentState = !(_expandedStates[index] ?? true);
      _expandedStates[index] = currentState;

      if (currentState) {
        // 展开动画
        _animationControllers[index]?.forward();
      } else {
        // 收起动画
        _animationControllers[index]?.reverse();
      }
    });
  }

  /// 构建记录列表
  Widget _buildDiaryClockList(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.dailyClockList.length,
        separatorBuilder: (context, index) => Divider(
          height: 12,
          thickness: 12,
          color: Colors.transparent,
        ),
        itemBuilder: (context, index) {
          final item = widget.dailyClockList[index];
          return Column(
            children: [
              //标题不收起
              Padding(
                padding: const EdgeInsets.only(left: 10, right: 10),
                child: _buildDiaryTitleSection(
                    item.systemUIState.shortNameOrName, item.progress, index),
              ),

              ///可折叠的内容列表，带动画
              AnimatedBuilder(
                animation: _animations[index]!,
                builder: (context, child) {
                  return ClipRect(
                    child: Align(
                      alignment: Alignment.topCenter,
                      heightFactor: _animations[index]?.value,
                      child: child,
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.only(left: 14, right: 10),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: item.list.length,
                    separatorBuilder: (context, listIndex) => const Divider(
                      height: 5,
                      thickness: 5,
                      color: Colors.transparent,
                    ),
                    itemBuilder: (context, listIndex) {
                      return _buildDiaryClockItem(
                          item.list[listIndex], index, listIndex);
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建标题栏
  Widget _buildDiaryTitleSection(String title, double progress, int index) {
    final isExpanded = _expandedStates[index] ?? true;
    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () => _toggleExpansion(index),
            behavior: HitTestBehavior.opaque,
            child: Row(
              children: [
                AnimatedRotation(
                  turns: isExpanded ? 0.25 : 0.75, // 0度到-90度
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
                ),
                const SizedBox(width: 5),
                ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 200.w),
                    child: Text(
                      title,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: TextStyle(
                        fontSize: 12,
                        color: ColorsUtil.textBlack,
                      ),
                    )),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              //进度条
              _buildProgressWidget(progress),
              GestureDetector(
                onTap: () => widget.onAddButtonTap?.call(index),
                child: Icon(
                  Icons.add_box,
                  size: 20,
                  color: Colors.grey.shade400,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  /// 构建进度条
  Widget _buildProgressWidget(double progress) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "$progress%",
          style: TextStyle(
            fontSize: 12,
            color: ColorsUtil.garyB1,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: 67,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10), // 圆角
            child: LinearProgressIndicator(
              value: progress / 100,
              // 设置高度
              minHeight: 5,
              // 进度颜色
              backgroundColor: ColorsUtil.primaryColor20,
              // 背景色
              valueColor:
                  AlwaysStoppedAnimation<Color>(ColorsUtil.primaryColor),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建单个记录项
  Widget _buildDiaryClockItem(
      ClockUIState uiState, int groupIndex, int itemIndex) {
    return GestureDetector(
      onTap: () => widget.onClockItemTap?.call(uiState, groupIndex, itemIndex),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          //左边圆角竖线
                          Container(
                            width: 5,
                            height: 17,
                            decoration: BoxDecoration(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(5)),
                              color: uiState.goalType == TargetType.clock
                                  ? ColorsUtil.primaryColor
                                  : Colors.red,
                            ),
                          ),
                          const SizedBox(width: 8),
                          //标题和内容
                          GestureDetector(
                            onTap: () {
                              Get.toNamed(Routes.planDetails);
                            },
                            child: ConstrainedBox(
                              constraints: BoxConstraints(maxWidth: 200.w),
                              child: Text(
                                uiState.target,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: ColorsUtil.textBlack,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                          Text(
                            uiState.dailyCount > 0
                                ? " x${uiState.dailyCount}"
                                : "",
                            style: TextStyle(
                              fontSize: 14,
                              color: ColorsUtil.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      if (uiState.motivation?.isNotEmpty == true) ...[
                        Padding(
                          padding: const EdgeInsets.only(left: 13),
                          child: ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: 200.w),
                            child: Text(
                              uiState.motivation ?? "",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 12,
                                color: ColorsUtil.garyB1,
                              ),
                            ),
                          ),
                        ),
                      ]
                    ],
                  ),
                ),
                _buildRightContentView(uiState, groupIndex, itemIndex),
              ],
            ),
            const SizedBox(width: 8),
            //图片
            if (uiState.images.isNotEmpty) ...[
              const SizedBox(height: 6),
              Container(
                padding: const EdgeInsets.only(left: 13),
                child: ImageGridWidget(
                    images: uiState.images,
                    onImageTap: (index) {
                      widget.onImageTap
                          ?.call(uiState, index, groupIndex, itemIndex);
                    }),
              ),
            ]
          ],
        ),
      ),
    );
  }

  ///构建右侧内容
  Widget _buildRightContentView(
      ClockUIState uiState, int groupIndex, int itemIndex) {
    //正能量
    if (uiState.goalType == TargetType.clock) {
      return _buildStatusSelector(uiState, groupIndex, itemIndex);
    }
    //持戒
    return _buildCounterWidget(uiState, groupIndex, itemIndex);
  }

  /// 单次计数都选状态
  Widget _buildStatusSelector(
      ClockUIState uiState, int groupIndex, int itemIndex) {
    //两个都相同标识完成
    if (uiState.dailyTotalCount == uiState.dailyCount) {
      return GestureDetector(
        onTap: () => {
          //弹出删除打卡弹窗
          showCommonDialog(CommonDialogConfig(
            hiddenTitle: true,
            content: "是否要删除此打开？",
            positive: '确定',
            onPositive: () {
              //删除此打卡记录
              widget.onDeleteClockTap?.call(uiState, groupIndex, itemIndex);
            },
          ))
        },
        child: Icon(
          Icons.check_box,
          color: ColorsUtil.primaryColor,
          size: 24,
        ),
      );
    }
    //有完成数量但是一次都没有完成
    if (uiState.dailyTotalCount != 0 && uiState.dailyCount == 0) {
      return GestureDetector(
        onTap: () => _handleIncrementCount(uiState, groupIndex, itemIndex),
        child: Icon(
          Icons.check_box_outline_blank,
          color: ColorsUtil.primaryColor,
          size: 24,
        ),
      );
    }
    return GestureDetector(
      onTap: () => _handleIncrementCount(uiState, groupIndex, itemIndex),
      child: Stack(alignment: Alignment.center, children: [
        Icon(
          Icons.check_box_outline_blank,
          color: ColorsUtil.primaryColor,
          size: 24,
        ),
        Text(
          "${uiState.dailyCount.toInt()}",
          style: TextStyle(
            fontSize: 12,
            color: ColorsUtil.primaryColor,
          ),
        ),
      ]),
    );
  }

  ///持戒
  Widget _buildCounterWidget(
      ClockUIState uiState, int groupIndex, int itemIndex) {
    //一次都没有点+
    if (uiState.totalCount != 0 || uiState.nativeCount == 0) {
      return Row(
        spacing: 6,
        mainAxisSize: MainAxisSize.min,
        children: [
          //比如7天内打卡30次
          Text(
            "${uiState.goalDay}天内剩余${uiState.dailyTotalCount}次",
            style: TextStyle(color: ColorsUtil.garyB1, fontSize: 12),
          ),
          _buildIncrementButton(uiState, groupIndex, itemIndex),
        ],
      );
    }

    //没有达到目标，但是又完成了1次，中间显示次数1次，
    if (uiState.nativeCount < uiState.totalCount) {
      return Row(
        spacing: 6,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDecrementButton(uiState, groupIndex, itemIndex),
          Text(
            "${uiState.dailyCount}次",
            style: TextStyle(color: Colors.red, fontSize: 14),
          ),
          _buildIncrementButton(uiState, groupIndex, itemIndex),
        ],
      );
    }

    //达到目标，中间显示1次(破戒)，
    if (uiState.nativeCount >= uiState.totalCount) {
      return Row(
        spacing: 6,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDecrementButton(uiState, groupIndex, itemIndex),
          Text(
            "${uiState.dailyCount}次(破戒)",
            style: TextStyle(color: Colors.red, fontSize: 14),
          ),
          _buildIncrementButton(uiState, groupIndex, itemIndex),
        ],
      );
    }
    return const SizedBox.shrink();
  }

  /// 构建一个+号按钮
  Widget _buildIncrementButton(ClockUIState uiState, int groupIndex, int itemIndex) {
    return GestureDetector(
      onTap: () => _handleIncrementCount(uiState, groupIndex, itemIndex),
      child: Container(
        width: 18,
        height: 18,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.red,
            width: 1,
          ),
        ),
        child: const Icon(
          Icons.add,
          size: 16,
          color: Colors.red,
        ),
      ),
    );
  }

  /// 构建一个-号按钮
  Widget _buildDecrementButton(
      ClockUIState uiState, int groupIndex, int itemIndex) {
    return GestureDetector(
      onTap: () =>
          widget.onDecrementCount?.call(uiState, groupIndex, itemIndex),
      child: Container(
        width: 18,
        height: 18,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.red,
            width: 1,
          ),
        ),
        child: const Icon(
          Icons.remove,
          size: 16,
          color: Colors.red,
        ),
      ),
    );
  }

  /// 处理打卡计数的逻辑
  void _handleIncrementCount(
      ClockUIState uiState, int groupIndex, int itemIndex) {
    widget.onIncrementCount?.call(uiState, groupIndex, itemIndex);
  }

}
