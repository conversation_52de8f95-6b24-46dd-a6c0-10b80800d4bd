import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/record_type.dart';
import 'package:flutter_kexue/page/common_uistate/daily_record_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/record_uistate.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///记录列表
class DiaryRecordListView extends StatefulWidget {
  final List<DailyRecordUIState> recordList;

  /// 图片选择回调
  final Function(int groupIndex, int itemIndex)? onRecordItemPressed;

  const DiaryRecordListView({
    super.key,
    required this.recordList,
    this.onRecordItemPressed,
  });

  @override
  State<DiaryRecordListView> createState() => _DiaryRecordListViewState();
}

class _DiaryRecordListViewState extends State<DiaryRecordListView>
    with TickerProviderStateMixin {
  // 用于跟踪每个分组的展开/收起状态
  final Map<int, bool> _expandedStates = {};

  // 动画控制器
  final Map<int, AnimationController> _animationControllers = {};
  final Map<int, Animation<double>> _animations = {};

  @override
  void initState() {
    super.initState();
    // 初始化动画控制器和状态
    for (int i = 0; i < widget.recordList.length; i++) {
      _expandedStates[i] = true; // 默认展开
      _animationControllers[i] = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      );
      _animations[i] = CurvedAnimation(
        parent: _animationControllers[i]!,
        curve: Curves.easeInOut,
      );
      // 初始状态为展开
      _animationControllers[i]!.value = 1.0;
    }
  }

  @override
  void dispose() {
    // 释放动画控制器
    for (var controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildRecordList(context);
  }

  /// 切换展开/收起状态
  void _toggleExpansion(int index) {
    setState(() {
      final currentState = !(_expandedStates[index] ?? true);
      _expandedStates[index] = currentState;

      if (currentState) {
        // 展开动画
        _animationControllers[index]?.forward();
      } else {
        // 收起动画
        _animationControllers[index]?.reverse();
      }
    });
  }

// 构建记录列表
  Widget _buildRecordList(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.recordList.length,
        separatorBuilder: (context, index) => Divider(
          height: 12,
          thickness: 12,
          color: Colors.transparent,
        ),
        itemBuilder: (context, index) {
          final item = widget.recordList[index];
          return Column(
            children: [
              //标题不收起
              Padding(
                padding: const EdgeInsets.only(left: 10, right: 10),
                child:
                    _buildDiaryTitleSection(item.title, item.progress, index),
              ),

              ///可折叠的内容列表，带动画
              AnimatedBuilder(
                animation: _animations[index]!,
                builder: (context, child) {
                  return ClipRect(
                    child: Align(
                      alignment: Alignment.topCenter,
                      heightFactor: _animations[index]?.value,
                      child: child,
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.only(left: 14, right: 10),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: item.list.length,
                    separatorBuilder: (context, listIndex) => const Divider(
                      height: 5,
                      thickness: 5,
                      color: Colors.transparent,
                    ),
                    itemBuilder: (context, listIndex) {
                      return _buildRecordItem(
                          item.list[listIndex], index, listIndex);
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建标题栏
  Widget _buildDiaryTitleSection(String title, double progress, int index) {
    final isExpanded = _expandedStates[index] ?? true;
    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () => _toggleExpansion(index),
            behavior: HitTestBehavior.opaque,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedRotation(
                  turns: isExpanded ? 0.25 : 0.75, // 0度到-90度
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
                ),
                const SizedBox(width: 5),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: ColorsUtil.textBlack,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => {Get.toNamed(Routes.recordManager)},
            child: Icon(
              Icons.add_box,
              size: 20,
              color: Colors.grey.shade400,
            ),
          )
        ],
      ),
    );
  }

  /// 构建单个记录项
  Widget _buildRecordItem(
      RecordUIState uiState, int groupIndex, int itemIndex) {
    return GestureDetector(
      onTap: () {
        widget.onRecordItemPressed?.call(groupIndex, itemIndex);
      },
      child: Container(
        height: 45,
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(
              color: Color(0xFFEFEFEF),
              width: 1,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 图标
            if (uiState.icon?.isNotEmpty == true) ...[
              SizedBox(
                width: 25,
                height: 25,
                child: Center(
                  child: Text(
                    uiState.icon ?? "",
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
            ] else ...[
              //左边圆角竖线
              Container(
                width: 5,
                height: 17,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                  color: ColorsUtil.gary85,
                ),
              ),
            ],
            const SizedBox(width: 10),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 100.w),
              child: Text(
                uiState.title ?? "阿迪力，和发肯定会发",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
            ),
            Spacer(),
            // 右侧内容 - 剩余空间
            _buildRightContentView(uiState, groupIndex, itemIndex),
          ],
        ),
      ),
    );
  }

  //构建右侧内容
  Widget _buildRightContentView(
      RecordUIState uiState, int groupIndex, int itemIndex) {
    //拍照打卡
    if (uiState.type == RecordType.photo) {
      return _buildTakeMySelf(uiState, groupIndex, itemIndex);
    }

    //显示文本内容
    if (uiState.content?.isNotEmpty == true) {
      return _buildTextContent(uiState.content ?? "");
    }

    return const SizedBox.shrink();
  }

  Widget _buildTakeMySelf(
      RecordUIState uiState, int groupIndex, int itemIndex) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (uiState.image == null) ...[
          Icon(
            Icons.camera_alt,
            size: 24,
            color: Colors.grey.shade400,
          ),
        ] else ...[
          //显示图片有可能是自己选的图片也有可能是网络图片
          Container(
            width: 25,
            height: 25,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.shade400,
                width: 1,
              ),
              borderRadius: const BorderRadius.all(Radius.circular(5)),
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(4)),
              child: _buildImageWidget(uiState),
            ),
          ),
        ],
        const SizedBox(width: 3),
        Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: ColorsUtil.garyE5,
        ),
      ],
    );
  }

  /// 文本内容
  Widget _buildTextContent(String content) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Content部分 - 最大宽度150
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 200.w),
          child: Text(
            content ?? "",
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.gary58,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 3),
        Icon(
          Icons.arrow_forward_ios,
          size: 16.w,
          color: ColorsUtil.garyE5,
        ),
      ],
    );
  }

  /// 构建图片组件，支持本地文件和网络图片
  Widget _buildImageWidget(RecordUIState uiState) {
    if (uiState.image == null || uiState.image!.isEmpty) {
      return Container(
        width: 25.w,
        height: 25.h,
        color: Colors.grey[300],
        child: Icon(
          Icons.image,
          size: 12.w,
          color: Colors.grey[500],
        ),
      );
    }
    // 网络图片
    if (uiState.isNetworkImage) {
      return Image.network(
        uiState.image!,
        width: 25,
        height: 25,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: 25,
            height: 25,
            color: Colors.grey[300],
            child: Center(
              child: SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 1,
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 25,
            height: 25,
            color: Colors.red[100],
            child: Icon(
              Icons.error,
              size: 12,
              color: Colors.red,
            ),
          );
        },
      );
    }

    // 本地文件图片
    if (uiState.isLocalImage) {
      return Image.file(
        File(uiState.image!),
        width: 25,
        height: 25,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 25,
            height: 25,
            color: Colors.orange[100],
            child: Icon(
              Icons.broken_image,
              size: 12,
              color: Colors.orange,
            ),
          );
        },
      );
    }

    // 默认情况
    return Container(
      width: 25,
      height: 25,
      color: Colors.grey[300],
      child: Icon(
        Icons.image,
        size: 12,
        color: Colors.grey[500],
      ),
    );
  }
}
