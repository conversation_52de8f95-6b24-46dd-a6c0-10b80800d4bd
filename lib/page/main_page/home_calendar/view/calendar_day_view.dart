import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/lunar_util.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

///日历底部打卡列表
class CalendarDayView extends StatelessWidget {
  final CalendarDayData dayData;

  const CalendarDayView({
    super.key,
    required this.dayData,
  });

  @override
  Widget build(BuildContext context) {
    // 如果是占位符，返回空容器
    if (dayData.isPlaceholder) {
      return Container(); // 保留占位但不显示内容
    }

    return Container(
      width: double.infinity,
      clipBehavior: Clip.hardEdge,
      margin: const EdgeInsets.symmetric(horizontal: 1),
      decoration: BoxDecoration(
        // 选中状态的背景色
        color: (dayData.isPositive || dayData.isNegative)
            ? dayData.isPositive
                ? const Color(0xFF4ACE7F).withOpacity(0.2)
                : const Color(0xFFF92800).withOpacity(0.2)
            : null,
        // 如果有数据，显示边框
        border: (dayData.isPositive || dayData.isNegative)
            ? Border.all(
                color: dayData.isPositive
                    ? const Color(0xFF4ACE7F)
                    : const Color(0xFFF92800),
                width: 1,
              )
            : null,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 有数据时的显示
              if (dayData.hasListData) ...[
                // 日期显示
                Padding(
                  padding: const EdgeInsets.only(top: 1),
                  child: Text(
                    '${dayData.date.day}',
                    style: TextStyle(
                      fontSize: 15.sp,
                      height: 1, // 设置行高为1
                      fontWeight: FontWeight.w500,
                      color: _getDateTextColor(),
                    ),
                  ),
                ),
                // 数据列表显示区域
                Expanded(
                  child: _buildDataListsArea(dayData),
                ),
              ] else ...[
                // 公历日期
                Padding(
                  padding: const EdgeInsets.only(top: 6),
                  child: Text(
                    '${dayData.date.day}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: _getDateTextColor(),
                    ),
                  ),
                ),
                const SizedBox(height: 2),
                // 农历日期或"今天"
                Text(
                  LunarUtil.getDisplayText(dayData.date,
                      isToday: dayData.isToday),
                  style: TextStyle(
                    fontSize: 10,
                    color: _getLunarTextColor(),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
          //如果有照片固定显示在左上角
          if (dayData.isPhoto) ...[
            Padding(
              padding: const EdgeInsets.only(top: 2, left: 5),
              child: Align(
                alignment: Alignment.topLeft,
                child: Icon(Icons.my_library_books,
                    size: 10, color: ColorsUtil.primaryColor),
              ),
            ),
          ],

          //如果有日记固定显示在右上角
          if (dayData.isDiary) ...[
            Padding(
              padding: const EdgeInsets.only(top: 2, right: 5),
              child: Align(
                alignment: Alignment.topRight,
                child: Icon(Icons.my_library_books, size: 10, color: Colors.grey),
              ),
            )
          ],
        ],
      ),
    );
  }

  /// 获取公历日期文字颜色
  Color _getDateTextColor() {
    if (!dayData.isCurrentMonth) {
      return Colors.grey.withOpacity(0.4);
    }

    if (dayData.isToday) {
      return const Color(0xFF4ACE7F);
    }

    return Colors.black87;
  }

  /// 获取农历文字颜色
  Color _getLunarTextColor() {
    if (!dayData.isCurrentMonth) {
      return Colors.grey.withOpacity(0.3);
    }

    if (dayData.isToday) {
      return const Color(0xFF4ACE7F);
    }

    return Colors.grey;
  }

  /// 构建数据列表区域（固定1/3高度）
  Widget _buildDataListsArea(CalendarDayData dayData) {
    if(dayData.hasListData){
      List<Widget> dataWidgets = [];

      // 按顺序添加有数据的列表，每个占1/3高度
      // 记录列表
      if (dayData.listRecord != null && dayData.listRecord!.isNotEmpty) {
        dataWidgets.add(
          Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.all(0),
              color: ColorsUtil.gary58,
              child: Text(
                dayData.getRecordText,
                maxLines: 1,
                style: TextStyle(
                  height: 1, // 设置行高为1
                  fontSize: 8.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        );
      }

      // 正能量打卡列表
      if (dayData.listPositive != null && dayData.listPositive!.isNotEmpty) {
        dataWidgets.add(
          Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.all(0),
              color: ColorsUtil.primaryColor,
              child: Text(
                dayData.getPositiveText,
                maxLines: 1,
                style: TextStyle(
                  height: 1, // 设置行高为1
                  fontSize: 8.sp,
                  color: ColorsUtil.black14,
                ),
              ),
            ),
          ),
        );
      }

      // 持戒打卡列表
      if (dayData.listNegative != null && dayData.listNegative!.isNotEmpty) {
        dataWidgets.add(
          Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.all(0),
              color: ColorsUtil.red,
              child: Text(
                dayData.getNegativeText,
                maxLines: 1,
                style: TextStyle(
                  height: 1, // 设置行高为1
                  fontSize: 8.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        );
      }

      // 添加空白占位，确保总共有3个部分（每个占1/3）
      while (dataWidgets.length < 3) {
        dataWidgets.add(
          Expanded(flex: 1, child: Container()),
        );
      }

      return Column(children: dataWidgets);
    }else{
      return SizedBox();
    }
  }
}
