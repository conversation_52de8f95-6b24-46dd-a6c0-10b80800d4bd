import 'package:flutter_kexue/data/mock/mock_data.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/daily_clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/daily_record_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/system_uistate.dart';
import 'package:flutter_kexue/utils/clock_list_helper.dart';
import 'package:flutter_kexue/utils/record_list_helper.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_model.dart';
import 'package:get/get.dart';

/// @date 2025/06/24
/// @description HomeCalendar页UI状态
class HomeCalendarUS {
  final weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  final _dataTime = DateTime.now().obs;

  ///大日历数据
  final RxList<CalendarDayData> _datesWithDataList = <CalendarDayData>[
    CalendarDayData(
      date: DateTime(2025, 7, 19),
      isCurrentMonth: true,
      isToday: false,
      listRecord: ['日记', '照片', '照片'],
      listPositive: ['运动', '学习', '早起'],
      listNegative: ['戒糖', '戒熬夜'],
      isPhoto: true,
      isDiary: true,
    ),
    CalendarDayData(
      date: DateTime(2025, 7, 9),
      isCurrentMonth: true,
      isToday: false,
      listRecord: ['记录1', '记录2', '记录3'],
    ),
    CalendarDayData(
      date: DateTime(2025, 7, 11),
      isCurrentMonth: true,
      isToday: false,
      isPositive: true,
      listRecord: ['记录1', '记录2', '记录3'],
    ),
    CalendarDayData(
      date: DateTime(2025, 7, 16),
      isCurrentMonth: true,
      isToday: false,
      listRecord: ['记录1', '记录2', '记录3'],
    ),
    CalendarDayData(
      date: DateTime(2025, 7, 17),
      isCurrentMonth: true,
      isToday: false,
      isPositive: true,
      listPositive: ['运动', '读书', '冥想'],
    ),
    CalendarDayData(
      date: DateTime(2025, 7, 18),
      isCurrentMonth: true,
      isToday: false,
      isNegative: true,
      listNegative: ['戒烟', '戒酒'],
    ),
  ].obs;

  List<CalendarDayData> get datesWithDataList => _datesWithDataList.value;

  setDatesWithDataList(List<CalendarDayData> list) {
    _datesWithDataList.value = list;
    _datesWithDataList.refresh();
  }

  /// 记录列表
  final RxList<DailyRecordUIState> _recordList = <DailyRecordUIState>[
    DailyRecordUIState(
      title: "记录",
      list: MockData.getRecordList(),
    ),
  ].obs;

  List<DailyRecordUIState> get recordList => _recordList.value;

  setRecordList(List<DailyRecordUIState> list) {
    _recordList.value = list;
    _recordList.refresh();
  }

  ///打卡列表
  final RxList<DailyClockUIState> _diaryClockList = <DailyClockUIState>[
    DailyClockUIState(
        systemUIState:
            SystemUIState(id: "1", name: "我正在努力戒色中", shortName: "戒色"),
        progress: 80,
        isExpand: true,
        list: MockData.getClockList())
  ].obs;

  List<DailyClockUIState> get dailyClockList => _diaryClockList.value;

  setDailyClockList(List<DailyClockUIState> list) {
    _diaryClockList.value = list;
    _diaryClockList.refresh();
  }

  /// 选中的日期（DateTime类型，用于日历组件）
  DateTime get selectedDateTime => _dataTime.value;

  get currentMonth => _dataTime.value.month;

  get currentYear => _dataTime.value.year;

  String get selectedDate =>
      "${selectedDateTime.year}年${selectedDateTime.month}月${selectedDateTime.day}日";

  String get selectedMonth =>
      "${selectedDateTime.year}年${selectedDateTime.month}月";

  String get selectedWeek => weekdays[selectedDateTime.weekday % 7];

  //是否显示当月
  bool get isCurrentDay =>
      _dataTime.value.year == DateTime.now().year &&
      _dataTime.value.month == DateTime.now().month &&
      _dataTime.value.day == DateTime.now().day;

  setDataTime(DateTime dateTime) {
    _dataTime.value = dateTime;
    _dataTime.refresh();

    print('选中日期: ${selectedDate}');
  }

  /// 切换到下一个月
  void onNextMonth() {
    final currentDate = _dataTime.value;
    final today = DateTime.now();

    // 计算下一个月的日期
    DateTime nextMonth;
    if (currentDate.month == 12) {
      // 如果是12月，切换到下一年的1月
      nextMonth = DateTime(currentDate.year + 1, 1, 1);
    } else {
      // 否则月份+1
      nextMonth = DateTime(currentDate.year, currentDate.month + 1, 1);
    }

    // 检查是否超过当前月份（不允许切换到未来月份）
    if (nextMonth.year > today.year ||
        (nextMonth.year == today.year && nextMonth.month > today.month)) {
      print('无法切换到未来月份');
      return;
    }

    // 处理选中日期的逻辑
    DateTime selectedDate;
    if (nextMonth.year == today.year && nextMonth.month == today.month) {
      // 如果切换到当前月份，选中今天
      selectedDate = today;
    } else {
      // 否则选中该月的第一天
      selectedDate = nextMonth;
    }

    // 更新日期
    setDataTime(selectedDate);

    print(
        '切换到下一个月: ${selectedDate.year}年${selectedDate.month}月${selectedDate.day}日');
  }

  /// 切换到上一个月
  void onPrevMonth() {
    final currentDate = _dataTime.value;
    final today = DateTime.now();

    // 计算上一个月的日期
    DateTime prevMonth;
    if (currentDate.month == 1) {
      // 如果是1月，切换到上一年的12月
      prevMonth = DateTime(currentDate.year - 1, 12, 1);
    } else {
      // 否则月份-1
      prevMonth = DateTime(currentDate.year, currentDate.month - 1, 1);
    }

    // 处理选中日期的逻辑
    DateTime selectedDate;
    if (prevMonth.year == today.year && prevMonth.month == today.month) {
      // 如果切换到当前月份，选中今天
      selectedDate = today;
    } else {
      // 否则选中该月的第一天
      selectedDate = prevMonth;
    }

    // 更新日期
    setDataTime(selectedDate);

    print(
        '切换到上一个月: ${selectedDate.year}年${selectedDate.month}月${selectedDate.day}日');
  }

  /// 回到今天
  void onBackToToday() {
    final today = DateTime.now();
    setDataTime(today);
    print('已回到今天: ${today.year}年${today.month}月${today.day}日');
  }

  ///更新列表上的图片
  void uploadImageToServer(int groupIndex, int itemIndex, String imagePath) {
    final updatedList = RecordListHelper.updateImage(
        recordList, groupIndex, itemIndex, imagePath);
    _recordList.value = updatedList;
    _recordList.refresh();
  }

  ///更新列表上的内容
  void updateContent(int groupIndex, int itemIndex, String content) {
    final updatedList = RecordListHelper.updateContent(
        recordList, groupIndex, itemIndex, content);
    _recordList.value = updatedList;
    _recordList.refresh();
  }

  /// 更新打卡每日计数
  void updateClockDailyCount(int groupIndex, int itemIndex,{int? dailyCount,int? nativeCount}) {
    final updatedList = ClockListHelper.updateClockDailyCount(
        dailyClockList, groupIndex, itemIndex, dailyCount: dailyCount,nativeCount: nativeCount);
    _diaryClockList.value = updatedList;
    _diaryClockList.refresh();
  }

  /// 更新破戒打卡计数
  void onDecrementCount(int groupIndex, int itemIndex, int newCount) {
    final updatedList = ClockListHelper.updateClockDailyCount(
        dailyClockList, groupIndex, itemIndex,
        nativeCount: newCount);
    _diaryClockList.value = updatedList;
    _diaryClockList.refresh();
  }
}
