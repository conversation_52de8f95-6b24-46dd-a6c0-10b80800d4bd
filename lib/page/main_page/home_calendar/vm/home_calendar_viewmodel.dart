import "package:flutter/cupertino.dart";
import "package:flutter_kexue/data/enums/target_type.dart";
import "package:flutter_kexue/data/enums/record_type.dart";
import "package:flutter_kexue/data/mock/mock_data.dart";
import "package:flutter_kexue/page/common_uistate/clock_uistate.dart";
import "package:flutter_kexue/page/main_page/home_calendar/vm/home_calendar_us.dart";
import "package:flutter_kexue/page/page/diary_details/entity/diary_details_props.dart";
import "package:flutter_kexue/page/page/diary_editor/entity/diary_editor_props.dart";
import "package:flutter_kexue/page/page/image_preview/entity/image_preview_props.dart";
import "package:flutter_kexue/routes/routes.dart";
import "package:flutter_kexue/utils/ui_util/toast_util.dart";
import "package:flutter_kexue/widget/dialog/check_in_suc_dialog.dart";
import "package:flutter_kexue/widget/dialog/image_picker_dialog.dart";
import "package:flutter_kexue/widget/dialog/muile_record_dialog.dart";
import "package:get/get.dart";

/// @date 2025/06/24
/// @description HomeCalendar页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class HomeCalendarViewModel {
  var us = HomeCalendarUS();

  HomeCalendarViewModel() {
    _initializeData();
    fetchData();
  }

  /// 初始化基础数据
  void _initializeData() {
    // 初始化每日记录数据
    // us.setRecordList([]);

    // 初始化每日记录数据
    // us.setDailyClockList([]);
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      //todo 同步获取数据
      // var result = uiRep.getStatus();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      print('获取数据失败: $e');
    }
  }

  /// 处理月份切换
  void onMonthChanged(bool isNext) {
    if (isNext) {
      us.onNextMonth();
    } else {
      us.onPrevMonth();
    }
  }

  /// 处理记录项点击
  void onRecordItemPressed(
      BuildContext context, int groupIndex, int itemIndex) {
    var uiState = us.recordList[groupIndex].list[itemIndex];
    if (uiState.type == RecordType.photo) {
      //图片选择
      if (uiState.image == null) {
        _showImagePicker(groupIndex, itemIndex);
      } else {
        Get.toNamed(
          Routes.imagePreview,
          arguments: ImagePreviewProps(
            images: ['${uiState.image}'],
            initialIndex: 0,
          ),
        );
      }
    } else if (uiState.type == RecordType.diary) {
      var content = uiState.content;
      //日记
      if (content?.isEmpty == true || content == '无') {
        var props = DiaryEditorProps(dateTime: us.selectedDateTime);
        Get.toNamed(Routes.diaryEditor, arguments: props);
      } else {
        //跳转日记详情页面
        var props = DiaryDetailsProps(dateTime: us.selectedDateTime);
        Get.toNamed(Routes.diaryDetails, arguments: props);
      }
    } else if (uiState.type == RecordType.multiple) {
      //多选 - 使用持久化弹窗
      MultipleRecordDialog.show(
        context: context,
        items: MockData.createGridMockData(),
        titleLftText: "管理",
        titleCenterText: "多选记录",
        onTitleLeftTap: () {
          Get.toNamed(Routes.recordMultipleManager);
        },
        onSelected: (selectedItems) {
          var content = selectedItems.map((item) => item.label).join("、");
          us.updateContent(groupIndex, itemIndex, content);
        },
      );
    }
  }

  /// 显示图片选择对话框
  void _showImagePicker(int groupIndex, int itemIndex) {
    ImagePickerDialog.show(
      onImageSelected: (imagePath) {
        // 通知父组件更新图片
        _uploadImage(groupIndex, itemIndex, imagePath);
      },
    );
  }

  /// 上传图片到服务器（暂不实现具体逻辑）
  Future<void> _uploadImage(
      int groupIndex, int itemIndex, String imagePath) async {
    try {
      ToastUtil.showLoading(message: '正在上传图片...');
      // 模拟上传延迟
      await Future.delayed(const Duration(seconds: 1));
      // 这里应该调用实际的上传API
      updateTodayImage(groupIndex, itemIndex, imagePath);
      ToastUtil.showToast('图片上传成功');
    } catch (e) {
      ToastUtil.showToast('图片上传失败');
    } finally {
      ToastUtil.hideLoading();
    }
  }

  updateTodayImage(int groupIndex, int itemIndex, String imagePath) {
    us.uploadImageToServer(groupIndex, itemIndex, imagePath);
  }

  ///打卡记录和破戒点击事件

  /// 处理打卡项点击
  void onClockItemTap(ClockUIState uiState, int groupIndex, int itemIndex) {
    // 跳转到计划详情页面
    Get.toNamed(Routes.planDetails);
  }

  /// 处理图片点击
  void onImageTap(
      ClockUIState uiState, int imageIndex, int groupIndex, int itemIndex) {
    // 跳转到图片预览页面
    if (uiState.images.isNotEmpty && imageIndex < uiState.images.length) {
      Get.toNamed(
        Routes.imagePreview,
        arguments: ImagePreviewProps(
          images: uiState.images,
          initialIndex: imageIndex,
        ),
      );
    }
  }

  /// 处理添加按钮点击（标题栏的+号）,跳转此行动系统的【行动系统详情页】
  void onAddButtonTap(int groupIndex) {
    Get.toNamed(Routes.planDetails);
  }

  /// 处理删除打卡记录
  void onDeleteClockTap(ClockUIState uiState, int groupIndex, int itemIndex) {
    try {
      // 更新UI状态：将dailyCount设为0
      us.updateClockDailyCount(groupIndex, itemIndex, dailyCount: 0);
      ToastUtil.showToast('删除打卡记录成功');
    } catch (e) {
      ToastUtil.showToast('删除失败，请重试');
    }
  }

  /// 处理增加计数
  void onIncrementCount(ClockUIState uiState, int groupIndex, int itemIndex) {
    try {
      if (uiState.goalType == TargetType.clock) {
        var newCount = uiState.dailyCount + 1;
        // 更新UI状态
        us.updateClockDailyCount(groupIndex, itemIndex, dailyCount: newCount);
        //todo 调用接口
        // 检查是否达到目标，如果达到则弹出打卡成功弹窗
        if (newCount >= uiState.dailyTotalCount) {
          // 延迟一下确保UI更新完成后再弹窗
          Future.delayed(const Duration(milliseconds: 100), () {
            _showCheckInSuccessDialog(uiState);
          });
        }
      } else {
        /// 处理破戒计数的逻辑
        var newCount = uiState.nativeCount + 1;
        // 更新UI状态
        us.updateClockDailyCount(groupIndex, itemIndex, nativeCount: newCount);
        //todo 调用接口
        // 检查是否达到目标，如果达到则弹出打卡成功弹窗
        if (newCount >= uiState.totalCount) {
          // 延迟一下确保UI更新完成后再弹窗
          Future.delayed(const Duration(milliseconds: 100), () {
            _showCheckInSuccessDialog(uiState);
          });
        }
      }
    } catch (e) {
      ToastUtil.showToast('操作失败，请重试');
    }
  }

  /// 处理减少计数
  void onDecrementCount(ClockUIState uiState, int groupIndex, int itemIndex) {
    try {
      // 计算新的计数值
      int newCount = uiState.dailyCount - 1;
      // 确保不小于0
      if (newCount >= 0) {
        // 更新UI状态
        us.onDecrementCount(groupIndex, itemIndex, newCount);
        //todo 调用接口
      } else {
        ToastUtil.showToast('计数不能小于0');
      }
    } catch (e) {
      ToastUtil.showToast('操作失败，请重试');
    }
  }

  /// 显示打卡成功弹窗
  void _showCheckInSuccessDialog(ClockUIState uiState) {
    // 根据目标类型显示不同的弹窗
    if (uiState.goalType == TargetType.clock) {
      // 打卡类型：显示打卡成功
      CheckInSuccessDialog.show(
        dateTime: DateTime.now(),
        isPrivate: true,
        onConfirm: () {
          //todo 刷新接口
        },
      );
    } else if (uiState.goalType == TargetType.persist) {
      // 戒断类型：显示破戒提醒
      CheckInSuccessDialog.show(
        dateTime: DateTime.now(),
        isPrivate: false,
        onConfirm: () {
          //todo  刷新接口
        },
      );
    }
  }
}
