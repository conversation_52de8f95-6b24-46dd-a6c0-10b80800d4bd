import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/view/DiaryClockListView.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/view/DiaryRecordListView.dart';
import 'package:flutter_kexue/page/main_page/home_calendar/view/calendar_day_view.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/calendar/td_calendar.dart';
import 'package:get/get.dart';

import 'entity/home_calendar_props.dart';
import 'vm/home_calendar_viewmodel.dart';

// 点击回到今天去掉动画,快速点击时月份切换最小最大限制
// 手动切换月份没有选中当前月份的第一天，帮我实现选中当前月份的今天
// 底部网上滑动赢非选中当前天数的其他布局 只显示选中天一行
/// @date 2025/06/24
/// @param props 页面路由参数
/// @returns
/// @description HomeCalendar页面入口
class HomeCalendarPage extends StatelessWidget {
  HomeCalendarPage({super.key, this.props});

  final HomeCalendarProps? props;
  final HomeCalendarViewModel viewModel = HomeCalendarViewModel();

  @override
  Widget build(BuildContext context) {
    // 设置系统栏样式
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      // 状态栏背景透明
      statusBarIconBrightness: Brightness.dark,
      // 状态栏图标（如电量、信号）为黑色
      statusBarBrightness: Brightness.light,
      // iOS 的状态栏字体颜色模式（可选）
      systemNavigationBarColor: Colors.white,
      // 底部导航栏颜色（可选）
      systemNavigationBarIconBrightness: Brightness.dark, // 底部导航栏图标颜色
    ));

    return Scaffold(
      // 移除默认的 AppBar
      body: Stack(
        children: [
          // 渐变背景层
          Container(
            height: 92, // 设置渐变层的高度为200
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFD3F4EF), // 顶部颜色
                  Color(0xFFF6F6F6), // 底部颜色
                ],
              ),
            ),
          ),

          // 自定义内容层（包含虚拟状态栏高度）
          SafeArea(
            child: Column(
              children: [
                // 标题栏
                _buildCustomAppBar(),
                // 主体内容
                Expanded(child: contentView(context)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        color: Color(0xFFF2F2F5),
        child: Column(
          children: [
            //顶部区banner
            _buildBanner(),
            // 月份选择栏
            _buildMonthSelector(context),
            // 大日历栏
            _buildCalendarArea(),
            Divider(height: 6, color: Color(0xFFF2F2F5), thickness: 6),
            _buildDaySelector(),
            Divider(height: 6, color: Color(0xFFF2F2F5), thickness: 6),
            // 记录列表
            _buildRecordListView(context),

            Divider(height: 8, color: Color(0xFFF2F2F5), thickness: 8),
            // 目标列表
            _buildClockListView()
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      height: 40,
      color: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 14),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧标题
            GestureDetector(
              onTap: () => {Get.toNamed(Routes.test)},
              child: Text(
                "棵学戒社",
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w900,
                  color: ColorsUtil.textBlack,
                ),
              ),
            ),

            // 右侧按钮（统计）
            Row(
              children: [
                // Text(
                //   "统计",
                //   style: TextStyle(
                //     fontSize: 16.sp,
                //     color: ColorsUtil.textBlack,
                //   ),
                // ),
                // const SizedBox(width: 24),
                GestureDetector(
                  onTap: () => Get.toNamed(Routes.statisticsFlow),
                  child: Text(
                    "统计",
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorsUtil.textBlack,
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  ///构建一个高44的banner
  Widget _buildBanner() {
    return Stack(
      children: [
        Container(
          color: Colors.red,
          height: 44,
          width: double.infinity,
          child: Image.network(
              "https://img.17sucai.com/upload/534358/2016-06-13/ca269bfed13507fa8928f57bbff720c7.jpg?x-oss-process=style/ready",
              fit: BoxFit.fill),
        ),
        Container(
            height: 44,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            width: double.infinity,
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "连续持戒第1002天",
                    style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w600),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "最高1002天",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        "今天12.3万人与你同在",
                        style: TextStyle(
                          fontSize: 9,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  )
                ]))
      ],
    );
  }

  /// 构建月份选择栏
  Widget _buildMonthSelector(BuildContext context) {
    return Container(
      height: 46,
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            spacing: 4,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 左箭头
              GestureDetector(
                onTap: () => viewModel.onMonthChanged(false),
                child: const Icon(
                  Icons.chevron_left,
                  size: 24,
                  color: Colors.black54,
                ),
              ),

              Obx(() => Container(
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          viewModel.us.selectedMonth,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(width: 4),
                        Icon(Icons.arrow_drop_down_rounded)
                      ],
                    ),
                  )),

              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: () => viewModel.onMonthChanged(true),
                    child: const Icon(
                      Icons.chevron_right,
                      size: 24,
                      color: Colors.black54,
                    ),
                  )
                ],
              ),
            ],
          ),
          GestureDetector(
            onTap: _handleBackToToday,
            child: Row(
              children: [
                Text(
                  "筛选",
                  style: TextStyle(
                    fontSize: 16,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                Icon(Icons.arrow_drop_down_rounded)
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建日历区域
  Widget _buildCalendarArea() {
    return Obx(() {
      return TDCalendar(
        selectedDate: viewModel.us.selectedDateTime,
        onMonthChanged: (monthFirstDay) {
          // 根据是否为当前月份决定选中的日期
          _handleMonthChangeSelection(monthFirstDay.year, monthFirstDay.month);
        },
        onDateTap: (date) {
          viewModel.us.setDataTime(date);
        },
        datesWithData: viewModel.us.datesWithDataList,
        customDayBuilder: (dayData) {
          return CalendarDayView(dayData: dayData);
        },
      );
    });
  }

  /// 构建日期栏
  Widget _buildDaySelector() {
    return Container(
      height: 44,
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Obx(
            () => Text(
              viewModel.us.selectedDate,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: ColorsUtil.textBlack,
              ),
            ),
          ),

          // 居中按钮（回今天）- 只在非当月时显示
          Obx(() {
            if (!viewModel.us.isCurrentDay) {
              return GestureDetector(
                onTap: _handleBackToToday,
                child: Text(
                  "回今天",
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
              );
            }
            return Container();
          }),
        ],
      ),
    );
  }

  /// 记录
  Widget _buildRecordListView(BuildContext context) {
    return Obx(
      () => DiaryRecordListView(
        recordList: viewModel.us.recordList,
        onRecordItemPressed: (groupIndex, itemIndex) {
          viewModel.onRecordItemPressed(context, groupIndex, itemIndex);
        },
      ),
    );
  }

  Widget _buildClockListView() {
    return Obx(() {
      final clockList = viewModel.us.dailyClockList;
      return DiaryClockListView(
        dailyClockList: clockList,
        onClockItemTap: viewModel.onClockItemTap,
        onImageTap: viewModel.onImageTap,
        onAddButtonTap: viewModel.onAddButtonTap,
        onDeleteClockTap: viewModel.onDeleteClockTap,
        onIncrementCount: viewModel.onIncrementCount,
        onDecrementCount: viewModel.onDecrementCount,
      );
    });
  }

  /// 处理"回到今天"按钮点击事件
  void _handleBackToToday() {
    final today = DateTime.now();
    viewModel.us.setDataTime(today);
  }

  /// 处理月份切换时的日期选中逻辑
  void _handleMonthChangeSelection(int year, int month) {
    final today = DateTime.now();
    final isCurrentMonth = year == today.year && month == today.month;

    DateTime selectedDate;
    if (isCurrentMonth) {
      // 如果是当前月份，选中今天
      selectedDate = today;
    } else {
      // 如果不是当前月份，选中当月第一天
      selectedDate = DateTime(year, month, 1);
    }
    viewModel.us.setDataTime(selectedDate);
  }
}
