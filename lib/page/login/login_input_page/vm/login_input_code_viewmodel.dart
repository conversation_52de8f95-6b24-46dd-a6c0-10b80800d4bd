import "package:flutter/widgets.dart";
import "package:flutter_kexue/data/login/ds/model/param/login_params_model.dart";
import "package:flutter_kexue/data/login/repo/account_repo.dart";
import "package:flutter_kexue/data/login/repo/model/account_biz_model.dart";
import "package:flutter_kexue/data/login/repo/user_repo.dart";
import "package:flutter/material.dart";
import "package:flutter_kexue/page/login/login_input_page/vm/login_input_code_us.dart";
import "package:flutter_kexue/routes/routes.dart";
import "package:flutter_kexue/utils/ui_util/toast_util.dart";
import "package:flutter_kexue/widget/countdown_button.dart";
import "package:flutter_kexue/widget/verification_code_input.dart";
import "package:get/get.dart";

/// @date 2025/06/22
/// @description 验证码输入页面ViewModel
class LoginInputCodeViewModel {
  final _userRepo = UserRepo();
  final _accountRepo = AccountRepo();
  var us = LoginInputCodeUS();
  String? _code = "";

  // 倒计时控制器
  final countdownController = CountdownController();

  // 验证码输入组件引用
  GlobalKey<VerificationCodeInputState>? _codeInputRef;

  setCode(String? code) {
    _code = code;
  }

  /// 设置验证码输入组件引用
  void setCodeInputRef(GlobalKey<VerificationCodeInputState> ref) {
    _codeInputRef = ref;
  }

  /// 处理验证码输入变化
  void onCodeChanged(String code) {
    us.verificationCode = code;
    us.isNextButtonEnabled = code.length == 6;
  }

  /// 清空验证码
  void clearCode() {
    // 清空验证码输入
    _codeInputRef?.currentState?.clear();
    us.verificationCode = '';
    us.isNextButtonEnabled = false;
  }

  /// 处理下一步按钮点击
  Future<void> onNextButtonPressed() async {
    if (!us.isNextButtonEnabled) {
      ToastUtil.showToast("请输入完整的验证码");
      return;
    }
    var request = LoginRequest(tel: us.tel, code: us.verificationCode);
    var result = await _userRepo.login(request);
    if (result.isSuccess) {
      var data = result.data;
      if (data == null) {
        ToastUtil.showToast("登录失败");
        return;
      }
      // 模拟API返回的用户信息
      final userInfo = AccountBizModel(
        userId: data.userId,
        username: data.username,
        phone: us.tel,
        nickname: data.username,
        accessToken: data.token,
        status: 0,
      );
      await _accountRepo.saveUser(userInfo);
      _navigateToMainPage();
    } else {
      ToastUtil.showToast(result.message);
    }
  }

  /// 验证验证码（模拟）
  bool _verifyCode(String code) {
    return code == _code;
  }

  /// 处理验证码错误
  void _handleCodeError() {
    ToastUtil.showToast("验证码不正确");
  }

  /// 跳转到主页面
  void _navigateToMainPage() {
    // 这里应该根据用户设置跳转到对应页面，默认跳转到首页
    Get.offAllNamed(Routes.main);
  }

  /// 重新发送验证码
  Future<bool> onResendCode() async {
    // 这里应该调用发送验证码的接口
    var result = await _userRepo.sendVerificationCode(us.tel);
    if (result.isSuccess) {
      countdownController.startCountdown();
      ToastUtil.showToast("验证码已发送");
      if (result.data != null) {
        _code = result.data ?? "";
        return true;
      }
    } else {
      ToastUtil.showToast(result.message);
    }
    return false;
  }
}
