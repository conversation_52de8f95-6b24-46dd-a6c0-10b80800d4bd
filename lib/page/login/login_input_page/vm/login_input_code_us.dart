import 'package:get/get.dart';

/// @date 2025/06/22
/// @description 验证码输入页面UI状态
class LoginInputCodeUS {

  /// 手机号
  final _tel = "".obs;
  
  /// 验证码
  final _verificationCode = "".obs;
  
  /// 下一步按钮是否可用
  final _isNextButtonEnabled = false.obs;
  
  /// 是否正在倒计时
  final _isCountingDown = true.obs;
  
  /// 倒计时秒数
  final _countdownSeconds = 60.obs;

  String get tel => _tel.value;

  String get verificationCode => _verificationCode.value;

  bool get isNextButtonEnabled => _isNextButtonEnabled.value;

  bool get isCountingDown => _isCountingDown.value;


  set tel(String value) {
    _tel.value = value;
    _tel.refresh();
  }

  set verificationCode(String value) {
    _verificationCode.value = value;
    _verificationCode.refresh();
  }

  set isNextButtonEnabled(bool value) {
    _isNextButtonEnabled.value = value;
    _isNextButtonEnabled.refresh();
  }

  set isCountingDown(bool value) {
    _isCountingDown.value = value;
    _isCountingDown.refresh();
  }

  set countdownSeconds(int value) {
    _countdownSeconds.value = value;
    _countdownSeconds.refresh();
  }
}
