import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/login/login_input_page/entity/login_input_code_props.dart';
import 'package:flutter_kexue/page/login/login_input_page/vm/login_input_code_viewmodel.dart';
import 'package:flutter_kexue/utils/ui_util/button_util.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/countdown_button.dart';
import 'package:flutter_kexue/widget/verification_code_input.dart';
import 'package:get/get.dart';

/// @date 2025/06/22
/// @param props 页面路由参数
/// @returns
/// @description 验证码输入页面入口
class LoginInputCodePage extends StatefulWidget {
  const LoginInputCodePage({super.key, this.props});

  final LoginInputCodeProps? props;

  @override
  State<LoginInputCodePage> createState() => _LoginInputCodePageState();
}

class _LoginInputCodePageState extends State<LoginInputCodePage> {
  final LoginInputCodeViewModel _viewModel = LoginInputCodeViewModel();
  final GlobalKey<VerificationCodeInputState> _codeInputKey = GlobalKey<VerificationCodeInputState>();

  @override
  void initState() {
    super.initState();
    // 从路由参数中获取数据
    final props = widget.props ?? Get.arguments as LoginInputCodeProps?;
    _viewModel.us.tel = props?.tel ?? "";
    _viewModel.setCode(props?.code);
    // 设置验证码输入组件引用
    _viewModel.setCodeInputRef(_codeInputKey);

    // 如果是自动获取验证码，则自动开始倒计时
    if (props?.autoGetCode == true) {
      // 延迟一帧后开始倒计时，确保组件已经构建完成
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _viewModel.countdownController.startCountdown();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Get.back(),
        ),
      ),
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      body: contentView(),
    );
  }

  /// 实际展示的视图
  Widget contentView() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(18, 38, 18, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和副标题
          const Text(
            "输入短信验证码",
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 6),
          // 提示文本
          Obx(
            () => Text(
              "已向手机号 ${_formatPhoneNumber(_viewModel.us.tel)} 发送验证码",
              style: TextStyle(
                fontSize: 12,
                color: ColorsUtil.gary85,
              ),
            ),
          ),

          const SizedBox(height: 40),

          // 验证码输入框
          _buildCodeInputFields(),

          // 重新发送按钮
          _buildResendButton(),

          const SizedBox(height: 16),
          // 下一步按钮
          _buildNextButton(),

          const Spacer(),
        ],
      ),
    );
  }

  /// 构建验证码输入框
  Widget _buildCodeInputFields() {
    return Center(
      child: VerificationCodeInput(
        key: _codeInputKey,
        // 设置为4位验证码
        onChanged: _viewModel.onCodeChanged,
        onCompleted: (code) {
          // 验证码输入完成后自动提交
          _viewModel.onNextButtonPressed();
        },
        textStyle: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        decoration: InputBoxDecoration(
          focusedBorderColor: ColorsUtil.primaryColor,
          borderRadius: 8,
        ),
      ),
    );
  }

  /// 构建单个验证码输入框

  /// 构建重新发送按钮
  Widget _buildResendButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CountdownButton(
          defaultText: "重新发送",
          countdownText: "重新发送 ({count}s)",
          onPressed: _viewModel.onResendCode,
          countdownDuration: 60,
          controller: _viewModel.countdownController,
          textStyle: TextStyle(
            fontSize: 12,
            color: ColorsUtil.garyB1,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建下一步按钮
  Widget _buildNextButton() {
    final isEnabled = _viewModel.us.isNextButtonEnabled;
    final backgroundColor =
        isEnabled ? ColorsUtil.primaryColor : ColorsUtil.loginButtonInactive;

    return ButtonUtil.buildCommonButton(
      text: "下一步",
      onPressed: _viewModel.onNextButtonPressed,
      enabled: isEnabled,
      backgroundColor: backgroundColor,
      height: 40,
      borderRadius: 6,
      fontSize: 14,
    );
  }

  /// 格式化手机号显示
  String _formatPhoneNumber(String phone) {
    if (phone.length >= 11) {
      return '${phone.substring(0, 3)}****${phone.substring(7)}';
    }
    return phone;
  }
}
