import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/page/login/login_page/entity/login_props.dart';
import 'package:flutter_kexue/page/login/login_page/vm/login_viewmodel.dart';
import 'package:flutter_kexue/utils/ui_util/button_util.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

/// @date 2025/06/22
/// @param props 页面路由参数
/// @returns
/// @description Login页面入口
class LoginPage extends StatelessWidget {
  LoginPage({super.key, this.props});
  final LoginViewModel _viewModel = LoginViewModel();
  final LoginProps? props;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      body: contentView(),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Obx(() => Padding(
          padding: const EdgeInsets.fromLTRB(18, 78, 18, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),

              // 标题和副标题
              const Text(
                "登录/注册",
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                "首次登录将自动注册并登录账号",
                style: TextStyle(
                  fontSize: 12,
                  color: ColorsUtil.gary85,
                ),
              ),

              const SizedBox(height: 40),

              // 手机号输入框
              _buildPhoneInputField(),

              const SizedBox(height: 32),

              // 登录按钮
              _buildLoginButton(),

              const SizedBox(height: 17),

              // 协议勾选
              _buildAgreementSection(),

              const Spacer(),

              // 其他登录方式
              // _buildOtherLoginMethods(),

              // 底部
              _buildBottomMethods(),

              const SizedBox(height: 40),
            ],
          ),
        ));
  }

  /// 构建手机号输入框
  Widget _buildPhoneInputField() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        border:
            Border(bottom: BorderSide(color: ColorsUtil.garyB1, width: 0.1)),
      ),
      child: Row(
        children: [
          // +86 前缀
          const Text(
            "+86",
            style: TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
          ),

          // 分隔线
          // Container(
          //   width: 1,
          //   height: 24,
          //   color: Colors.grey.withOpacity(0.3),
          // ),

          // 手机号输入框
          Expanded(
            child: TextField(
              controller: _viewModel.phoneNumberController,
              // 👈 绑
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(11),
              ],
              decoration: InputDecoration(
                hintText: "请输入您的手机号码",
                hintStyle: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade500,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
          ),

          // 清空按钮
          if (_viewModel.us.tel.isNotEmpty)
            GestureDetector(
              onTap: _viewModel.clearPhoneNumber,
              child: Container(
                padding: const EdgeInsets.all(12),
                child: Icon(
                  Icons.clear,
                  size: 20,
                  color: Colors.grey.shade500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建登录按钮
  Widget _buildLoginButton() {
    final isEnabled = _viewModel.us.isLoginButtonEnabled;
    final backgroundColor =
        isEnabled ? ColorsUtil.primaryColor : ColorsUtil.primaryColor20;

    return ButtonUtil.buildCommonButton(
      text: "下一步",
      onPressed: _viewModel.onLoginButtonPressed,
      enabled: isEnabled,
      backgroundColor: backgroundColor,
      height: 40,
      borderRadius: 6,
      fontSize: 14,
    );
  }

  /// 构建协议勾选部分
  Widget _buildAgreementSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () =>
              _viewModel.onAgreementChanged(!_viewModel.us.isAgreementChecked),
          child: Container(
            width: 16,
            height: 16,
            margin: const EdgeInsets.only(top: 2),
            decoration: BoxDecoration(
              border: Border.all(
                color: _viewModel.us.isAgreementChecked
                    ? ColorsUtil.primaryColor
                    : Colors.grey.shade400,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: _viewModel.us.isAgreementChecked
                  ? ColorsUtil.primaryColor
                  : Colors.transparent,
            ),
            child: _viewModel.us.isAgreementChecked
                ? const Icon(
                    Icons.check,
                    size: 14,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 12,
                color: ColorsUtil.gary85,
                height: 1.5,
                fontWeight: FontWeight.w500,
              ),
              children: [
                const TextSpan(text: "已阅读并同意 "),
                TextSpan(
                  text: "《棵学戒社用户协议》",
                  style: TextStyle(
                    color: ColorsUtil.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const TextSpan(text: " 和 "),
                TextSpan(
                  text: "《隐私政策》",
                  style: TextStyle(
                    color: ColorsUtil.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const TextSpan(text: "，允许棵学戒社统一管理本人账号信息"),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建其他登录方式
  Widget _buildBottomMethods() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 20,
          children: [
            _buildBottomButton(
              text: "服务热线",
              onTap: () {},
            ),
            _buildBottomButton(
              text: "监督举报电话",
              onTap: () {},
            ),
            _buildBottomButton(
              text: "资质证照",
              onTap: () {},
            ),
          ],
        ),
      ],
    );
  }

  /// 构建第三方登录按钮
  Widget _buildBottomButton({
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Text(text,
            style: TextStyle(fontSize: 12, color: ColorsUtil.gary85)));
  }
}
