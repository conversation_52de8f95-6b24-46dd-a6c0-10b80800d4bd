import "package:flutter/material.dart";
import "package:flutter_kexue/data/login/repo/user_repo.dart";
import "package:flutter_kexue/page/login/login_input_page/entity/login_input_code_props.dart";
import "package:flutter_kexue/page/main_page/agreement_dialog.dart";
import "package:flutter_kexue/routes/routes.dart";
import "package:flutter_kexue/utils/ui_util/toast_util.dart";
import "package:flutter_kexue/utils/validation_util.dart";
import "package:flutter_smart_dialog/flutter_smart_dialog.dart";
import "package:get/get.dart";

import "login_us.dart";

/// @date 2025/06/22
/// @description Login页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class LoginViewModel {
  final _userRepo = UserRepo();
  final _code = "";
  final phoneNumberController = TextEditingController();
  var us = LoginUS();

  LoginViewModel() {
    phoneNumberController.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    _onPhoneNumberChanged(phoneNumberController.text);
  }

  /// 处理手机号输入
  void _onPhoneNumberChanged(String phone) {
    us.tel = phone;
    us.isPhoneValid = ValidationUtil.isValidPhoneNumber(phone);
    _updateLoginButtonState();
  }

  /// 处理协议勾选状态变化
  void onAgreementChanged(bool checked) {
    us.isAgreementChecked = checked;
    _updateLoginButtonState();
  }

  /// 更新登录按钮状态
  void _updateLoginButtonState() {
    us.isLoginButtonEnabled = us.tel.isNotEmpty && us.isPhoneValid;
  }

  /// 处理登录按钮点击
  void onLoginButtonPressed() {
    // 检查协议是否勾选
    if (!us.isAgreementChecked) {
      _showAgreementDialog();
      FocusManager.instance.primaryFocus?.unfocus();
      return;
    }

    // 检查手机号格式
    if (!us.isPhoneValid) {
      ToastUtil.showToast("请输入正确的手机号");
      return;
    }

    // 发送验证码
    _sendVerificationCode();
  }

  /// 显示协议勾选弹窗
  void _showAgreementDialog() {
    SmartDialog.show(
      alignment: Alignment.center,
      maskColor: Colors.black.withAlpha(125),
      builder: (context) => AgreementDialog(
        onAgree: () {
          SmartDialog.dismiss();
          // 自动勾选协议
          onAgreementChanged(true);
          // 重新尝试登录
          onLoginButtonPressed();
        },
      ),
    );
  }

  /// 发送验证码
  void _sendVerificationCode() async {
    var response = await _userRepo.sendVerificationCode(us.tel);
    if (response.isSuccess) {
      _navigateToVerificationPage();
    } else {}
  }

  /// 跳转到验证码页面
  void _navigateToVerificationPage() {
    final props = LoginInputCodeProps(
      tel: us.tel,
      code: _code,
      autoGetCode: true,
    );

    Get.toNamed(
      Routes.loginInputCode,
      arguments: props,
    );
  }

  /// 清空手机号
  void clearPhoneNumber() {
    phoneNumberController.clear();
    _onPhoneNumberChanged("");
  }
}
