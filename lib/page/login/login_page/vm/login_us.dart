import 'package:get/get.dart';

/// @date 2025/06/22
/// @description Login页UI状态
class LoginUS {

  /// 手机号
  final _tel = "".obs;

  /// 手机号是否有效
  final _isPhoneValid = false.obs;

  /// 是否勾选协议
  final _isAgreementChecked = false.obs;

  /// 登录按钮是否可用
  final _isLoginButtonEnabled = false.obs;

  String get tel => _tel.value;

  bool get isPhoneValid => _isPhoneValid.value;

  bool get isAgreementChecked => _isAgreementChecked.value;

  bool get isLoginButtonEnabled => _isLoginButtonEnabled.value;


  set isPhoneValid(bool value) {
    _isPhoneValid.value = value;
   _isPhoneValid.refresh();
  }

  set isAgreementChecked(bool value) {
    _isAgreementChecked.value = value;
    _isAgreementChecked.refresh();
  }

  set isLoginButtonEnabled(bool value) {
    _isLoginButtonEnabled.value = value;
    _isLoginButtonEnabled.refresh();
  }

  set tel(String value) {
    _tel.value = value;
    _tel.refresh();
  }

}
