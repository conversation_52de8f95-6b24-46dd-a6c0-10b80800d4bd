import 'package:get/get.dart';

class MainUS extends GetxController {
  static MainUS get to => Get.find();
  
  final currentIndex = 0.obs;
  final previousIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();
    resetState();
  }

  void resetState() {
    currentIndex.value = 0;
    previousIndex.value = 0;
  }

  void changeTabIndex(int index) {
    previousIndex.value = currentIndex.value;
    currentIndex.value = index;
  }
} 