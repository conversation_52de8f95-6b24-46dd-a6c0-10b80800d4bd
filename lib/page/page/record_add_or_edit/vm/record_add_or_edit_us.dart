import 'package:get/get.dart';

/// @date 2025/07/07
/// @description 新增记录页面UI状态
class RecordAddOrEditUS {
  /// 记录类型选择状态 (0: 日记, 1: 多选记录)
  final _recordType = 0.obs;

  /// 记录名称
  final _recordName = ''.obs;

  /// 是否是新增
  final _isAdd = true.obs;

  // Getters
  int get recordType => _recordType.value;

  String get recordName => _recordName.value;

  /// 是否为日记类型
  bool get isDiaryType => _recordType.value == 0;

  /// 是否为多选记录类型
  bool get isMultiSelectType => _recordType.value == 1;

  /// 获取记录类型文本
  String get recordTypeText => isDiaryType ? '日记' : '多选记录';

  get isAdd => _isAdd.value;

  // Setters
  setRecordType(int value) {
    _recordType.value = value;
    _recordType.refresh();
  }

  setRecordName(String value) {
    _recordName.value = value;
    _recordName.refresh();
  }

  setIsAdd(bool value) {
    _isAdd.value = value;
    _isAdd.refresh();
  }
}
