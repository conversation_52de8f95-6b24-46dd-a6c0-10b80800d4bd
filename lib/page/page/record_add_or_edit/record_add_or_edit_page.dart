import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/record_lib_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'entity/record_add_or_edit_props.dart';
import 'vm/record_add_or_edit_viewmodel.dart';

/// @date 2025/07/07
/// @param props 页面路由参数
/// @returns
/// @description 新增记录页面
class RecordAddOrEditPage extends StatefulWidget {
  const RecordAddOrEditPage({super.key, this.props});

  final RecordAddOrEditProps? props;

  @override
  State<RecordAddOrEditPage> createState() => _RecordAddOrEditPageState();
}

class _RecordAddOrEditPageState extends State<RecordAddOrEditPage> {
  final RecordAddOrEditViewModel viewModel = RecordAddOrEditViewModel();

  @override
  void initState() {
    super.initState();
    // 处理初始化参数
    final props = widget.props ?? Get.arguments as RecordAddOrEditProps?;
    if (props != null) {
      viewModel.handleInitialParams(
        recordType: props.recordType,
        presetName: props.recordName,
        isAdd: props.isAdd,
      );
    }
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildCustomAppBar(),
      backgroundColor: Colors.white,
      body: _buildBody(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildCustomAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: () => {Get.back()},
      ),
      title: Obx(() => Text(
            viewModel.us.isAdd == true ? '新增记录' : '编辑记录',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: ColorsUtil.textBlack,
            ),
          )),
      centerTitle: true,
      actions: [
        TextButton(
          onPressed: () => viewModel.onSavePressed(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              '保存',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 记录类型选择
          _buildRecordTypeSection(),

          SizedBox(height: 14.h),

          // 记录名称输入
          _buildRecordNameSection(),
        ],
      ),
    );
  }

  /// 构建记录类型选择区域
  Widget _buildRecordTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '记录类型',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: ColorsUtil.textBlack,
          ),
        ),
        SizedBox(height: 12.h),
        Obx(() => Row(
              children: [
                // 日记选项
                _buildRadioOption(
                  title: '日记',
                  isSelected: viewModel.us.isDiaryType,
                  onTap: () => viewModel.onRecordTypeChanged(0),
                ),

                SizedBox(width: 10.w),

                // 多选记录选项
                _buildRadioOption(
                  title: '多选记录',
                  isSelected: viewModel.us.isMultiSelectType,
                  onTap: () => viewModel.onRecordTypeChanged(1),
                ),
              ],
            )),
      ],
    );
  }

  /// 构建单选按钮选项
  Widget _buildRadioOption({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              color: isSelected ? ColorsUtil.primaryColor : Color(0xFFF2F2F2),
            ),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                color: isSelected ? Colors.white : ColorsUtil.textBlack,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建记录名称输入区域
  Widget _buildRecordNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '记录名称',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: ColorsUtil.textBlack,
          ),
        ),
        SizedBox(height: 5.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: TextField(
              controller: viewModel.recordNameController,
              onChanged: viewModel.onRecordNameChanged,
              decoration: InputDecoration(
                hintText: '请输入记录名称',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
                filled: true,
                // 启用填充背景
                fillColor: ColorsUtil.garyF2,
                // 设置填充背景色
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7.r),
                  borderSide: BorderSide(
                    color: ColorsUtil.garyF2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7.r),
                  borderSide: BorderSide(
                    color: ColorsUtil.garyF2,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(7.r),
                  borderSide: BorderSide(
                    color: ColorsUtil.garyF2,
                  ),
                ),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                counterText: '', // 隐藏字符计数器
              ),
              maxLength: 20,
            )),
            SizedBox(width: 12.w), // 添加间距
            GestureDetector(
              onTap: () {
                // 隐藏键盘
                FocusScope.of(context).requestFocus(FocusNode());
                RecordLibDialog.show(
                    context: context,
                    isShowBottomView: false,
                    onConfirm: () => {});
              },
              child: Text(
                "记录库",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            )
          ],
        )
      ],
    );
  }
}
