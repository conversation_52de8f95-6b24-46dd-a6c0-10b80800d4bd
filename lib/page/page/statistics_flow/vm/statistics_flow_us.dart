import 'package:flutter_kexue/page/common_uistate/daily_clock_uistate.dart';
import 'package:get/get.dart';

/// @date 2025/07/05
/// @description StatisticsFlow页UI状态
class StatisticsFlowUS {
  /// 打卡总次数
  final _totalCheckIns = 30000.obs;

  /// 破戒总次数
  final _totalBreaks = 20.obs;

  /// 记录总次数
  final _totalRecords = 0.obs;

  // Getters
  int get totalCheckIns => _totalCheckIns.value;

  int get totalBreaks => _totalBreaks.value;

  int get totalRecords => _totalRecords.value;

  // Setters
  set totalCheckIns(int value) {
    _totalCheckIns.value = value;
    _totalCheckIns.refresh();
  }

  set totalBreaks(int value) {
    _totalBreaks.value = value;
    _totalBreaks.refresh();
  }

  set totalRecords(int value) {
    _totalRecords.value = value;
    _totalRecords.refresh();
  }

  /// 格式化数字显示（如：30000次）
  String formatCount(int count) {
    if (count >= 10000) {
      return "${(count / 10000).toStringAsFixed(1)}万次";
    } else if (count >= 1000) {
      return "${(count / 1000).toStringAsFixed(1)}k次";
    } else {
      return "$count次";
    }
  }

  /// 更新统计数据
  void updateStatistics({
    int? checkIns,
    int? breaks,
    int? records,
  }) {
    if (checkIns != null) totalCheckIns = checkIns;
    if (breaks != null) totalBreaks = breaks;
    if (records != null) totalRecords = records;
  }

  /// 重置统计数据
  void resetStatistics() {
    totalCheckIns = 0;
    totalBreaks = 0;
    totalRecords = 0;
  }

  ///=======================================统计=======================================
  final RxList<DailyClockUIState> _diaryClockList = <DailyClockUIState>[].obs;

  List<DailyClockUIState> get dailyClockList => _diaryClockList.value;

  setDailyClockList(List<DailyClockUIState> list) {
    _diaryClockList.value = list;
    _diaryClockList.refresh();
  }
}
