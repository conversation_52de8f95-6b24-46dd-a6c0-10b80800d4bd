import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/utils/ui_util/appbar_util.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/combined_filter_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/statistics_flow_props.dart';
import 'vm/statistics_flow_viewmodel.dart';

/// @date 2025/07/05
/// @param props 页面路由参数
/// @returns
/// @description StatisticsFlow页面入口
class StatisticsFlowPage extends StatelessWidget {
  StatisticsFlowPage({super.key, this.props});

  final StatisticsFlowProps? props;
  final StatisticsFlowViewModel viewModel = StatisticsFlowViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(title: '流水统计'),
      body: contentView(context),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView(BuildContext context) {
    // 进行事件处理
    // handleStatisticsFlowVMEvent(props.vm)
    return Column(
      children: [
        Divider(
          height: 5,
          color: ColorsUtil.bgColor,
        ),
        CombinedFilterWidget(
          onSelectAll: () {},
          onFilterChanged: (filter) {},
        ),
        Divider(
          height: 10,
          color: ColorsUtil.bgColor,
        ),
        _buildStatisticsView(),
        Expanded(
          child: _buildStatisticsFlowList(context),
        ),
      ],
    );
  }

  /// 构建统计区域
  Widget _buildStatisticsView() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Obx(() => Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            spacing: 5,
            children: [
              _buildStatisticsItem(
                title: "打卡",
                count: viewModel.us.totalCheckIns,
                color: ColorsUtil.primaryColor,
              ),
              _buildStatisticsItem(
                title: "破戒",
                count: viewModel.us.totalBreaks,
                color: ColorsUtil.red,
              ),
              _buildStatisticsItem(
                title: "记录",
                count: viewModel.us.totalRecords,
                color: ColorsUtil.gary58,
              ),
            ],
          )),
    );
  }

  /// 构建单个统计项
  Widget _buildStatisticsItem({
    required String title,
    required int count,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        height: 70.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(7),
          boxShadow: ShadowsUtil.cardShadow,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: color,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              viewModel.us.formatCount(count),
              style: TextStyle(
                fontSize: 18,
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///构建流水列表
  Widget _buildStatisticsFlowList(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: viewModel.us.dailyClockList.length,
        separatorBuilder: (context, index) => Divider(
          height: 0,
          thickness: 0,
          color: Colors.transparent,
        ),
        itemBuilder: (context, index) {
          final item = viewModel.us.dailyClockList[index];
          return Column(
            children: [
              //标题不收起
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16),
                child:
                    _buildItemTitleSection(item.systemUIState.shortNameOrName),
              ),

              ///可折叠的内容列表，带动画
              Container(
                padding: const EdgeInsets.only(left: 10, right: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(7),
                  boxShadow: ShadowsUtil.cardShadow,
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: item.list.length,
                  separatorBuilder: (context, listIndex) => const Divider(
                    height: 5,
                    thickness: 5,
                    color: Colors.transparent,
                  ),
                  itemBuilder: (context, listIndex) {
                    return _buildItemContentSection(item.list[listIndex]);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildItemTitleSection(String? date) {
    return Container(
        height: 37.h,
        alignment: Alignment.centerLeft,
        child: Text("2025年7月2日",
            style: TextStyle(fontSize: 13, color: ColorsUtil.gary85)));
  }

  Widget _buildItemContentSection(ClockUIState uiState) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          Row(
            children: [
              //左边圆角竖线
              Container(
                width: 5,
                height: 17,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                  color: uiState.goalType == TargetType.clock
                      ? ColorsUtil.primaryColor
                      : Colors.red,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  uiState.target ?? "",
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.textBlack,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ///构建一个√的icon
                  _buildRightContentSection(uiState),
                  Icon(
                    Icons.arrow_forward_ios_outlined,
                    color: Colors.grey.shade400,
                    size: 16,
                  ),
                ],
              ),
            ],
          ),
          Container(
            alignment: Alignment.topLeft,
            padding: const EdgeInsets.symmetric(horizontal: 14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //内容
                if (uiState.motivation?.isNotEmpty == true) ...[
                  Text(
                    uiState.motivation ?? "",
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorsUtil.garyB1,
                    ),
                  ),
                ],
                //图片
                if (uiState.images.isNotEmpty) ...[
                  const SizedBox(height: 6),
                  Container(
                    color: Colors.green,
                    height: 60,
                    width: double.infinity,
                  ),
                ]
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildRightContentSection(ClockUIState uiState) {
    if (uiState.goalType == TargetType.clock) {
      if (uiState.dailyTotalCount == uiState.dailyCount) {
        return Icon(
          Icons.check,
          color: ColorsUtil.primaryColor,
          size: 16,
        );
      }
      if (uiState.dailyTotalCount != 0 &&
          uiState.dailyCount < uiState.dailyTotalCount) {
        return Text(
          "+${uiState.dailyCount}",
          style: TextStyle(
            fontSize: 16,
            color: ColorsUtil.primaryColor,
          ),
        );
      }
      return SizedBox.shrink();
    } else {
      if (uiState.dailyTotalCount != 0) {
        if (uiState.dailyCount >= uiState.dailyTotalCount) {
          return Text(
            "+${uiState.dailyCount}(破戒)",
            style: TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
          );
        }
        return Text(
          "+${uiState.dailyCount}",
          style: TextStyle(
            fontSize: 16,
            color: Colors.red,
          ),
        );
      }
      return SizedBox.shrink();
    }
  }
}
