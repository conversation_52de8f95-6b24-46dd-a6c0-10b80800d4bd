import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/multiple_record_edit/entity/multiple_record_edit_props.dart';
import 'package:flutter_kexue/page/page/multiple_record_edit/vm/multiple_record_edit_viewmodel.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/record_lib_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// @date 2025/07/07
/// @param props 页面路由参数
/// @returns
/// @description 新增记录页面
class MultipleRecordEditPage extends StatefulWidget {
  const MultipleRecordEditPage({super.key, this.props});

  final MultipleRecordEditProps? props;

  @override
  State<MultipleRecordEditPage> createState() => _MultipleRecordEditPageState();
}

class _MultipleRecordEditPageState extends State<MultipleRecordEditPage> {
  final MultipleRecordEditViewModel viewModel = MultipleRecordEditViewModel();

  @override
  void initState() {
    super.initState();
    // 处理初始化参数
    final props = widget.props ?? Get.arguments as MultipleRecordEditProps?;
    if (props != null) {
      viewModel.handleInitialParams(
        presetName: props.recordName,
        isAdd: props.isAdd,
      );
    }
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildCustomAppBar(),
      backgroundColor: Colors.white,
      body: _buildBody(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildCustomAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: () => {Get.back()},
      ),
      title: Obx(() => Text(
        viewModel.us.isAdd == true ? '新增记录' : '编辑记录',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      )),
      centerTitle: true,
      actions: [
        TextButton(
          onPressed: () => viewModel.onSavePressed(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              '保存',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 记录名称输入
          _buildRecordNameSection(),
        ],
      ),
    );
  }

  /// 构建记录名称输入区域
  Widget _buildRecordNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '多选记录名称',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: ColorsUtil.textBlack,
          ),
        ),
        SizedBox(height: 5.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: TextField(
                  controller: viewModel.recordNameController,
                  onChanged: viewModel.onRecordNameChanged,
                  decoration: InputDecoration(
                    hintText: '请输入记录名称',
                    hintStyle: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey,
                    ),
                    filled: true,
                    // 启用填充背景
                    fillColor: ColorsUtil.garyF2,
                    // 设置填充背景色
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(7.r),
                      borderSide: BorderSide(
                        color: ColorsUtil.garyF2,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(7.r),
                      borderSide: BorderSide(
                        color: ColorsUtil.garyF2,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(7.r),
                      borderSide: BorderSide(
                        color: ColorsUtil.garyF2,
                      ),
                    ),
                    contentPadding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                    counterText: '', // 隐藏字符计数器
                  ),
                  maxLength: 20,
                )),
            SizedBox(width: 12.w), // 添加间距
            GestureDetector(
              onTap: () {
                RecordLibDialog.show(
                    context: context,
                    isShowBottomView: false,
                    onConfirm: () => {});
              },
              child: Text(
                "记录库",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            )
          ],
        )
      ],
    );
  }
}
