import "package:flutter/widgets.dart";
import "package:flutter_kexue/utils/ui_util/toast_util.dart";
import "package:get/get.dart";
import "multiple_record_edit_us.dart";

/// @date 2025/07/07
/// @description 新增记录页面ViewModel
class MultipleRecordEditViewModel {
  var us = MultipleRecordEditUS();

  /// 记录名称输入控制器
  final TextEditingController recordNameController = TextEditingController();

  MultipleRecordEditViewModel() {
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    // 监听记录名称输入变化
    recordNameController.addListener(() {
      us.setRecordName(recordNameController.text);
    });
  }

  /// 处理记录名称输入
  void onRecordNameChanged(String name) {
    us.setRecordName(name);
  }

  /// 处理保存按钮点击
  void onSavePressed() {
    // 模拟保存逻辑
    _saveRecord();
  }

  /// 保存记录
  void _saveRecord() {
    try {
      // 这里应该调用实际的保存接口
      print('保存记录:');
      print('- 名称: ${us.recordName}');

      // 显示成功提示
      ToastUtil.showToast('记录创建成功');

      // 返回上一页
      Get.back(result: {
        'name': us.recordName,
        'success': true,
      });
    } catch (e) {
      print('保存记录失败: $e');
      ToastUtil.showToast('保存失败，请重试');
    }
  }

  /// 处理页面初始化参数
  void handleInitialParams({
    String? presetName,
    int? recordType,
    bool? isAdd,
  }) {
    us.setRecordName(presetName ?? "");
    us.setIsAdd(isAdd ?? true);
    recordNameController.text = presetName ?? "";
  }

  /// 释放资源
  void dispose() {
    recordNameController.dispose();
  }
}
