/// @date 2025/07/08
/// @description 图片预览页面路由参数
class ImagePreviewProps {
  /// 图片URL列表
  final List<String> images;

  /// 初始显示的图片索引
  final int initialIndex;

  /// 是否显示删除按钮
  final bool showDeleteButton;

  /// 是否显示下载按钮
  final bool showDownloadButton;

  /// 删除回调
  final Function(int index)? onDelete;

  const ImagePreviewProps({
    required this.images,
    this.initialIndex = 0,
    this.showDeleteButton = true,
    this.showDownloadButton = true,
    this.onDelete,
  });
}
