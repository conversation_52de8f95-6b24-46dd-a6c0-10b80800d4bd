import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// @date 2025/07/08
/// @description 图片预览页面UI状态
class ImagePreviewUS {
  
  /// 图片列表
  final _images = <String>[].obs;
  
  /// 当前页面索引
  final _currentIndex = 0.obs;
  
  /// 页面控制器
  late PageController pageController;
  
  /// 是否显示底部信息
  final _showBottomInfo = true.obs;

  // Getters
  List<String> get images => _images;
  int get currentIndex => _currentIndex.value;
  bool get showBottomInfo => _showBottomInfo.value;
  
  /// 获取当前页面显示文本 (1/5)
  String get pageIndicatorText => '${currentIndex + 1}/${images.length}';
  
  /// 是否有上一张
  bool get hasPrevious => currentIndex > 0;
  
  /// 是否有下一张
  bool get hasNext => currentIndex < images.length - 1;

  // Setters
  set currentIndex(int value) {
    if (value >= 0 && value < images.length) {
      _currentIndex.value = value;
      _currentIndex.refresh();
    }
  }

  set showBottomInfo(bool value) {
    _showBottomInfo.value = value;
    _showBottomInfo.refresh();
  }

  /// 初始化数据
  void initData({
    required List<String> imageList,
    int initialIndex = 0,
  }) {
    _images.value = imageList;
    _images.refresh();
    
    currentIndex = initialIndex.clamp(0, imageList.length - 1);
    
    // 初始化页面控制器
    pageController = PageController(initialPage: currentIndex);
  }

  /// 跳转到指定页面
  void jumpToPage(int index) {
    if (index >= 0 && index < images.length) {
      currentIndex = index;
      pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 上一张图片
  void previousImage() {
    if (hasPrevious) {
      jumpToPage(currentIndex - 1);
    }
  }

  /// 下一张图片
  void nextImage() {
    if (hasNext) {
      jumpToPage(currentIndex + 1);
    }
  }

  /// 切换底部信息显示状态
  void toggleBottomInfo() {
    showBottomInfo = !showBottomInfo;
  }

  /// 删除当前图片
  void deleteCurrentImage() {
    if (images.isNotEmpty) {
      final indexToDelete = currentIndex;
      _images.removeAt(indexToDelete);
      _images.refresh();
      
      // 如果删除的是最后一张，调整当前索引
      if (currentIndex >= images.length && images.isNotEmpty) {
        currentIndex = images.length - 1;
      }
      // 如果没有图片了，返回上一页
      if (images.isEmpty) {
        Get.back();
      }
    }
  }

  /// 释放资源
  void dispose() {
    pageController.dispose();
  }
}
