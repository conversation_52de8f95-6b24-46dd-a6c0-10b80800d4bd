import 'package:flutter_kexue/utils/ui_util/common_dialog.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

import 'image_preview_us.dart';

/// @date 2025/07/08
/// @description 图片预览页面ViewModel
class ImagePreviewViewModel {
  var us = ImagePreviewUS();

  /// 外部删除回调
  Function(int index)? _onDeleteCallback;

  ImagePreviewViewModel() {
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    // 可以在这里添加初始化逻辑
  }

  /// 处理页面变化
  void onPageChanged(int index) {
    us.currentIndex = index;
  }

  /// 处理图片点击
  void onImageTap() {
    // 切换底部信息显示状态
    us.toggleBottomInfo();
  }

  /// 处理返回按钮点击
  void onBackPressed() {
    Get.back();
  }

  /// 处理删除按钮点击
  void onDeletePressed() {
    if (us.images.isEmpty) return;
    showCommonDialog(CommonDialogConfig(
      title: '温馨提示',
      content: '确定要删除这张图片吗？',
      negative: '取消',
      positive: '确定',
      onPositive: () {
        _deleteCurrentImage();
      },
    ));
  }

  /// 删除当前图片
  void _deleteCurrentImage() {
    try {
      final currentIndex = us.currentIndex;
      // 如果有外部删除回调，则调用外部回调
      if (_onDeleteCallback != null) {
        _onDeleteCallback?.call(currentIndex);
      }
      us.deleteCurrentImage();
      ToastUtil.showToast('图片已删除');
    } catch (e) {
      ToastUtil.showToast('删除失败');
    }
  }

  /// 处理保存图片
  void onSavePressed() {
    try {
      final currentImageUrl = us.images[us.currentIndex];
      // TODO: 实现保存图片到相册的逻辑
      ToastUtil.showToast('图片已保存');
    } catch (e) {
      ToastUtil.showToast('保存失败');
    }
  }

  /// 处理初始化参数
  void handleInitialParams({
    required List<String> images,
    int initialIndex = 0,
    Function(int index)? onDelete,
  }) {
    _onDeleteCallback = onDelete;
    us.initData(
      imageList: images,
      initialIndex: initialIndex,
    );
  }

  /// 释放资源
  void dispose() {
    us.dispose();
  }
}
