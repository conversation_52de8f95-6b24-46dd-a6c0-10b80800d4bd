import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'entity/image_preview_props.dart';
import 'vm/image_preview_viewmodel.dart';

/// @date 2025/07/08
/// @param props 页面路由参数
/// @returns
/// @description 图片预览页面
class ImagePreviewPage extends StatefulWidget {
  const ImagePreviewPage({super.key, this.props});

  final ImagePreviewProps? props;

  @override
  State<ImagePreviewPage> createState() => _ImagePreviewPageState();
}

class _ImagePreviewPageState extends State<ImagePreviewPage> {
  final ImagePreviewViewModel viewModel = ImagePreviewViewModel();

  @override
  void initState() {
    super.initState();

    // // 设置全屏显示
    // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

    // 处理初始化参数
    final props = widget.props ?? Get.arguments as ImagePreviewProps?;
    if (props != null) {
      viewModel.handleInitialParams(
        images: props.images,
        initialIndex: props.initialIndex,
        onDelete: props.onDelete,
      );
    }
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: null,
      body: Stack(
        children: [
          // 图片预览区域
          _buildImageViewer(),

          // 顶部操作栏
          Align(
            alignment: Alignment.topCenter,
            child: _buildTopBar(),
          ),

          // 底部信息栏
          Align(
            alignment: Alignment.bottomCenter,
            child: _buildBottomBar(),
          ),
        ],
      ),
    );
  }

  /// 构建图片查看器
  Widget _buildImageViewer() {
    return Obx(() => PageView.builder(
          controller: viewModel.us.pageController,
          onPageChanged: viewModel.onPageChanged,
          itemCount: viewModel.us.images.length,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: viewModel.onImageTap,
              child: Center(
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: _buildImage(viewModel.us.images[index]),
                ),
              ),
            );
          },
        ));
  }

  /// 构建图片，可能是本地图片也有可能是网络图片
  Widget _buildImage(String imageUrl) {
    if (imageUrl.startsWith('http')) {
      return Image.network(
        imageUrl,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
              color: Colors.white,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  color: Colors.white54,
                  size: 64.w,
                ),
                SizedBox(height: 16.h),
                Text(
                  '图片加载失败',
                  style: TextStyle(
                    color: Colors.white54,
                    fontSize: 16.sp,
                  ),
                ),
              ],
            ),
          );
        },
      );
    } else {
      return Image.file(
        File(imageUrl),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[200],
            child: Icon(
              Icons.broken_image,
              color: Colors.grey[400],
              size: 24,
            ),
          );
        },
      );
    }
  }

  /// 构建顶部操作栏
  Widget _buildTopBar() {
    return Obx(() => AnimatedOpacity(
          opacity: viewModel.us.showBottomInfo ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Stack(
            children: [
              Container(
                height: 92, // 设置渐变层的高度为200
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.5),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
              SafeArea(
                child: Container(
                  height: 40.h,
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  child: Stack(
                    children: [
                      Align(
                        alignment: Alignment.topLeft,
                        child: IconButton(
                          onPressed: viewModel.onBackPressed,
                          icon: Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                            size: 24.w,
                          ),
                        ),
                      ),
                      // 页面指示器
                      Align(
                        child: Text(
                          viewModel.us.pageIndicatorText,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  /// 构建底部信息栏
  Widget _buildBottomBar() {
    return Obx(() => AnimatedOpacity(
          opacity: viewModel.us.showBottomInfo ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: SafeArea(
            child: Container(
              height: 56.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.5),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // 操作按钮
                  if (widget.props?.showDownloadButton != false)
                    IconButton(
                      onPressed: viewModel.onSavePressed,
                      icon: Icon(
                        Icons.download,
                        color: Colors.white,
                        size: 24.w,
                      ),
                    ),

                  if (widget.props?.showDeleteButton != false)
                    IconButton(
                      onPressed: viewModel.onDeletePressed,
                      icon: Icon(
                        Icons.delete,
                        color: Colors.red,
                        size: 24.w,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ));
  }
}
