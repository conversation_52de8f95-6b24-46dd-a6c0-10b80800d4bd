import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'entity/check_in_record_props.dart';
import 'vm/check_in_record_viewmodel.dart';

/// @date 2025/07/06
/// @param props 页面路由参数
/// @returns
/// @description CheckInRecord页面入口 打卡记录
class CheckInRecordPage extends StatelessWidget {
  CheckInRecordPage({super.key, this.props});

  final CheckInRecordProps? props;
  final CheckInRecordViewModel viewModel = CheckInRecordViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildCustomAppBar(context),
      backgroundColor: Colors.white,
      body: contentView(),
    );
  }

  /// 构建自定义AppBar
  PreferredSizeWidget _buildCustomAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: () => Get.back(),
      ),
      title: Text(
        '',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Icon(Icons.delete_forever, color: ColorsUtil.red, size: 26),
          onPressed: () => Get.back(),
        ),
      ],
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleCheckInRecordVMEvent(props.vm)
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildTitleView(),
          Obx(() => SizedBox(height: viewModel.us.isPositive ? 18 : 12)),
          _buildDateSection(),
          SizedBox(height: 12),
          _buildContent(),
          SizedBox(height: 12),
          _buildImageView()
        ],
      ),
    );
  }

  /// 构建顶部标题
  Widget _buildTitleView() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        //左边圆角竖线
        Container(
          width: 5,
          height: 17,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            color: viewModel.us.isPositive == true
                ? ColorsUtil.primaryColor
                : Colors.red,
          ),
        ),

        const SizedBox(width: 8),

        Expanded(
          child: Obx(
            () => Text(
              "${viewModel.us.title}",
              maxLines: viewModel.us.isPositive ? 1 : 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 16,
                color: ColorsUtil.textBlack,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        )
      ],
    );
  }

  /// 构建日期显示部分
  Widget _buildDateSection({DateTime? dateTime}) {
    final now = dateTime ?? DateTime.now();
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[now.weekday - 1];
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${now.year}年${now.month}月${now.day}日',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
            const SizedBox(width: 10),
            Text(
              weekday,
              style: TextStyle(
                fontSize: 16,
                color: ColorsUtil.garyB1,
              ),
            ),
          ],
        ),
        _buildRightView(),
      ],
    );
  }

  /// 构建日期右边完成状态
  Widget _buildRightView() {
    if (viewModel.us.isComplete) {
      return Icon(Icons.check, color: ColorsUtil.primaryColor, size: 16);
    }

    return Text(
      viewModel.us.getReligiousStr(),
      style: TextStyle(
        fontSize: 16,
        color: viewModel.us.isPositive == true
            ? ColorsUtil.primaryColor
            : Colors.red,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// 构建内容
  Widget _buildContent() {
    return Text(
      viewModel.us.content,
      style: TextStyle(
        fontSize: 14,
        color: ColorsUtil.textBlack,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// 构建图片显示区域
  Widget _buildImageView() {
    return Obx(() {
      final images = viewModel.us.images;

      // 如果没有图片，返回空容器
      if (images.isEmpty) {
        return const SizedBox.shrink();
      }

      return LayoutBuilder(
        builder: (context, constraints) {
          // 计算可用宽度（父容器宽度减去左右间距）
          final availableWidth = constraints.maxWidth;

          // 每行3个图片，图片之间的间隔为5
          final spacing = 5.0;
          final itemsPerRow = 3;

          // 计算每个图片的宽度
          // 总间隔 = (每行图片数量 - 1) * 间隔
          final totalSpacing = (itemsPerRow - 1) * spacing;
          final imageSize = (availableWidth - totalSpacing) / itemsPerRow;

          // 计算需要多少行
          final rowCount = (images.length / itemsPerRow).ceil();

          return Column(
            children: List.generate(rowCount, (rowIndex) {
              return Padding(
                padding: EdgeInsets.only(
                    bottom: rowIndex < rowCount - 1 ? spacing : 0),
                child: _buildImageRow(
                  images: images,
                  rowIndex: rowIndex,
                  itemsPerRow: itemsPerRow,
                  imageSize: imageSize,
                  spacing: spacing,
                ),
              );
            }),
          );
        },
      );
    });
  }

  /// 构建单行图片
  Widget _buildImageRow({
    required List<String> images,
    required int rowIndex,
    required int itemsPerRow,
    required double imageSize,
    required double spacing,
  }) {
    final startIndex = rowIndex * itemsPerRow;
    final endIndex = (startIndex + itemsPerRow).clamp(0, images.length);
    final rowImages = images.sublist(startIndex, endIndex);

    return Row(
      children: List.generate(itemsPerRow, (columnIndex) {
        if (columnIndex < rowImages.length) {
          return Row(
            children: [
              _buildImageItem(
                imageUrl: rowImages[columnIndex],
                size: imageSize,
                index: startIndex + columnIndex,
              ),
              if (columnIndex < itemsPerRow - 1) SizedBox(width: spacing),
            ],
          );
        } else {
          // 占位空间，保持对齐
          return Row(
            children: [
              SizedBox(width: imageSize, height: imageSize),
              if (columnIndex < itemsPerRow - 1) SizedBox(width: spacing),
            ],
          );
        }
      }),
    );
  }

  /// 构建单个图片项
  Widget _buildImageItem({
    required String imageUrl,
    required double size,
    required int index,
  }) {
    return GestureDetector(
      onTap: () => _onImageTap(index),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[200],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            imageUrl,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                width: size,
                height: size,
                color: Colors.grey[200],
                child: Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                    strokeWidth: 2,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: size,
                height: size,
                color: Colors.grey[200],
                child: Icon(
                  Icons.broken_image,
                  color: Colors.grey[400],
                  size: size * 0.3,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 处理图片点击事件
  void _onImageTap(int index) {
    print('点击了第 ${index + 1} 张图片');
    // 这里可以实现图片预览功能
    // 比如：打开图片查看器、全屏显示等
  }
}
