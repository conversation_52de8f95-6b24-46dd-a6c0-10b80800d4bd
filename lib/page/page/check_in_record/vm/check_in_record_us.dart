import 'package:get/get.dart';

/// @date 2025/07/06
/// @description JDetail页UI状态
class CheckInRecordUS {
  final _positive = true.obs;
  final _title = '卡带回家弗兰克联化科技卡速度后方可拉斯东方红'.obs;
  final _content = '咖对话框拉好地方接口拉好地方金坷垃还得发离开家阿萨德话费卡带回家弗兰克联化科技卡速度后方可拉斯东方红'.obs;

  final _count = 1.obs;

  ///是否破戒
  final _religious = true.obs;
  ///是否完成
  final _isComplete = true.obs;

  /// 图片列表
  final _images = <String>[
    'https://img.17sucai.com/upload/534358/2016-06-13/ca269bfed13507fa8928f57bbff720c7.jpg?x-oss-process=style/ready',
    'https://img.17sucai.com/upload/534358/2016-06-13/c31e77614794ea676993340c088dd12b.jpg?x-oss-process=style/ready',
    'https://img.17sucai.com/upload/534358/2016-06-12/5717f32fe8ed1497863c6968d88a65b0.jpg?x-oss-process=style/lessen',
    'https://img.17sucai.com/upload/534358/2016-06-13/ca269bfed13507fa8928f57bbff720c7.jpg?x-oss-process=style/ready',
    'https://img.17sucai.com/upload/534358/2016-06-13/ca269bfed13507fa8928f57bbff720c7.jpg?x-oss-process=style/ready',
    'https://img.17sucai.com/upload/534358/2016-06-13/ca269bfed13507fa8928f57bbff720c7.jpg?x-oss-process=style/ready',
    'https://img.17sucai.com/upload/534358/2016-06-13/ca269bfed13507fa8928f57bbff720c7.jpg?x-oss-process=style/ready',
  ].obs;

  get isPositive => _positive.value;

  get title => _title.value;
  get content => _content.value;

  get count => _count.value;

  get religious => _religious.value;

  get isComplete => _isComplete.value;

  List<String> get images => _images;

  String getReligiousStr() {

    if (_religious.value) {
      return "+${_count.value}(破戒)";
    } else {
      return "+${_count.value}";
    }
  }
}
