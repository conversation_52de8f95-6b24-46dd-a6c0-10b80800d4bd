import "package:flutter/material.dart";
import "package:flutter_kexue/page/page/diary_editor/vm/protocol/diary_editor_ui_state.dart";
import "package:flutter_kexue/widget/upload/td_upload.dart";
import "package:get/get.dart";

/// @date 2025/06/28
/// @description DiaryEditor页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class DiaryEditorViewModel extends GetxController {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = DiaryEditorUIState().obs;

  final List<TDUploadFile> files2 = [
    TDUploadFile(
        key: 1,
        remotePath: 'https://tdesign.gtimg.com/demo/images/example1.png'),
    TDUploadFile(
        key: 2,
        remotePath: 'https://tdesign.gtimg.com/demo/images/example2.png'),
    TDUploadFile(
        key: 3,
        remotePath: 'https://tdesign.gtimg.com/demo/images/example3.png'),
  ];
  final List<TDUploadFile> files3 = [
    TDUploadFile(
        key: 1,
        status: TDUploadFileStatus.loading,
        loadingText: '上传中...',
        remotePath: 'https://tdesign.gtimg.com/demo/images/example1.png'),
    TDUploadFile(
        key: 2,
        status: TDUploadFileStatus.loading,
        progress: 68,
        remotePath: 'https://tdesign.gtimg.com/demo/images/example1.png'),
  ];
  final List<TDUploadFile> files4 = [
    TDUploadFile(
        key: 1,
        status: TDUploadFileStatus.retry,
        retryText: '重新上传',
        remotePath: 'https://tdesign.gtimg.com/demo/images/example1.png'),
  ];
  final List<TDUploadFile> files5 = [
    TDUploadFile(
        key: 1,
        status: TDUploadFileStatus.error,
        errorText: '上传失败',
        remotePath: 'https://tdesign.gtimg.com/demo/images/example4.png'),
  ];

  // 文本编辑控制器
  final TextEditingController textController = TextEditingController();

  // 上传的文件列表
  var uploadedFiles = <TDUploadFile>[].obs;

  // 选中的天气
  var selectedWeather = ''.obs;

  DiaryEditorViewModel() {
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.data = "failed";
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState() {}

  /// 处理保存按钮点击
  void onSavePressed() {
    final content = textController.text.trim();
    if (content.isEmpty && uploadedFiles.isEmpty) {
      Get.snackbar('提示', '请输入日记内容或添加图片');
      return;
    }

    print('保存日记: $content');
    print('图片数量: ${uploadedFiles.length}');
    print('天气: ${selectedWeather.value}');

    // TODO: 实现保存逻辑
    Get.snackbar('成功', '日记保存成功');
    Get.back();
  }

  /// 处理天气选择
  void onWeatherPressed() {
    final weatherOptions = ['晴天', '多云', '阴天', '雨天', '雪天'];

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择天气',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            ...weatherOptions.map((weather) => ListTile(
                  title: Text(weather),
                  onTap: () {
                    selectedWeather.value = weather;
                    Get.back();
                  },
                )),
          ],
        ),
      ),
    );
  }

  /// 处理文本变化
  void onTextChanged(String value) {
    // 可以在这里处理文本变化逻辑
    print('文本变化: $value');
  }

  /// 处理添加图片
  void onAddImagePressed() {
    if (uploadedFiles.length >= 9) {
      Get.snackbar('提示', '最多只能上传9张图片');
      return;
    }

    // 模拟添加一张图片
    final newFile = TDUploadFile(
      key: uploadedFiles.length + 1,
      remotePath:
          'https://tdesign.gtimg.com/demo/images/example${(uploadedFiles.length % 3) + 1}.png',
      status: TDUploadFileStatus.success,
    );

    uploadedFiles.add(newFile);
    print('添加图片: ${newFile.remotePath}');
  }

  /// 处理删除图片
  void onRemoveImage(TDUploadFile file) {
    uploadedFiles.remove(file);
    print('删除图片: ${file.remotePath}');
  }

  @override
  void onClose() {
    textController.dispose();
    super.onClose();
  }
}
