import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/common_dialog.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:flutter_kexue/widget/dialog/image_picker_dialog.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_kexue/widget/upload/td_upload_h.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/diary_editor_props.dart';
import 'vm/diary_editor_viewmodel.dart';

/// 日记编辑页面
class DiaryEditorPage extends StatefulWidget {
   DiaryEditorPage({super.key,  DiaryEditorProps? props})
      : props = props ?? Get.arguments as DiaryEditorProps?;
  final DiaryEditorProps? props;

  @override
  State<DiaryEditorPage> createState() => _DiaryEditorPageState();
}

class _DiaryEditorPageState extends State<DiaryEditorPage> {
  final DiaryEditorViewModel viewModel = DiaryEditorViewModel();
  final FocusNode _textFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

  }

  void onItemClick(int key) {}

  void onCancel() {}

  void onValueChanged(List<TDUploadFile> value, TDUploadType event) {
    // 创建新的列表副本进行操作，而不是直接修改viewModel中的列表
    final List<TDUploadFile> newFiles = List.from(viewModel.uploadedFiles);

    switch (event) {
      case TDUploadType.add:
        newFiles.addAll(value);
        break;
      case TDUploadType.remove:
        newFiles.removeWhere((element) => element.key == value[0].key);
        break;
      case TDUploadType.replace:
        final firstReplaceFile = value.first;
        final index =
            newFiles.indexWhere((file) => file.key == firstReplaceFile.key);
        if (index != -1) {
          newFiles[index] = firstReplaceFile;
        }
        break;
    }

    // 替换整个列表而不是修改现有列表
    viewModel.uploadedFiles.assignAll(newFiles);
  }

  /// 多选图片回调
  void _onImagesSelected(List<String> imagePaths) {
    // 获取当前已有的图片路径
    final currentPaths = _getCurrentImagePaths();

    // 找出新添加的图片
    final newPaths =
        imagePaths.where((path) => !currentPaths.contains(path)).toList();

    // 找出被移除的图片
    final removedPaths =
        currentPaths.where((path) => !imagePaths.contains(path)).toList();

    // 处理移除的图片
    for (final removedPath in removedPaths) {
      final fileToRemove = viewModel.uploadedFiles.firstWhere(
        (file) => file.file?.path == removedPath,
        orElse: () => TDUploadFile(key: -1, status: TDUploadFileStatus.success),
      );
      if (fileToRemove.key != -1) {
        onValueChanged([fileToRemove], TDUploadType.remove);
      }
    }

    // 处理新添加的图片
    for (final newPath in newPaths) {
      // 检查是否达到最大限制
      if (viewModel.uploadedFiles.length >= 9) {
        ToastUtil.showToast('最多只能上传9张图片');
        break;
      }

      final newFile = TDUploadFile(
        key: DateTime.now().millisecondsSinceEpoch + newPaths.indexOf(newPath),
        file: File(newPath),
        status: TDUploadFileStatus.success,
      );

      onValueChanged([newFile], TDUploadType.add);
    }

    // 显示结果提示
    if (newPaths.isNotEmpty || removedPaths.isNotEmpty) {
      final message = [];
      if (newPaths.isNotEmpty) message.add('添加${newPaths.length}张');
      if (removedPaths.isNotEmpty) message.add('移除${removedPaths.length}张');
      ToastUtil.showToast('图片更新成功：${message.join('，')}');
    }
  }

  /// 获取当前已有的图片路径列表
  List<String> _getCurrentImagePaths() {
    return viewModel.uploadedFiles
        .where((file) => file.file != null)
        .map((file) => file.file!.path)
        .toList();
  }

  /// 关闭键盘
  void _dismissKeyboard() {
    if (_textFocusNode.hasFocus) {
      _textFocusNode.unfocus();
    }
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true, // 键盘弹出时调整布局
      appBar: _buildCustomAppBar(context),
      body: Column(
        children: [
          // 主要内容区域
          Expanded(
            child: contentView(context),
          ),
          // 底部栏（会被键盘顶起）
          _buildBottomBar(context),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _textFocusNode.dispose();
    super.dispose();
  }

  /// 构建自定义AppBar
  PreferredSizeWidget _buildCustomAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: (){
          showCommonDialog(CommonDialogConfig(
            hiddenTitle: true,
            content: '退出编辑不会保存已填写内容，确认退出吗？',
            negative: '取消',
            positive: '确定',
            onPositive: () {
              Get.back();
            },
          ));
        },
      ),
      title: Text(
        '日记',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      ),
      centerTitle: true,
      actions: [
        TextButton(
          onPressed: () => viewModel.onSavePressed(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              '保存',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20, top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期显示
          _buildDateSection(),

          const SizedBox(height: 20),

          // 文本编辑区域
          _buildTextEditor(),
        ],
      ),
    );
  }

  /// 构建日期显示部分
  Widget _buildDateSection() {
    final now = widget.props?.dateTime ?? DateTime.now();
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[now.weekday - 1];

    return Row(
      children: [
        Text(
          '${now.year}年${now.month}月${now.day}日',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ColorsUtil.textBlack,
          ),
        ),
        const SizedBox(width: 10),
        Text(
          weekday,
          style: TextStyle(
            fontSize: 16,
            color: ColorsUtil.garyB8,
          ),
        ),
      ],
    );
  }

  /// 构建文本编辑器
  Widget _buildTextEditor() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 120),
      alignment: Alignment.topLeft,
      child: TextField(
        controller: viewModel.textController,
        focusNode: _textFocusNode,
        maxLines: null,
        keyboardType: TextInputType.multiline,
        decoration: InputDecoration(
          hintText: '这一刻的想法...',
          hintStyle: TextStyle(
            fontSize: 14,
            color: ColorsUtil.garyB8,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(0),
          isDense: true, // 减少默认的内部间距
        ),
        style: TextStyle(
          fontSize: 16,
          color: ColorsUtil.textBlack,
          height: 1.4, // 设置为1.0以减少行高
        ),
        onChanged: (value) => viewModel.onTextChanged(value),
      ),
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    return SafeArea(
      top: false, // 不需要顶部安全区域
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHorizontalImageUpload(),
          SizedBox(height: 2),
          _buildImageView(context),
        ],
      ),
    );
  }

  /// 构建横向滑动图片上传区域
  Widget _buildHorizontalImageUpload() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12.w),
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          // 获取父容器的宽度
          final maxWidth = constraints.maxWidth;
          // 计算每个上传控件的宽度（显示3个，留出间距）
          final itemWidth = (maxWidth - 12.w - 5.w * 10) / 6; // 减去间距和边距
          return Obx(() {
            return TDUploadH(
              files: viewModel.uploadedFiles.value,
              max: 9,
              height: itemWidth,
              spacing: 10,
              showAddButton: false,
              enablePreview: true,
              canDelete: true,
              canDownload: true,
              onCancel: onCancel,
              onError: (error) => debugPrint('Upload error: $error'),
              onValidate: (error) => debugPrint('Validation error: $error'),
              onMaxLimitReached: () {
                ToastUtil.showToast('最多只能上传9张图片');
              },
              onChange: (fileList, type) => onValueChanged(fileList, type),
            );
          });
        },
      ),
    );
  }

  Widget _buildImageView(BuildContext context) {
    // 底部提示区域
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      color: ColorsUtil.garyF5,
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              // 关闭键盘
              _dismissKeyboard();

              // 显示图片选择对话框（多选模式）
              ImagePickerDialog.showMultiple(
                context: context,
                selectedImages: _getCurrentImagePaths(),
                maxCount: 9-viewModel.uploadedFiles.length,
                onImagesSelected: _onImagesSelected,
              );
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.image_outlined,
                  size: 23.sp,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 8.w),
                Text(
                  '图片',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.garyB8,
                  ),
                )
              ],
            ),
          ),
          Spacer(),
          Obx(() {
            return Text(
              '${viewModel.uploadedFiles.length}/9',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[500],
              ),
            );
          }),
          SizedBox(width: 8.w),
          Icon(
            Icons.visibility_outlined,
            size: 16.sp,
            color: Colors.grey[500],
          ),
          SizedBox(width: 4.w),
          Text(
            '自己可见',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
