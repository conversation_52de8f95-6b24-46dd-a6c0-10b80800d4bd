import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/multiple_record_manager_props.dart';
import 'vm/multiple_record_manager_viewmodel.dart';

/// 多选记录管里
class MultipleRecordManagerPage extends StatelessWidget {
  MultipleRecordManagerPage({super.key, this.props});

  final MultipleRecordManagerProps? props;
  final MultipleRecordManagerViewModel viewModel =
      MultipleRecordManagerViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildCustomAppBar(context),
      backgroundColor: Colors.white,
      body: contentView(),
    );
  }

  /// 构建自定义AppBar（复用日记编辑页面样式）
  PreferredSizeWidget _buildCustomAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: () => Get.back(),
      ),
      title: Text(
        '多选记录管理',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      ),
      centerTitle: true,
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Column(
      children: [
        Divider(height: 6, color: Color(0xFFF5F5F6), thickness: 6),
        SizedBox(height: 8),
        // 添加
        _buildAddSection(),

        SizedBox(height: 8),

        Expanded(
          child: Obx(()=>_buildListView()),
        ),
      ],
    );
  }

  Widget _buildAddSection() {
    return GestureDetector(
      onTap: () => {Get.toNamed(Routes.recordMultipleEdit)},
      child: Container(
        height: 42,
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(horizontal: 13),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Color(0xFFCECECE),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.add, color: ColorsUtil.textBlack, size: 25),
            Text("添加",
                style: TextStyle(fontSize: 15, color: ColorsUtil.textBlack)),
          ],
        ),
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: viewModel.us.recordList.length,
      itemBuilder: (BuildContext context, int index) {
        return _buildItemView(viewModel.us.recordList[index], index);
      },
    );
  }

  /// 构建单个习惯项
  Widget _buildItemView(TDActionSheetItem state, int index) {
    return GestureDetector(
      onTap: () => {},
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFE0E0E0),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                state.label,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.textBlack,
                ),
              ),
            ),

            _buildHabitRightContent(),
          ],
        ),
      ),
    );
  }

  /// 构建习惯右侧内容
  Widget _buildHabitRightContent() {
    // 编辑模式显示三根横线
    return Row(
      spacing: 8,
      children: [
        Icon(
          Icons.arrow_forward_ios,
          size: 20,
          color: Colors.grey.shade400,
        ),
        Icon(
          Icons.drag_handle,
          color: Colors.grey.shade400,
          size: 20,
        ),
      ],
    );
  }
}
