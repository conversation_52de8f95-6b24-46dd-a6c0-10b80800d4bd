import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/plan_common_widget.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/dialog/daily_count_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'vm/plan_add_goal_viewmodel.dart';

/// 持戒扩展页面
extension PlanPersistPageExtension on State {
  /// 构建持戒配置
  Widget buildAbstinenceConfig(PlanAddGoalViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 累计几次完成
          Row(
            children: [
              Text(
                '累计几次为破戒',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              Obx(() => buildCounterWidget(
                    '${viewModel.us.totalCount}次',
                    viewModel.us.decreaseTotalCount,
                    viewModel.us.increaseTotalCount,
                  )),
            ],
          ),

          SizedBox(height: 12.h),

          // 累计次数真实周期
          Text(
            '累计次数重置天数',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: ColorsUtil.textBlack,
            ),
          ),

          SizedBox(height: 6.h),

          Row(
            spacing: 10.w,
            children: [
              Obx(() =>
                  _buildPeriodButton('无限期', viewModel.us.totalPeriod == -1, () {
                    viewModel.onTotalPeriodSelected(-1);
                  })),
              Obx(() =>
                  _buildPeriodButton('7天', viewModel.us.totalPeriod == 7, () {
                    viewModel.onTotalPeriodSelected(7);
                  })),
              Obx(() =>
                  _buildPeriodButton('30天', viewModel.us.totalPeriod == 30, () {
                    viewModel.onTotalPeriodSelected(30);
                  })),
              Obx(() => _buildPeriodButton(
                      '100天', viewModel.us.totalPeriod == 100, () {
                    viewModel.onTotalPeriodSelected(100);
                  })),
              Spacer(),
              GestureDetector(
                onTap: () {
                  DailyCountDialog.show(
                    context: context,
                    targetType: TargetType.persist,
                    initialCount: viewModel.us.totalPeriod == -1
                        ? 100
                        : viewModel.us.totalPeriod,
                    totalPeriod: viewModel.us.totalCount,
                    onConfirm: (count) {
                      viewModel.onCustomTotalPeriod(count);
                    },
                  );
                },
                behavior: HitTestBehavior.opaque,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    //选中天数这里需要显示
                    Obx(
                      () => Text(
                        viewModel.us.totalPeriod == -1
                            ? '无限期'
                            : '${viewModel.us.totalPeriod}天',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: ColorsUtil.textBlack,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Icon(
                      Icons.edit,
                      size: 20,
                      color: Colors.grey.shade400,
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  /// 构建周期按钮
  Widget _buildPeriodButton(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        constraints: BoxConstraints(minWidth: 54.w),
        decoration: BoxDecoration(
          color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.garyF2,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              color: isSelected ? Colors.white : ColorsUtil.textBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
