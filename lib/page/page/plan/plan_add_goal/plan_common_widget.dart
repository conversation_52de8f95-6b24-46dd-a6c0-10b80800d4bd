import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/vm/plan_add_goal_viewmodel.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

extension PlanCommonWidget on State {
  /// 构建计数器组件
  Widget buildCounterWidget(
      String value, VoidCallback onDecrease, VoidCallback onIncrease) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: onDecrease,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: ColorsUtil.garyF2,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: const Icon(Icons.remove, size: 16),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: ColorsUtil.textBlack,
            ),
          ),
        ),
        GestureDetector(
          onTap: onIncrease,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: ColorsUtil.garyF2,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: const Icon(Icons.add, size: 16),
          ),
        ),
      ],
    );
  }

  /// 构建目标激励语模块
  Widget buildMotivationSection(PlanAddGoalViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '目标激励语',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: viewModel.onGenerateMotivationPressed,
                child: Text(
                  '随机生成',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 6.h),

          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF0F0F0),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: TextField(
              controller: viewModel.us.motivationController,
              maxLines: null,
              // 支持多行输入，null表示自动增长
              inputFormatters: [
                LengthLimitingTextInputFormatter(100), // 最多5位数字
              ],
              decoration: InputDecoration(
                hintText: '输入激励语名称',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
                contentPadding:
                EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                isDense: true,
                border: InputBorder.none,
              ),
              style: TextStyle(fontSize: 14.sp, color: ColorsUtil.textBlack),
            ),
          ),

          SizedBox(height: 20.h),

          // 每日完成时弹出目标导语开关
          Row(
            children: [
              Text(
                '自动弹出打卡日志',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              Obx(() => Switch(
                value: viewModel.us.logSwitch,
                onChanged: (value) {
                  viewModel.us.setLogSwitch(value);
                },
                activeColor: ColorsUtil.primaryColor,
              )),
            ],
          ),
        ],
      ),
    );
  }

}
