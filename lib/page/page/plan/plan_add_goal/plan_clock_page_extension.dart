import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/plan_common_widget.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/dialog/daily_count_dialog.dart';
import 'package:flutter_kexue/widget/picker/td_picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'vm/plan_add_goal_viewmodel.dart';

/// 打卡扩展页面
extension PlanClockPageExtension on State {
  /// 构建目标天数模块
  Widget buildGoalDaysSection(PlanAddGoalViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '目标天数',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: ColorsUtil.textBlack,
            ),
          ),

          SizedBox(height: 12.h),

          // 天数选择按钮
          Row(
            spacing: 5.w,
            children: [
              Obx(() => buildDaysButton(
                    7,
                    viewModel.us.targetDay == 7,
                    () {
                      viewModel.onCustomDaysChanged(7);
                    },
                  )),
              Obx(() => buildDaysButton(
                    30,
                    viewModel.us.targetDay == 30,
                    () {
                      viewModel.onCustomDaysChanged(30);
                    },
                  )),
              Obx(() => buildDaysButton(
                    100,
                    viewModel.us.targetDay == 100,
                    () {
                      viewModel.onCustomDaysChanged(100);
                    },
                  )),
              Obx(() => buildDaysButton(
                    -1,
                    viewModel.us.targetDay == -1,
                    () {
                      viewModel.onCustomDaysChanged(-1);
                    },
                  )),
              Spacer(),
              // 自定义天数输入框
              GestureDetector(
                onTap: () {
                  DailyCountDialog.show(
                    context: context,
                    targetType: TargetType.clock,
                    initialCount: viewModel.us.targetDay == -1
                        ? 100
                        : viewModel.us.targetDay,
                    onConfirm: (count) {
                      viewModel.onCustomDaysChanged(count);
                    },
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    //选中天数这里需要显示
                    Obx(
                      () => Text(
                        viewModel.us.targetDay == -1
                            ? '无限期'
                            : '${viewModel.us.targetDay}天',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: ColorsUtil.textBlack,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Icon(
                      Icons.edit,
                      size: 20,
                      color: Colors.grey.shade400,
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // 根据选择显示不同的时间信息
          Obx(() => buildTimeInfo(viewModel)),

          SizedBox(height: 16.h),

          // 打卡频率
          buildCheckInFrequencySection(viewModel),
        ],
      ),
    );
  }

  /// 构建天数按钮
  Widget buildDaysButton(int day, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        constraints: BoxConstraints(minWidth: 54.w),
        decoration: BoxDecoration(
          color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.garyF2,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Center(
          child: Text(
            day == -1 ? '无限期' : '$day天',
            style: TextStyle(
              fontSize: 12.sp,
              color: isSelected ? Colors.white : ColorsUtil.textBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建时间信息
  Widget buildTimeInfo(PlanAddGoalViewModel viewModel) {
    String formatDate(DateTime date) {
      return '${date.year}年${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
    }

    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '开始时间',
              style: TextStyle(
                fontSize: 12.sp,
                color: ColorsUtil.garyB1,
              ),
            ),
            SizedBox(height: 1.h),
            GestureDetector(
              onTap: () {
                var year = viewModel.us.startDate.year;
                var month = viewModel.us.startDate.month;
                var day = viewModel.us.startDate.day;
                TDPicker.showDatePicker(context,
                    title: '选择时间',
                    initialDate: [year, month, day],
                    dateStart: viewModel.us.startDateStart,
                    dateEnd: viewModel.us.startDateEnd, onConfirm: (v) {
                  var year = v['year'] as int;
                  var month = v['month'] as int;
                  var day = v['day'] as int;
                  viewModel.us.setStartDate(DateTime(year, month, day));
                });
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3.r),
                  color: ColorsUtil.garyF2,
                ),
                child: IntrinsicWidth(
                  child: Row(
                    children: [
                      Text(
                        formatDate(viewModel.us.startDate),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey,
                        ),
                      ),
                      Icon(
                        Icons.arrow_drop_down,
                        size: 16.w,
                        color: Colors.grey.shade400,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              viewModel.us.startDateDisplayText,
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.black14,
              ),
            ),
          ],
        ),
        if (viewModel.us.targetDay != -1) ...[
          Spacer(),
          Padding(
            padding: const EdgeInsets.only(bottom: 18),
            child: Icon(
              Icons.minimize,
              size: 16.w,
              color: Colors.grey.shade400,
            ),
          ),
          Spacer(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '结束时间',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: ColorsUtil.garyB1,
                ),
              ),
              SizedBox(height: 1.h),
              GestureDetector(
                onTap: () {
                  var year = viewModel.us.endDate.year;
                  var month = viewModel.us.endDate.month;
                  var day = viewModel.us.endDate.day;
                  TDPicker.showDatePicker(context,
                      title: '选择时间',
                      initialDate: [year, month, day],
                      dateStart: viewModel.us.endDateStart,
                      dateEnd: viewModel.us.endDateEnd, onConfirm: (v) {
                    var year = v['year'] as int;
                    var month = v['month'] as int;
                    var day = v['day'] as int;
                    viewModel.us.setEndDate(DateTime(year, month, day));
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(3.r),
                    color: ColorsUtil.garyF2,
                  ),
                  child: IntrinsicWidth(
                    child: Row(
                      children: [
                        Text(
                          formatDate(viewModel.us.endDate),
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey,
                          ),
                        ),
                        Icon(
                          Icons.arrow_drop_down,
                          size: 16.w,
                          color: Colors.grey.shade400,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                viewModel.us.endDateDisplayText,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.black14,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 构建打卡频率模块
  Widget buildCheckInFrequencySection(PlanAddGoalViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '打卡频率',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: ColorsUtil.textBlack,
          ),
        ),
        SizedBox(height: 12.h),

        // 周一到周天
        Row(
          spacing: 7.w,
          children: [
            Obx(() => _buildWeekButton(
                '周一',
                1,
                viewModel.us.frequencyWeek.contains(1),
                () => viewModel.onWeekdayToggle(1))),
            Obx(() => _buildWeekButton(
                '周二',
                2,
                viewModel.us.frequencyWeek.contains(2),
                () => viewModel.onWeekdayToggle(2))),
            Obx(() => _buildWeekButton(
                '周三',
                3,
                viewModel.us.frequencyWeek.contains(3),
                () => viewModel.onWeekdayToggle(3))),
            Obx(() => _buildWeekButton(
                '周四',
                4,
                viewModel.us.frequencyWeek.contains(4),
                () => viewModel.onWeekdayToggle(4))),
            Obx(() => _buildWeekButton(
                '周五',
                5,
                viewModel.us.frequencyWeek.contains(5),
                () => viewModel.onWeekdayToggle(5))),
            Obx(() => _buildWeekButton(
                '周六',
                6,
                viewModel.us.frequencyWeek.contains(6),
                () => viewModel.onWeekdayToggle(6))),
            Obx(() => _buildWeekButton(
                '周日',
                7,
                viewModel.us.frequencyWeek.contains(7),
                () => viewModel.onWeekdayToggle(7))),
          ],
        ),

        SizedBox(height: 16.h),

        // 可休息天数
        _buildRestDaysSection(viewModel),
      ],
    );
  }

  /// 构建每周休息天数模块
  Widget _buildRestDaysSection(PlanAddGoalViewModel viewModel) {
    return Obx(() {
      if (viewModel.us.frequencyWeek.length == 7) {
        return Column(
          children: [
            Row(
              children: [
                Text(
                  '每周休息天数',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                const Spacer(),
                buildCounterWidget(
                  '${viewModel.us.restDays}天',
                  viewModel.us.decreaseRestDays,
                  viewModel.us.increaseRestDays,
                ),
              ],
            ),
            SizedBox(height: 6.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '每周需打卡',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.gary85,
                  ),
                ),
                Text(
                  viewModel.us.checkInFrequencyText,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
                Text(
                  ',总共需打卡',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.gary85,
                  ),
                ),
                Text(
                  '${viewModel.us.targetDayNum}天',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return SizedBox();
      }
    });
  }

  /// 构建每日打卡次数模块
  Widget buildDailyCheckInCountSection(PlanAddGoalViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            children: [
              Text(
                '每日打卡次数',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              Obx(() => buildCounterWidget(
                    '${viewModel.us.targetDayNum}次',
                    viewModel.us.decreaseTargetDayNum,
                    viewModel.us.increaseTargetDayNum,
                  )),
            ],
          ),
          SizedBox(height: 8.h),
          Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            Text(
              '每日需打卡',
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.gary85,
              ),
            ),
            Obx(
              () => Text(
                '${viewModel.us.targetDayNum}次',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            ),
            Text(
              '，才算完成当日打卡',
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.gary85,
              ),
            ),
          ]),
        ],
      ),
    );
  }

  /// 构建周期按钮
  Widget _buildWeekButton(
      String text, int weekday, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          decoration: BoxDecoration(
            color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.garyF2,
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color: isSelected ? Colors.white : ColorsUtil.textBlack,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
