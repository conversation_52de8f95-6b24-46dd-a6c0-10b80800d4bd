import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/data/target/ds/model/param/target_create_params_model.dart';
import 'package:flutter_kexue/data/target/repo/target_repo.dart';
import 'package:flutter_kexue/utils/date_utils.dart';
import 'package:flutter_kexue/utils/system_util/jprint.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

import '../entity/plan_add_goal_props.dart';
import 'plan_add_goal_us.dart';

/// @date 2025/07/12
/// @description 新增目标页面ViewModel
class PlanAddGoalViewModel {
  final _repo = TargetRepo();
  var us = PlanAddGoalUS();
  String? _planId = '22';

  /// 页面参数
  PlanAddGoalProps? props;

  PlanAddGoalViewModel({this.props}) {
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    // 如果是编辑模式，加载现有数据
    if (props?.isEditMode == true) {
      _loadExistingGoalData();
    } else {
      // 如果是新增目标，从缓存中加载目标天数
      _loadCachedTargetDays();
    }

    // 设置默认的激励语
    us.motivationController.text = '每一次完成都是进步的体现！';
  }

  /// 加载现有目标数据（编辑模式）
  void _loadExistingGoalData() {
    // TODO: 根据goalId加载现有数据
    jprint('加载目标数据: ${props?.goalId}');
  }

  /// 处理返回按钮点击
  void onBackPressed() {
    Get.back();
  }

  /// 处理保存按钮点击
  void onSavePressed() {
    if (_validateForm()) {
      _saveGoal();
    }
  }

  /// 处理目标类型选择
  void onGoalTypeSelected(TargetType type) {
    us.setTargetType(type);
  }

  /// 处理周几选择
  void onWeekdayToggle(int weekday) {
    us.toggleWeekday(weekday);
  }

  /// 选择全部周几
  void onSelectAllWeekdays() {
    us.selectAllWeekdays();
  }

  /// 清除所有周几选择
  void onClearAllWeekdays() {
    us.clearAllWeekdays();
  }

  /// 处理自定义天数变更
  void onCustomDaysChanged(int days) {
    us.setTargetDay(days);
  }

  /// 处理累计次数周期选择
  void onTotalPeriodSelected(TotalPeriod period) {
    us.setTotalPeriod(period);
  }

  /// 处理开始时间选择
  void onStartDatePressed() async {
    final selectedDate = await showDatePicker(
      context: Get.context!,
      initialDate: us.startDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (selectedDate != null) {
      us.setStartDate(selectedDate);
    }
  }

  /// 处理随机生成激励语
  void onGenerateMotivationPressed() {
    final motivations = [
      '每一次完成都是进步的体现！',
      '坚持就是胜利，加油！',
      '今天的努力，明天的收获！',
      '相信自己，你一定可以做到！',
      '每一步都在向目标靠近！',
    ];

    final randomMotivation =
        motivations[DateTime.now().millisecond % motivations.length];
    us.motivationController.text = randomMotivation;
    ToastUtil.showToast('已生成随机激励语');
  }

  /// 验证表单
  bool _validateForm() {
    _repo.cacheTargetDays(us.targetDay);
    if (us.targetNameController.text.trim().isEmpty) {
      ToastUtil.showToast('请输入目标名称');
      return false;
    }
    return true;
  }

  /// 保存目标
  Future<void> _saveGoal() async {
    try {
      jprint('保存目标:');
      jprint('- 计划id: ${_planId}');
      jprint('- 体系: ${us.planName}');
      jprint('- 目标名称: ${us.targetNameController.text}');
      jprint('- 目标类型: ${us.targetType == TargetType.clock ? "打卡" : "持戒"}');
      jprint('- 打卡频率: ${us.frequencyWeek.toList()}');
      jprint('- 每日次数: ${us.targetDayNum}');
      jprint('- 开始时间: ${JDateUtils.formatDate(us.startDate)}');
      jprint('- 结束时间: ${JDateUtils.formatDate(us.endDate)}');
      jprint('- 累计次数: ${us.totalCount}');
      jprint('- 目标天数: ${us.targetDay}天');
      jprint('- 可休息天数: ${us.restDays}');
      jprint('- 激励语: ${us.motivationController.text}');
      jprint('- 显示每日激励: ${us.logSwitch}');

      var params = (us.targetType == TargetType.clock)
          ? TargetCreateParams(
              plan_id: _planId ?? "",
              target_name: us.targetNameController.text,
              target_type: us.targetType.value,
              target_day: us.targetDay,
              target_start_day: JDateUtils.formatDate(us.startDate),
              target_end_day: JDateUtils.formatDate(us.endDate),
              clock_frequency: jsonEncode(us.frequencyWeek.toList()),
              target_rest_day: us.restDays,
              target_day_num: us.targetDayNum,
              target_motivate: us.motivationController.text,
              target_log_switch: us.logSwitch ? 1 : 0,
            )
          : TargetCreateParams(
              plan_id: _planId ?? "",
              target_name: us.targetNameController.text,
              target_type: us.targetType.value,
              target_day: us.targetDay,
              target_day_num: us.targetDayNum,
              target_motivate: us.motivationController.text,
              target_log_switch: us.logSwitch ? 1 : 0,
            );

      var result = await _repo.createTarget(params);
      if (result.isSuccess) {
        ToastUtil.showToast('新增目标成功');
        // 缓存目标天数
        _repo.cacheTargetDays(us.targetDay);
        Get.back();
      } else {
        ToastUtil.showToast(result.message);
      }
    } catch (e) {
      ToastUtil.showToast('新增目标失败，请重试');
    }
  }

  /// 从缓存中获取目标天数
  /// 返回缓存的天数，如果没有缓存则返回null
  int _getCachedTargetDays() {
    try {
      final cachedDays = _repo.getCachedTargetDays();
      jprint('ViewModel获取缓存目标天数: cachedDays=$cachedDays');
      return cachedDays ?? 7;
    } catch (e) {
      jprint('获取缓存目标天数失败: $e');
    }
    return 7;
  }

  /// 加载缓存的目标天数并设置到UI状态
  _loadCachedTargetDays() {
    try {
      final cachedDays = _getCachedTargetDays();
      jprint('从缓存加载目标天数: $cachedDays');
      us.setTargetDay(cachedDays);
      jprint('设置目标天数完成: ${us.targetDay}');
    } catch (e) {
      jprint('加载缓存目标天数失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    us.dispose();
  }
}
