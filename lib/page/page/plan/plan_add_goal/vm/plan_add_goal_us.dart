import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:get/get.dart';

/// @date 2025/07/12
/// @description 新增目标页面UI状态
class PlanGoalUS {

  /// 是否处于编辑状态呢
  final _isEdit = true.obs;

  bool get isEdit => _isEdit.value;

  setIsEdit(bool value) {
    _isEdit.value = value;
    _isEdit.refresh();
  }

  /// 体系名称
  final _planName = ''.obs;

  String get planName => _planName.value;

  /// 目标名称控制器
  final TextEditingController targetNameController = TextEditingController();

  /// 目标类型
  final _targetType = TargetType.clock.obs;

  TargetType get targetType => _targetType.value;

  bool get isClock => targetType == TargetType.clock;

  /// 目标天数
  final _targetDay = 7.obs;

  int get targetDay => _targetDay.value;

  /// 开始时间
  final _startDate = DateTime.now().obs;

  /// 结束时间
  final _endDate = DateTime.now().add(const Duration(days: 7)).obs;

  ///开始时间的开始时间
  List<int> get startDateStart => [
        startDate.year - 1,
        startDate.month,
        startDate.day,
      ];

  ///开始时间的结束时间
  List<int> get startDateEnd => [
        endDate.year,
        endDate.month,
        endDate.day,
      ];

  DateTime get startDate => _startDate.value;

  /// 结束时间的开始时间
  List<int> get endDateStart => [
        startDate.year,
        startDate.month,
        startDate.day,
      ];

  /// 结束时间的结束时间
  List<int> get endDateEnd => [
        endDate.year + 1,
        endDate.month,
        endDate.day,
      ];

  DateTime get endDate => _endDate.value;

  /// 打卡频率 选中的周几（周一到周日）
  final _frequencyWeek = <int>{1, 2, 3, 4, 5, 6, 7}.obs;

  setFrequencyWeek(Set<int> value) {
    _frequencyWeek.value = value;
    _frequencyWeek.refresh();
  }

  /// 可休息天数
  final _restDays = 0.obs;

  int get restDays => _restDays.value;

  /// 每日打卡次数
  final _targetDayNum = 1.obs;

  int get targetDayNum => _targetDayNum.value;

  /// 目标激励语控制器
  final TextEditingController motivationController = TextEditingController();

  /// 每日完成时弹出目标导语
  final _logSwitch = false.obs;

  bool get logSwitch => _logSwitch.value;

  /// 破戒的 累计几次完成
  final _totalCount = 1.obs;

  int get totalCount => _totalCount.value;

  /// 累计次数重置天数
  final RxInt _totalPeriod = RxInt(-1);

  /// 累计次数重置天数
  int get totalPeriod => _totalPeriod.value;

  Set<int> get frequencyWeek => _frequencyWeek.value;

  /// 开始时间下方显示文本（今天、明天、n天后、n天前）
  String get startDateDisplayText {
    final today = DateTime.now();
    final startDateOnly =
        DateTime(startDate.year, startDate.month, startDate.day);
    final todayOnly = DateTime(today.year, today.month, today.day);

    final difference = startDateOnly.difference(todayOnly).inDays;

    if (difference == 0) {
      return '今天';
    } else if (difference == 1) {
      return '明天';
    } else if (difference > 1) {
      return '$difference天后';
    } else {
      return '${difference.abs()}天前';
    }
  }

  /// 结束时间下方显示文本（持续n天）
  String get endDateDisplayText {
    if (targetDay == -1) {
      return '无限期'; // 无限期
    } else {
      return '持续$targetDay天';
    }
  }

  /// 设置计划名称
  setPlanName(String value) {
    _planName.value = value;
    _planName.refresh();
  }

  /// 设置目标类型
  setTargetType(TargetType value) {
    _targetType.value = value;
    _targetType.refresh();
  }

  /// 设置目标天数
  setTargetDayNum(int value) {
    _targetDayNum.value = value;
    _targetDayNum.refresh();
  }

  /// 设置累计次数
  setTotalCount(int value) {
    _totalCount.value = value;
    _totalCount.refresh();
  }

  /// 设置累计次数重置天数
  setTotalPeriod(int value) {
    _totalPeriod.value = value;
    _totalPeriod.refresh();
  }

  ///设置的天数是多少天就是多少天
  setTargetDay(int value) {
    _targetDay.value = value;
    _targetDay.refresh();
    _updateEndDate();
    // 触发显示文本更新
    _refreshDisplayTexts();
  }

  /// 更新结束时间
  void _updateEndDate() {
    if (_targetDay.value != -1) {
      _endDate.value =
          _startDate.value.add(Duration(days: _targetDay.value - 1));
    }
    _endDate.refresh();
  }

  ///计算开始时间和结束时间相差多少天
  _differenceInDays() {
    final difference = startDate.difference(endDate).inDays;
    if (difference == 0) {
      _targetDay.value = 1;
    } else if (difference == 1) {
      _targetDay.value = 2;
    } else if (difference > 1) {
      _targetDay.value = difference + 1;
    } else {
      _targetDay.value = difference.abs() + 1;
    }
    _targetDay.refresh();
  }

  /// 设置开始时间
  setStartDate(DateTime value) {
    _startDate.value = value;
    _startDate.refresh();
    //计算开始时间和结束时间相差多少天
    _differenceInDays();
    // 触发显示文本更新
    _refreshDisplayTexts();
  }

  /// 设置结束时间
  setEndDate(DateTime value) {
    _endDate.value = value;
    _endDate.refresh();
    //计算开始时间和结束时间相差多少天
    _differenceInDays();
    // 触发显示文本更新
    _refreshDisplayTexts();
  }

  /// 刷新显示文本（触发getter重新计算）
  void _refreshDisplayTexts() {
    // 通过刷新相关的observable来触发UI更新
    _startDate.refresh();
    _endDate.refresh();
    _targetDay.refresh();
  }

  ///设置可休息天数
  setRestDays(int value) {
    _restDays.value = value;
    _restDays.refresh();
  }

  /// 设置是否弹出目标导语
  setLogSwitch(bool value) {
    _logSwitch.value = value;
    _logSwitch.refresh();
  }

  /// 增加每日次数
  void increaseTargetDayNum() {
    if (targetDayNum < 99) {
      setTargetDayNum(targetDayNum + 1);
    }
  }

  /// 减少每日次数
  void decreaseTargetDayNum() {
    if (targetDayNum > 1) {
      setTargetDayNum(targetDayNum - 1);
    }
  }

  /// 增加破戒累计次数
  void increaseTotalCount() {
    if (totalCount < 99) {
      setTotalCount(totalCount + 1);
    }
  }

  /// 减少破戒累计次数
  void decreaseTotalCount() {
    if (totalCount > 1) {
      setTotalCount(totalCount - 1);
    }
  }

  /// 增加可休息天数
  void increaseRestDays() {
    final maxRestDays =
        frequencyWeek.length == 7 ? 6 : (7 - frequencyWeek.length);
    if (restDays < maxRestDays && restDays < 7) {
      setRestDays(restDays + 1);
    }
  }

  /// 减少可休息天数
  void decreaseRestDays() {
    if (restDays > 0) {
      setRestDays(restDays - 1);
    }
  }

  /// 切换周几的选中状态
  void toggleWeekday(int weekday) {
    final newSet = Set<int>.from(frequencyWeek);
    if (newSet.contains(weekday)) {
      newSet.remove(weekday);
    } else {
      newSet.add(weekday);
    }
    _frequencyWeek.value = newSet;
    _frequencyWeek.refresh();

    // 自动调整可休息天数
    setRestDays(0);
  }

  /// 获取打卡频率描述文本
  String get checkInFrequencyText {
    if (frequencyWeek.length == 7) {
      final checkInDays = 7 - restDays;
      return '$checkInDays天';
    } else if (frequencyWeek.isNotEmpty) {
      return '每周需要在选中的${frequencyWeek.length}天内打卡';
    }
    return '请选择打卡日期';
  }

  /// 释放资源
  void dispose() {
    targetNameController.dispose();
    motivationController.dispose();
  }
}

/// 累计次数真实周期
enum TotalPeriod {
  unlimited, // 无限期
  days7, // 7天
  days30, // 30天
  days100, // 100天
}
