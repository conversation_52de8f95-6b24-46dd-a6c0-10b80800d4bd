import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/dialog/daily_count_dialog.dart';
import 'package:flutter_kexue/widget/picker/td_picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'vm/plan_add_goal_viewmodel.dart';

/// PlanAddGoalPage 的扩展方法
extension PlanAddGoalPageExtension on State {
  void onCustomDaysChanged() {}

  /// 构建目标天数模块
  Widget buildGoalDaysSection(PlanAddGoalViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '目标天数',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: ColorsUtil.textBlack,
            ),
          ),

          SizedBox(height: 12.h),

          // 天数选择按钮
          Row(
            spacing: 10.w,
            children: [
              Obx(() => buildDaysButton(
                    7,
                    viewModel,
                  )),
              Obx(() => buildDaysButton(
                    30,
                    viewModel,
                  )),
              Obx(() => buildDaysButton(
                    100,
                    viewModel,
                  )),
              Obx(() => buildDaysButton(
                    -1,
                    viewModel,
                  )),
              Spacer(),
              // 自定义天数输入框
              GestureDetector(
                onTap: () {
                  DailyCountDialog.show(
                    context: context,
                    initialCount: viewModel.us.targetDay == -1
                        ? 7
                        : viewModel.us.targetDay,
                    onConfirm: (count) {
                      viewModel.onCustomDaysChanged(count);
                    },
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    //选中天数这里需要显示
                    Obx(
                      () => Text(
                        viewModel.us.targetDay == -1
                            ? '无限期'
                            : '${viewModel.us.targetDay}天',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: ColorsUtil.textBlack,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Icon(
                      Icons.edit,
                      size: 20,
                      color: Colors.grey.shade400,
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // 根据选择显示不同的时间信息
          Obx(() => buildTimeInfo(viewModel)),

          SizedBox(height: 16.h),

          // 打卡频率
          buildCheckInFrequencySection(viewModel),
        ],
      ),
    );
  }

  /// 构建天数按钮
  Widget buildDaysButton(int day, PlanAddGoalViewModel viewModel) {
    bool isSelected = viewModel.us.targetDay == day;
    return GestureDetector(
      onTap: () => viewModel.onCustomDaysChanged(day),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        constraints: BoxConstraints(minWidth: 54.w),
        decoration: BoxDecoration(
          color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.garyF2,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Center(
          child: Text(
            day == -1 ? '无限期' : '$day天',
            style: TextStyle(
              fontSize: 12.sp,
              color: isSelected ? Colors.white : ColorsUtil.textBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建时间信息
  Widget buildTimeInfo(PlanAddGoalViewModel viewModel) {
    String formatDate(DateTime date) {
      return '${date.year}年${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
    }

    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '开始时间',
              style: TextStyle(
                fontSize: 12.sp,
                color: ColorsUtil.garyB1,
              ),
            ),
            SizedBox(height: 1.h),
            GestureDetector(
              onTap: () {
                var year = viewModel.us.startDate.year;
                var month = viewModel.us.startDate.month;
                var day = viewModel.us.startDate.day;
                TDPicker.showDatePicker(context,
                    title: '选择时间',
                    initialDate: [year, month, day],
                    dateStart: viewModel.us.startDateStart,
                    dateEnd: viewModel.us.startDateEnd, onConfirm: (v) {
                  var year = v['year'] as int;
                  var month = v['month'] as int;
                  var day = v['day'] as int;
                  viewModel.us.setStartDate(DateTime(year, month, day));
                });
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3.r),
                  color: ColorsUtil.garyF2,
                ),
                child: IntrinsicWidth(
                  child: Row(
                    children: [
                      Text(
                        formatDate(viewModel.us.startDate),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey,
                        ),
                      ),
                      Icon(
                        Icons.arrow_drop_down,
                        size: 16.w,
                        color: Colors.grey.shade400,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              viewModel.us.startDateDisplayText,
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.black14,
              ),
            ),
          ],
        ),
        if (viewModel.us.targetDay != -1) ...[
          Spacer(),
          Padding(
            padding: const EdgeInsets.only(bottom: 18),
            child: Icon(
              Icons.minimize,
              size: 16.w,
              color: Colors.grey.shade400,
            ),
          ),
          Spacer(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '结束时间',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: ColorsUtil.garyB1,
                ),
              ),
              SizedBox(height: 1.h),
              GestureDetector(
                onTap: () {
                  var year = viewModel.us.endDate.year;
                  var month = viewModel.us.endDate.month;
                  var day = viewModel.us.endDate.day;
                  TDPicker.showDatePicker(context,
                      title: '选择时间',
                      initialDate: [year, month, day],
                      dateStart: viewModel.us.endDateStart,
                      dateEnd: viewModel.us.endDateEnd, onConfirm: (v) {
                    var year = v['year'] as int;
                    var month = v['month'] as int;
                    var day = v['day'] as int;
                    viewModel.us.setEndDate(DateTime(year, month, day));
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(3.r),
                    color: ColorsUtil.garyF2,
                  ),
                  child: IntrinsicWidth(
                    child: Row(
                      children: [
                        Text(
                          formatDate(viewModel.us.endDate),
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey,
                          ),
                        ),
                        Icon(
                          Icons.arrow_drop_down,
                          size: 16.w,
                          color: Colors.grey.shade400,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                viewModel.us.endDateDisplayText,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.black14,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 构建目标激励语模块
  Widget buildMotivationSection(PlanAddGoalViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '目标激励语',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: viewModel.onGenerateMotivationPressed,
                child: Text(
                  '随机生成',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 6.h),

          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF0F0F0),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: TextField(
              controller: viewModel.us.motivationController,
              decoration: InputDecoration(
                hintText: '输入激励语名称',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                isDense: true,
                border: InputBorder.none,
              ),
              style: TextStyle(fontSize: 14.sp, color: ColorsUtil.textBlack),
            ),
          ),

          SizedBox(height: 20.h),

          // 每日完成时弹出目标导语开关
          Row(
            children: [
              Text(
                '每日完成时弹出日志填写页',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              Obx(() => Switch(
                    value: viewModel.us.logSwitch,
                    onChanged: (value) {
                      viewModel.us.setLogSwitch(value);
                      ;
                    },
                    activeColor: ColorsUtil.primaryColor,
                  )),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建打卡频率模块
  Widget buildCheckInFrequencySection(PlanAddGoalViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '打卡频率',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: ColorsUtil.textBlack,
          ),
        ),
        SizedBox(height: 12.h),

        // 周一到周天
        Row(
          spacing: 7.w,
          children: [
            Obx(() => _buildWeekButton(
                '周一',
                1,
                viewModel.us.frequencyWeek.contains(1),
                () => viewModel.onWeekdayToggle(1))),
            Obx(() => _buildWeekButton(
                '周二',
                2,
                viewModel.us.frequencyWeek.contains(2),
                () => viewModel.onWeekdayToggle(2))),
            Obx(() => _buildWeekButton(
                '周三',
                3,
                viewModel.us.frequencyWeek.contains(3),
                () => viewModel.onWeekdayToggle(3))),
            Obx(() => _buildWeekButton(
                '周四',
                4,
                viewModel.us.frequencyWeek.contains(4),
                () => viewModel.onWeekdayToggle(4))),
            Obx(() => _buildWeekButton(
                '周五',
                5,
                viewModel.us.frequencyWeek.contains(5),
                () => viewModel.onWeekdayToggle(5))),
            Obx(() => _buildWeekButton(
                '周六',
                6,
                viewModel.us.frequencyWeek.contains(6),
                () => viewModel.onWeekdayToggle(6))),
            Obx(() => _buildWeekButton(
                '周日',
                7,
                viewModel.us.frequencyWeek.contains(7),
                () => viewModel.onWeekdayToggle(7))),
          ],
        ),

        SizedBox(height: 16.h),

        // 可休息天数
        Row(
          children: [
            Text(
              '可休息天数',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: ColorsUtil.textBlack,
              ),
            ),
            const Spacer(),
            Obx(() => buildCounterWidget(
                  '${viewModel.us.restDays}天',
                  viewModel.us.decreaseRestDays,
                  viewModel.us.increaseRestDays,
                )),
          ],
        ),
        SizedBox(height: 6.h),
        Align(
          alignment: Alignment.centerRight,
          child: Obx(() => Text(
                viewModel.us.checkInFrequencyText,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: ColorsUtil.garyB1,
                ),
              )),
        ),
      ],
    );
  }

  /// 构建每日打卡次数模块
  Widget buildDailyCheckInCountSection(PlanAddGoalViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            children: [
              Text(
                '每日打卡次数',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              Obx(() => buildCounterWidget(
                    '${viewModel.us.targetDayNum}次',
                    viewModel.us.decreaseTargetDayNum,
                    viewModel.us.increaseTargetDayNum,
                  )),
            ],
          ),
          SizedBox(height: 8.h),
          Obx(
            () => Text(
              '每日打卡${viewModel.us.targetDayNum}次，即完成当日目标',
              style: TextStyle(
                fontSize: 12.sp,
                color: ColorsUtil.garyB1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建计数器组件
  Widget buildCounterWidget(
      String value, VoidCallback onDecrease, VoidCallback onIncrease) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: onDecrease,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: ColorsUtil.garyF2,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: const Icon(Icons.remove, size: 16),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: ColorsUtil.textBlack,
            ),
          ),
        ),
        GestureDetector(
          onTap: onIncrease,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: ColorsUtil.garyF2,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: const Icon(Icons.add, size: 16),
          ),
        ),
      ],
    );
  }

  /// 构建周期按钮
  Widget _buildWeekButton(
      String text, int weekday, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          decoration: BoxDecoration(
            color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.garyF2,
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color: isSelected ? Colors.white : ColorsUtil.textBlack,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
