import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/plan_common_widget.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/plan_persist_page_extension.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/dialog/goal_library_dialog.dart';
import 'package:flutter_kexue/widget/dialog/plan_manager_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/plan_add_goal_props.dart';
import 'plan_clock_page_extension.dart';
import 'vm/plan_add_goal_viewmodel.dart';

/// @date 2025/07/12
/// @param props 页面路由参数
/// @returns
/// @description 新增目标页面
class PlanAddGoalPage extends StatefulWidget {
  const PlanAddGoalPage({super.key, this.props});

  final PlanAddGoalProps? props;

  @override
  State<PlanAddGoalPage> createState() => _PlanAddGoalPageState();
}

class _PlanAddGoalPageState extends State<PlanAddGoalPage> {
  late PlanAddGoalViewModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = PlanAddGoalViewModel(props: widget.props);
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorsUtil.bgColor,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 6.h),
            // 体系模块
            _buildSystemSection(),

            SizedBox(height: 6.h),

            // 目标名称模块
            _buildGoalNameSection(),

            Obx(() {
              if (viewModel.us.targetType == TargetType.clock) {
                return Column(children: [
                  SizedBox(height: 6.h),
                  // 目标天数模块
                  buildGoalDaysSection(viewModel),

                  SizedBox(height: 6.h),

                  //构建每日打卡次数模块
                  buildDailyCheckInCountSection(viewModel),
                ]);
              } else {
                return buildAbstinenceConfig(viewModel);
              }
            }),

            SizedBox(height: 6.h),
            // 目标激励语模块
            buildMotivationSection(viewModel),

            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        '新增目标',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      ),
      centerTitle: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 1,
      leading: IconButton(
        onPressed: viewModel.onBackPressed,
        icon: const Icon(Icons.arrow_back_ios),
      ),
      actions: [
        TextButton(
          onPressed: viewModel.onSavePressed,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              '保存',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建体系模块
  Widget _buildSystemSection() {
    return GestureDetector(
      onTap: () {
        PlanManagerDialog.show(context: context, onConfirm: () {});
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(7),
          boxShadow: ShadowsUtil.cardShadow,
        ),
        child: Row(
          children: [
            Text(
              '体系',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: ColorsUtil.textBlack,
              ),
            ),
            const Spacer(),
            Obx(() => Text(
                  viewModel.us.planName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.textBlack,
                    fontWeight: FontWeight.w500,
                  ),
                )),
            Icon(
              Icons.arrow_forward_ios,
              size: 20,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建目标名称模块
  Widget _buildGoalNameSection() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '目标名称',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorsUtil.textBlack,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  GoalLibraryDialog.show(
                    context: context,
                    initialType: viewModel.us.targetType,
                    onConfirm: (item) {
                      // 设置目标名称
                      viewModel.us.targetNameController.text = item.title;
                    },
                  );
                },
                child: Text(
                  '目标库',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 6.h),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF0F0F0),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: TextField(
              controller: viewModel.us.targetNameController,
              decoration: InputDecoration(
                hintText: '输入目标名称',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                isDense: true,
                border: InputBorder.none,
              ),
              style: TextStyle(fontSize: 14.sp, color: ColorsUtil.textBlack),
            ),
          ),
          SizedBox(height: 16.h),
          // 目标类型模块
          _buildGoalTypeSection(),
        ],
      ),
    );
  }

  /// 构建目标类型模块
  Widget _buildGoalTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // 打卡/持戒选择
        Row(
          children: [
            Text(
              '目标类型',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: ColorsUtil.textBlack,
              ),
            ),
            Spacer(),
            Obx(() => _buildTypeButton(
                  '打卡',
                  TargetType.clock,
                  viewModel.us.targetType == TargetType.clock,
                )),
            SizedBox(width: 12.w),
            Obx(() => _buildTypeButton(
                  '持戒',
                  TargetType.persist,
                  viewModel.us.targetType == TargetType.persist,
                  color: ColorsUtil.red,
                )),
          ],
        ),

        SizedBox(height: 6.h),

        Text(
          '每日需完成的为打卡，需警惕破戒的为持戒',
          style: TextStyle(
            fontSize: 12.sp,
            color: ColorsUtil.garyB1,
          ),
        ),
      ],
    );
  }

  /// 构建类型按钮
  Widget _buildTypeButton(String text, TargetType type, bool isSelected,
      {Color? color}) {
    return GestureDetector(
      onTap: () => viewModel.onGoalTypeSelected(type),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
        decoration: BoxDecoration(
          color:
              isSelected ? color ?? ColorsUtil.primaryColor : ColorsUtil.garyF2,
          borderRadius: BorderRadius.circular(8.r),
        ),
        constraints: BoxConstraints(minWidth: 54.w),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              color: isSelected ? Colors.white : ColorsUtil.textBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
