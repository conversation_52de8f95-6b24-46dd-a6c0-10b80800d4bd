import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_details/vm/plan_details_viewmodel.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/comlete_plan_item_ui_state.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/plant_item_type.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PlanDetailsListView extends StatelessWidget {
  const PlanDetailsListView({super.key, required this.viewModel});

  final PlanDetailsViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return _buildPlanDetailsListView();
  }

  /// 构建已完成计划列表
  Widget _buildPlanDetailsListView() {
    return Obx(() {
      final plans = viewModel.us.completedPlans;
      if (plans.isEmpty) {
        return Center(
          child: Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey,
            ),
          ),
        );
      }

      // 按日期分组
      final groupedPlans = _groupPlansByDate(plans);

      return ListView.builder(
        padding: EdgeInsets.all(0),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: groupedPlans.length,
        itemBuilder: (context, index) {
          final dateGroup = groupedPlans[index];
          return _buildDateGroup(dateGroup);
        },
      );
    });
  }

  /// 按日期分组计划
  List<DateGroupedPlans> _groupPlansByDate(
      List<CompletedPlanItemUIState> plans) {
    final Map<String, List<CompletedPlanItemUIState>> grouped = {};

    for (final plan in plans) {
      if (grouped[plan.date] == null) {
        grouped[plan.date] = [];
      }
      grouped[plan.date]!.add(plan);
    }

    return grouped.entries
        .map((entry) => DateGroupedPlans(
              date: entry.key,
              plans: entry.value,
            ))
        .toList();
  }

  /// 构建日期分组
  Widget _buildDateGroup(DateGroupedPlans dateGroup) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 日期标题
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          child: Text(
            dateGroup.date,
            style: TextStyle(
              fontSize: 13.sp,
              color: ColorsUtil.gary85,
            ),
          ),
        ),

        // 该日期下的计划列表
        ListView.separated(
          padding: EdgeInsets.all(0),
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: dateGroup.plans.length,
          separatorBuilder: (context, index) => SizedBox(height: 6.h),
          // 分隔间距
          itemBuilder: (context, index) {
            final plan = dateGroup.plans[index];
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.h),
              child: _buildCompletedPlanItem(plan),
            );
          },
        )
      ],
    );
  }

  /// 构建已完成计划项
  Widget _buildCompletedPlanItem(CompletedPlanItemUIState plan) {
    return GestureDetector(
      onTap: () => {},
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(7),
          boxShadow: ShadowsUtil.cardShadow,
        ),
        child: Row(
          children: [
            // 左侧状态指示器
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // 左侧状态指示器
                    Container(
                      width: 5.w,
                      height: 17.h,
                      decoration: BoxDecoration(
                        color: _getStatusColor(plan.status, plan.type),
                        borderRadius: BorderRadius.circular(2.r),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 标题
                    Text(
                      plan.title,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: ColorsUtil.textBlack,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 13.0),
                  child: Row(
                    children: [
                      if (_getSubtitle(plan.status).isNotEmpty) ...[
                        Text(
                          _getSubtitle(plan.status),
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: _getSubtitleColor(plan.status),
                          ),
                        ),
                        SizedBox(width: 8.w),
                      ],

                      // 副标题
                      Text(
                        plan.subtitle,
                        style: TextStyle(
                            fontSize: 12.sp, color: ColorsUtil.garyB1),
                      ),
                    ],
                  ),
                )
              ],
            ),

            Spacer(),

            // 右侧内容
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 进度文本
                if (plan.progressText?.isNotEmpty == true) ...[
                  Text(
                    plan.progressText ?? "",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ColorsUtil.garyB1,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  // 进度条
                  SizedBox(
                    width: 63,
                    child: ClipRRect(
                      // 进度条
                      borderRadius: BorderRadius.circular(10), // 圆角
                      child: LinearProgressIndicator(
                        value: plan.progress,
                        // 设置高度
                        minHeight: 5,
                        // 进度颜色
                        backgroundColor: ColorsUtil.primaryColor20,
                        // 背景色
                        valueColor: AlwaysStoppedAnimation<Color>(
                            ColorsUtil.primaryColor),
                      ),
                    ),
                  ),
                ],
              ],
            ),

            // 箭头
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 20.w,
              color: ColorsUtil.garyE5,
            ),
          ],
        ),
      ),
    );
  }

  /// 获取筛选文本
  String _getFilterText(CompletedPlanFilter filter) {
    switch (filter) {
      case CompletedPlanFilter.all:
        return '全部';
      case CompletedPlanFilter.success:
        return '成功';
      case CompletedPlanFilter.terminated:
        return '破戒';
      case CompletedPlanFilter.stopped:
        return '终止';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(CompletedPlanStatus status, PlanItemType type) {
    if (type == PlanItemType.negative) {
      return Colors.red;
    }

    switch (status) {
      case CompletedPlanStatus.success:
        return ColorsUtil.primaryColor;
      case CompletedPlanStatus.terminated:
        return ColorsUtil.primaryColor;
      case CompletedPlanStatus.negative:
        return Colors.red;
    }
  }

  /// 获取副标题颜色
  Color _getSubtitleColor(CompletedPlanStatus status) {
    switch (status) {
      case CompletedPlanStatus.success:
        return ColorsUtil.primaryColor;
      case CompletedPlanStatus.terminated:
        return Colors.red;
      case CompletedPlanStatus.negative:
        return Colors.grey;
    }
  }

  /// 获取副标题颜色
  String _getSubtitle(CompletedPlanStatus status) {
    switch (status) {
      case CompletedPlanStatus.success:
        return '成功';
      case CompletedPlanStatus.terminated:
        return '终止';
      default:
        return '';
    }
  }
}

/// 日期分组的计划数据
class DateGroupedPlans {
  final String date;
  final List<CompletedPlanItemUIState> plans;

  DateGroupedPlans({
    required this.date,
    required this.plans,
  });
}
