import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_details/view/plan_details_list_view.dart';
import 'package:flutter_kexue/utils/ui_util/appbar_util.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/plan_details_props.dart';
import 'vm/plan_details_us.dart';
import 'vm/plan_details_viewmodel.dart';

/// @date 2025/07/11
/// @param props 页面路由参数
/// @returns
/// @description PlanDetails页面入口 计划详情
class PlanDetailsPage extends StatelessWidget {
  PlanDetailsPage({super.key, this.props});

  final PlanDetailsProps? props;
  final PlanDetailsViewModel viewModel = PlanDetailsViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(title: "计划详情"),
      body: contentView(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          children: [
            _buildIntroduction(),
            PlanDetailsListView(
              viewModel: viewModel,
            ),
          ],
        ),
      );
    });
  }

  Widget _buildIntroduction() {
    final planDetail = viewModel.us.planDetail;
    if (planDetail == null) {
      return const Center(child: Text("暂无数据"));
    }
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7.r),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      padding: EdgeInsets.all(14.w),
      margin: EdgeInsets.only(left: 5.w, right: 5.w, top: 8.h),
      child: Column(children: [
        _buildHeader(planDetail),
        _buildContent(planDetail),
      ]),
    );
  }

  Widget _buildHeader(PlanDetailUIState planDetail) {
    return Row(
      children: [
        Expanded(
          child: Text(
            planDetail.title,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        _buildLikeButton(),
        SizedBox(width: 8.w),
        _buildFavoriteButton(),
      ],
    );
  }

  Widget _buildLikeButton() {
    return Obx(() {
      return GestureDetector(
        onTap: viewModel.us.toggleLike,
        child: Column(
          children: [
            Icon(
              viewModel.us.isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
              color: viewModel.us.isLiked ? Colors.red : Colors.grey,
            ),
            Text(
              "${viewModel.us.likeCount}",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildFavoriteButton() {
    return Obx(() {
      return GestureDetector(
        onTap: viewModel.us.toggleFavorite,
        child: Column(
          children: [
            Icon(
              viewModel.us.isFavorited ? Icons.star : Icons.star_border,
              color: viewModel.us.isFavorited ? Colors.orange : Colors.grey,
            ),
            Text(
              "${viewModel.us.favoriteCount}",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildContent(PlanDetailUIState planDetail) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "方法名",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            planDetail.introduction,
            maxLines: viewModel.us.isIntroExpanded ? null : 3,
            overflow:
                viewModel.us.isIntroExpanded ? null : TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Text(
                "计划ID: ${planDetail.id}",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey,
                ),
              ),
              Spacer(),
              GestureDetector(
                onTap: viewModel.us.toggleIntroExpanded,
                child: Text(
                  viewModel.us.isIntroExpanded ? "收起" : "查看更多",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          )
        ],
      );
    });
  }

  /// 构建底部悬浮按钮
  Widget _buildFloatingActionButton() {
    return Obx(() {
      final planDetail = viewModel.us.planDetail;
      if (planDetail == null) return const SizedBox.shrink();

      final isOwnPlan = planDetail.isOwnPlan;

      return GestureDetector(
        onTap: () {
          isOwnPlan ? viewModel.onSharePressed : viewModel.onUsePlanPressed;
        },
        child: Container(
          width: 156.w,
          height: 34.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: ColorsUtil.primaryColor,
          ),
          child: Text(
            isOwnPlan ? '分享此体系' : '分享此体系使用此计划',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
            ),
          ),
        ),
      );
    });
  }
}
