import 'package:flutter_kexue/page/page/plan/plan_home/vm/comlete_plan_item_ui_state.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/plant_item_type.dart';
import 'package:get/get.dart';

/// @date 2025/07/11
/// @description PlanDetails页UI状态
class PlanDetailsUS {
  /// 简介是否展开
  final _isIntroExpanded = false.obs;

  /// 是否收藏
  final _isFavorited = false.obs;

  /// 是否点赞
  final _isLiked = false.obs;

  /// 点赞数量
  final _likeCount = 6890.obs;

  /// 收藏数量
  final _favoriteCount = 6890.obs;

  /// 计划详情
  final _planDetail = Rxn<PlanDetailUIState>();

  // Getters
  bool get isIntroExpanded => _isIntroExpanded.value;
  bool get isFavorited => _isFavorited.value;
  bool get isLiked => _isLiked.value;
  int get likeCount => _likeCount.value;
  int get favoriteCount => _favoriteCount.value;
  PlanDetailUIState? get planDetail => _planDetail.value;

  // Setters
  set isIntroExpanded(bool value) {
    _isIntroExpanded.value = value;
  }

  set isFavorited(bool value) {
    _isFavorited.value = value;
  }

  set isLiked(bool value) {
    _isLiked.value = value;
  }

  set likeCount(int value) {
    _likeCount.value = value;
  }

  set planDetail(PlanDetailUIState? value) {
    _planDetail.value = value;
  }

  /// 切换简介展开状态
  void toggleIntroExpanded() {
    _isIntroExpanded.value = !_isIntroExpanded.value;
  }

  /// 切换收藏状态
  void toggleFavorite() {
    _isFavorited.value = !_isFavorited.value;
    if (_isFavorited.value) {
      _favoriteCount.value++;
    } else {
      _favoriteCount.value--;
    }
  }

  /// 切换点赞状态
  void toggleLike() {
    _isLiked.value = !_isLiked.value;
    if (_isLiked.value) {
      _likeCount.value++;
    } else {
      _likeCount.value--;
    }
  }

  /// 已结束的计划列表
  final _completedPlans = <CompletedPlanItemUIState>[
    CompletedPlanItemUIState(
      id: '1',
      title: '戒撸100天',
      subtitle: '共50天',
      date: '2025年7月2日',
      status: CompletedPlanStatus.success,
      progressText: '完成200次',
      progress: 0.56,
      type: PlanItemType.abstinence,
    ),
    CompletedPlanItemUIState(
      id: '2',
      title: '戒撸100天',
      subtitle: '共40天',
      date: '2025年7月1日',
      status: CompletedPlanStatus.terminated,
      progressText: '完成200次',
      progress: 0.56,
      type: PlanItemType.abstinence,
    ),
    CompletedPlanItemUIState(
      id: '3',
      title: '手艺活',
      subtitle: '距离上次245天',
      date: '2025年7月1日',
      status: CompletedPlanStatus.negative,
      progressText: '',
      progress: 0.56,
      type: PlanItemType.negative,
    ),
    CompletedPlanItemUIState(
      id: '4',
      title: '戒撸100天',
      subtitle: '共50天',
      date: '2025年6月30日',
      status: CompletedPlanStatus.terminated,
      progressText: '完成200次',
      progress: 0.56,
      type: PlanItemType.abstinence,
    ),
    CompletedPlanItemUIState(
      id: '5',
      title: '手艺活',
      subtitle: '无',
      date: '2025年6月30日',
      status: CompletedPlanStatus.negative,
      progressText: '',
      progress: 0.56,
      type: PlanItemType.negative,
    ),
  ].obs;

  get completedPlans => _completedPlans.value;


}

/// 计划详情UI状态
class PlanDetailUIState {
  final String id;
  final String title;
  final String introduction;
  final String authorName;
  final bool isOwnPlan;

  PlanDetailUIState({
    required this.id,
    required this.title,
    required this.introduction,
    required this.authorName,
    required this.isOwnPlan,
  });
}
