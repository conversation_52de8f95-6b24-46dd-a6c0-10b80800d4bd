import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';
import 'plan_recommend_us.dart';
import '../entity/plan_recommend_props.dart';

/// @date 2025/07/12
/// @description 计划推荐页面ViewModel
class PlanRecommendViewModel {
  var us = PlanRecommendUS();

  /// 页面参数
  PlanRecommendProps? props;

  PlanRecommendViewModel({this.props}) {
    us.initSearchController();
    fetchData();
  }

  /// 初始化数据
  void fetchData() async {
    try {
      // 模拟网络请求延迟
      await Future.delayed(const Duration(milliseconds: 300));

      // 如果有初始筛选类型，设置它
      if (props?.initialFilter != null) {
        // 根据初始筛选类型设置
        switch (props!.initialFilter) {
          case 'latest':
            us.selectedFilter = PlanFilterType.latest;
            break;
          case 'mostLiked':
            us.selectedFilter = PlanFilterType.mostLiked;
            break;
          default:
            us.selectedFilter = PlanFilterType.recommend;
            break;
        }
      }

      print('PlanRecommendViewModel: 数据加载完成');
    } catch (e) {
      print('PlanRecommendViewModel: 获取数据失败 - $e');
    }
  }

  /// 处理筛选类型选择
  void onFilterSelected(PlanFilterType filter) {
    us.selectedFilter = filter;
    print('选择筛选类型: ${_getFilterText(filter)}');
  }

  /// 处理搜索输入
  void onSearchChanged(String keyword) {
    us.searchKeyword = keyword;
    print('搜索关键词: $keyword');
  }

  /// 处理计划卡片点击
  void onPlanCardTap(RecommendPlanUIState plan) {
    print('点击计划: ${plan.title}');
    Get.toNamed(Routes.planDetails, arguments: {
      'planId': plan.id,
    });
  }

  /// 处理收藏按钮点击
  void onPlanCollectTap(RecommendPlanUIState plan) {
    print('收藏计划: ${plan.title}');
    ToastUtil.showToast('已收藏');
    // TODO: 实现收藏功能
  }

  /// 处理点击更多计划
  void onLoadMoreTap() {
    print('点击查看更多');
    ToastUtil.showToast('加载更多计划');
    // TODO: 实现加载更多功能
  }

  /// 获取筛选类型文本
  String _getFilterText(PlanFilterType filter) {
    switch (filter) {
      case PlanFilterType.recommend:
        return '推荐';
      case PlanFilterType.latest:
        return '最新';
      case PlanFilterType.mostLiked:
        return '最多点赞';
    }
  }

  /// 获取筛选类型显示文本
  String getFilterDisplayText(PlanFilterType filter) {
    return _getFilterText(filter);
  }

  /// 释放资源
  void dispose() {
    us.dispose();
  }
}
