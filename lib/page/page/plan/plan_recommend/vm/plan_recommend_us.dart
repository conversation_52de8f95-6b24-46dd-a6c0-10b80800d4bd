import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// @date 2025/07/12
/// @description 计划推荐页面UI状态
class PlanRecommendUS {
  
  /// 当前选中的筛选类型
  final _selectedFilter = PlanFilterType.recommend.obs;
  
  /// 搜索关键词
  final _searchKeyword = ''.obs;
  
  /// 搜索控制器
  final TextEditingController searchController = TextEditingController();
  
  /// 推荐计划列表
  final _recommendPlans = <RecommendPlanUIState>[
    RecommendPlanUIState(
      id: '1',
      title: '戒撸100天',
      author: '健身达人',
      likeCount: 6890,
      items: [
        PlanItemUIState(id: '1-1', title: '戒撸100天', type: PlanItemType.abstinence),
        PlanItemUIState(id: '1-2', title: '打八段锦', type: PlanItemType.exercise, count: 3),
        PlanItemUIState(id: '1-3', title: '冥想', type: PlanItemType.meditation),
        PlanItemUIState(id: '1-4', title: '看黄', type: PlanItemType.negative),
        PlanItemUIState(id: '1-5', title: '手艺活', type: PlanItemType.negative),
      ],
    ),
    RecommendPlanUIState(
      id: '2',
      title: '戒撸100天',
      author: '健身达人',
      likeCount: 6890,
      items: [
        PlanItemUIState(id: '2-1', title: '戒撸100天', type: PlanItemType.abstinence),
        PlanItemUIState(id: '2-2', title: '打八段锦', type: PlanItemType.exercise, count: 3),
        PlanItemUIState(id: '2-3', title: '冥想', type: PlanItemType.meditation),
        PlanItemUIState(id: '2-4', title: '看黄', type: PlanItemType.negative),
        PlanItemUIState(id: '2-5', title: '手艺活', type: PlanItemType.negative),
      ],
    ),
    RecommendPlanUIState(
      id: '3',
      title: '官方戒撸100天体系',
      author: '健身达人',
      likeCount: 6890,
      items: [
        PlanItemUIState(id: '3-1', title: '戒撸100天', type: PlanItemType.abstinence),
        PlanItemUIState(id: '3-2', title: '打八段锦', type: PlanItemType.exercise, count: 3),
        PlanItemUIState(id: '3-3', title: '冥想', type: PlanItemType.meditation),
        PlanItemUIState(id: '3-4', title: '看黄', type: PlanItemType.negative),
        PlanItemUIState(id: '3-5', title: '手艺活', type: PlanItemType.negative),
      ],
    ),
    RecommendPlanUIState(
      id: '4',
      title: '官方戒撸100天体系',
      author: '健身达人',
      likeCount: 6890,
      items: [
        PlanItemUIState(id: '4-1', title: '戒撸100天', type: PlanItemType.abstinence),
        PlanItemUIState(id: '4-2', title: '冥想', type: PlanItemType.meditation),
        PlanItemUIState(id: '4-3', title: '手艺活', type: PlanItemType.negative),
      ],
    ),
    RecommendPlanUIState(
      id: '5',
      title: '官方戒撸100天体系',
      author: '健身达人',
      likeCount: 6890,
      items: [
        PlanItemUIState(id: '5-1', title: '戒撸100天', type: PlanItemType.abstinence),
        PlanItemUIState(id: '5-2', title: '冥想', type: PlanItemType.meditation),
        PlanItemUIState(id: '5-3', title: '看黄', type: PlanItemType.negative),
        PlanItemUIState(id: '5-4', title: '手艺活', type: PlanItemType.negative),
      ],
    ),
    RecommendPlanUIState(
      id: '6',
      title: '官方戒撸100天体系',
      author: '健身达人',
      likeCount: 6890,
      items: [
        PlanItemUIState(id: '6-1', title: '戒撸100天', type: PlanItemType.abstinence),
        PlanItemUIState(id: '6-2', title: '冥想', type: PlanItemType.meditation),
        PlanItemUIState(id: '6-3', title: '看黄', type: PlanItemType.negative),
        PlanItemUIState(id: '6-4', title: '手艺活', type: PlanItemType.negative),
      ],
    ),
  ].obs;

  // Getters
  PlanFilterType get selectedFilter => _selectedFilter.value;
  String get searchKeyword => _searchKeyword.value;
  List<RecommendPlanUIState> get recommendPlans => _recommendPlans;

  /// 获取筛选后的计划列表
  List<RecommendPlanUIState> get filteredPlans {
    var plans = recommendPlans;
    
    // 根据筛选类型过滤
    switch (selectedFilter) {
      case PlanFilterType.recommend:
        // 推荐：显示所有
        break;
      case PlanFilterType.latest:
        // 最新：可以根据创建时间排序
        break;
      case PlanFilterType.mostLiked:
        // 最多点赞：根据点赞数排序
        plans = plans.toList()..sort((a, b) => b.likeCount.compareTo(a.likeCount));
        break;
    }
    
    // 根据搜索关键词过滤
    if (searchKeyword.isNotEmpty) {
      plans = plans.where((plan) {
        return plan.title.toLowerCase().contains(searchKeyword.toLowerCase()) ||
               plan.author.toLowerCase().contains(searchKeyword.toLowerCase());
      }).toList();
    }
    
    return plans;
  }

  // Setters
  set selectedFilter(PlanFilterType value) {
    _selectedFilter.value = value;
    _selectedFilter.refresh();
  }

  set searchKeyword(String value) {
    _searchKeyword.value = value;
    _searchKeyword.refresh();
  }

  /// 初始化搜索控制器
  void initSearchController() {
    searchController.addListener(() {
      searchKeyword = searchController.text;
    });
  }

  /// 释放资源
  void dispose() {
    searchController.dispose();
  }
}

/// 推荐计划UI状态
class RecommendPlanUIState {
  final String id;
  final String title;
  final String author;
  final int likeCount;
  final List<PlanItemUIState> items;

  RecommendPlanUIState({
    required this.id,
    required this.title,
    required this.author,
    required this.likeCount,
    required this.items,
  });
}

/// 计划项UI状态
class PlanItemUIState {
  final String id;
  final String title;
  final PlanItemType type;
  final int? count;

  PlanItemUIState({
    required this.id,
    required this.title,
    required this.type,
    this.count,
  });
}

/// 计划项类型
enum PlanItemType {
  abstinence,  // 戒断
  exercise,    // 运动
  meditation,  // 冥想
  negative,    // 负面记录
}

/// 筛选类型
enum PlanFilterType {
  recommend,   // 推荐
  latest,      // 最新
  mostLiked,   // 最多点赞
}
