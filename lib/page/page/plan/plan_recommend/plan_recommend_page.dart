import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'entity/plan_recommend_props.dart';
import 'vm/plan_recommend_us.dart';
import 'vm/plan_recommend_viewmodel.dart';

/// @date 2025/07/12
/// @param props 页面路由参数
/// @returns
/// @description 计划推荐页面
class PlanRecommendPage extends StatefulWidget {
  const PlanRecommendPage({super.key, this.props});

  final PlanRecommendProps? props;

  @override
  State<PlanRecommendPage> createState() => _PlanRecommendPageState();
}

class _PlanRecommendPageState extends State<PlanRecommendPage> {
  late PlanRecommendViewModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = PlanRecommendViewModel(props: widget.props);
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // 筛选和搜索栏
          _buildFilterAndSearchBar(),
          
          // 计划列表
          Expanded(
            child: _buildPlanList(),
          ),
        ],
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(80.h),
      child: Container(
        color: Colors.white,
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
            child: _buildSearchBox(),
          ),
        ),
      ),
    );
  }

  /// 构建搜索框
  Widget _buildSearchBox() {
    return Container(
      height: 36.h,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(18.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: viewModel.us.searchController,
              onChanged: viewModel.onSearchChanged,
              decoration: InputDecoration(
                hintText: '搜索体系名称、体系ID',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[500],
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                isDense: true,
              ),
              style: TextStyle(fontSize: 14.sp),
            ),
          ),
          // 清除按钮
          GestureDetector(
            onTap: () {
              viewModel.us.searchController.clear();
              viewModel.onSearchChanged('');
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.cancel,
                size: 18.w,
                color: Colors.grey[400],
              ),
            ),
          ),
          // 搜索按钮
          GestureDetector(
            onTap: () {
              viewModel.onSearchChanged(viewModel.us.searchController.text);
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.search,
                size: 18.w,
                color: Colors.grey[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建筛选和搜索栏
  Widget _buildFilterAndSearchBar() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        children: [
          // 筛选选项
          Expanded(
            child: _buildFilterOptions(),
          ),
        ],
      ),
    );
  }

  /// 构建筛选选项
  Widget _buildFilterOptions() {
    return Obx(() => Row(
      children: PlanFilterType.values.map((filter) {
        final isSelected = viewModel.us.selectedFilter == filter;
        return GestureDetector(
          onTap: () => viewModel.onFilterSelected(filter),
          child: Container(
            margin: EdgeInsets.only(right: 24.w),
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Column(
              children: [
                Text(
                  viewModel.getFilterDisplayText(filter),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: isSelected ? ColorsUtil.primaryColor : Colors.grey,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
                if (isSelected) ...[
                  SizedBox(height: 4.h),
                  Container(
                    width: 20.w,
                    height: 2.h,
                    decoration: BoxDecoration(
                      color: ColorsUtil.primaryColor,
                      borderRadius: BorderRadius.circular(1.r),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      }).toList(),
    ));
  }

  /// 构建计划列表
  Widget _buildPlanList() {
    return Obx(() {
      final plans = viewModel.us.filteredPlans;

      if (plans.isEmpty) {
        return const Center(
          child: Text('暂无推荐计划'),
        );
      }

      return SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _buildStaggeredGrid(plans),
            _buildLoadMoreButton(),
          ],
        ),
      );
    });
  }

  /// 构建瀑布流网格
  Widget _buildStaggeredGrid(List<RecommendPlanUIState> plans) {
    // 分为左右两列
    final leftPlans = <RecommendPlanUIState>[];
    final rightPlans = <RecommendPlanUIState>[];

    for (int i = 0; i < plans.length; i++) {
      if (i % 2 == 0) {
        leftPlans.add(plans[i]);
      } else {
        rightPlans.add(plans[i]);
      }
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左列
        Expanded(
          child: Column(
            children: leftPlans.map((plan) => Padding(
              padding: EdgeInsets.only(bottom: 12.h, right: 6.w),
              child: _buildPlanCard(plan),
            )).toList(),
          ),
        ),

        // 右列
        Expanded(
          child: Column(
            children: rightPlans.map((plan) => Padding(
              padding: EdgeInsets.only(bottom: 12.h, left: 6.w),
              child: _buildPlanCard(plan),
            )).toList(),
          ),
        ),
      ],
    );
  }

  /// 构建计划卡片
  Widget _buildPlanCard(RecommendPlanUIState plan) {
    return GestureDetector(
      onTap: () => viewModel.onPlanCardTap(plan),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              plan.title,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
            
            SizedBox(height: 12.h),
            
            // 计划项目列表
            _buildPlanItems(plan.items),
            
            SizedBox(height: 12.h),
            
            // 作者和点赞信息
            Row(
              children: [
                Icon(
                  Icons.person,
                  size: 16.w,
                  color: ColorsUtil.primaryColor,
                ),
                SizedBox(width: 4.w),
                Text(
                  plan.author,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.favorite,
                  size: 16.w,
                  color: Colors.red,
                ),
                SizedBox(width: 4.w),
                Text(
                  '${plan.likeCount}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建计划项目列表
  Widget _buildPlanItems(List<PlanItemUIState> items) {
    return Column(
      children: items.map((item) => _buildPlanItem(item)).toList(),
    );
  }

  /// 构建单个计划项
  Widget _buildPlanItem(PlanItemUIState item) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          // 左侧状态指示器
          Container(
            width: 4.w,
            height: 20.h,
            decoration: BoxDecoration(
              color: _getItemTypeColor(item.type),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          SizedBox(width: 8.w),

          // 标题
          Expanded(
            child: Row(
              children: [
                Text(
                  item.title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                if (item.count != null) ...[
                  SizedBox(width: 4.w),
                  Text(
                    'x ${item.count}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载更多按钮
  Widget _buildLoadMoreButton() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16.h),
      child: Center(
        child: GestureDetector(
          onTap: viewModel.onLoadMoreTap,
          child: Text(
            '点击查看更多',
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.primaryColor,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ),
    );
  }

  /// 获取项目类型颜色
  Color _getItemTypeColor(PlanItemType type) {
    switch (type) {
      case PlanItemType.abstinence:
        return ColorsUtil.primaryColor;
      case PlanItemType.exercise:
        return ColorsUtil.primaryColor;
      case PlanItemType.meditation:
        return ColorsUtil.primaryColor;
      case PlanItemType.negative:
        return Colors.red;
    }
  }
}
