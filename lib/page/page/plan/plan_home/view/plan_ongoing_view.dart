import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/plan_home_us.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/plan_home_viewmodel.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/plant_item_type.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PlanOngoingView extends StatelessWidget {
  const PlanOngoingView({super.key, required this.viewModel});

  final PlanHomeViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      var plans = viewModel.us.ongoingPlans;
      return ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: plans.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: _buildPlanCard(plans[index]),
          );
        },
      );
    });
  }

  /// 构建计划卡片
  Widget _buildPlanCard(PlanItemUIState plan) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 计划标题和进度
          _buildPlanHeader(plan),

          // 子项列表
          if (plan.items.isNotEmpty) ...[
            Divider(height: 1, color: Colors.grey[200]),
            ...plan.items.map((item) => _buildPlanSubItem(item)),
          ],

          // 添加按钮
          _buildAddButton(plan),
        ],
      ),
    );
  }

  /// 构建计划头部
  Widget _buildPlanHeader(PlanItemUIState plan) {
    return GestureDetector(
      onTap: () => viewModel.onPlanItemTap(plan),
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            // 展开/收起图标
            Icon(
              Icons.keyboard_arrow_down,
              size: 20.w,
              color: Colors.grey,
            ),
            SizedBox(width: 8.w),
            // 标题
            Expanded(
              child: Text(
                plan.title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: ColorsUtil.textBlack,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // 进度文本
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  plan.progressText,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.garyB1,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  width: 63,
                  child: ClipRRect(
                    // 进度条
                    borderRadius: BorderRadius.circular(10), // 圆角
                    child: LinearProgressIndicator(
                      value: plan.progress,
                      // 设置高度
                      minHeight: 5,
                      // 进度颜色
                      backgroundColor: ColorsUtil.primaryColor20,
                      // 背景色
                      valueColor: AlwaysStoppedAnimation<Color>(
                          ColorsUtil.primaryColor),
                    ),
                  ),
                ),
              ],
            ),

            // 更多操作
            SizedBox(width: 5.w),
            Icon(
              Icons.list,
              size: 20.w,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建计划子项
  Widget _buildPlanSubItem(PlanSubItemUIState item) {
    return GestureDetector(
      onTap: () => viewModel.onSubItemTap(item),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // 左侧状态指示器
                    Container(
                      width: 5.w,
                      height: 17.h,
                      decoration: BoxDecoration(
                        color: _getItemTypeColor(item.type),
                        borderRadius: BorderRadius.circular(2.r),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 标题行
                    Row(
                      children: [
                        Text(
                          item.title,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: ColorsUtil.textBlack,
                          ),
                        ),
                        if (item.completedCount != null) ...[
                          SizedBox(width: 4.w),
                          Text(
                            'x ${item.completedCount}',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ColorsUtil.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

                // 副标题
                if (item.subtitle.isNotEmpty) ...[
                  SizedBox(height: 2.h),
                  Padding(
                    padding: const EdgeInsets.only(left: 13.0),
                    child: Text(
                      item.subtitle,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ],
            ),

            Spacer(),

            //右侧内容
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 右侧进度文本
                Text(
                  item.progressText,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.garyB1,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // 右侧进度条
                if (item.progress > 0) ...[
                  SizedBox(
                    width: 63,
                    child: ClipRRect(
                      // 进度条
                      borderRadius: BorderRadius.circular(10), // 圆角
                      child: LinearProgressIndicator(
                        value: item.progress,
                        // 设置高度
                        minHeight: 5,
                        // 进度颜色
                        backgroundColor: ColorsUtil.primaryColor20,
                        // 背景色
                        valueColor: AlwaysStoppedAnimation<Color>(
                            ColorsUtil.primaryColor),
                      ),
                    ),
                  ),
                ]
              ],
            ),

            // 箭头
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 20.w,
              color: ColorsUtil.garyE5,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton(PlanItemUIState plan) {
    return GestureDetector(
      onTap: () => viewModel.onPlanItemAddPressed(plan),
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: 12.w),
        padding: EdgeInsets.symmetric(vertical: 10.h),
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(
              color: Color(0xFFEFEFEF),
              width: 1,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              size: 27.w,
              color: ColorsUtil.black85,
            ),
            SizedBox(width: 6.w),
            Text(
              '添加目标',
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.black10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取项目类型颜色
  Color _getItemTypeColor(PlanItemType type) {
    switch (type) {
      case PlanItemType.abstinence:
        return ColorsUtil.primaryColor;
      case PlanItemType.exercise:
        return ColorsUtil.primaryColor;
      case PlanItemType.meditation:
        return ColorsUtil.primaryColor;
      case PlanItemType.negative:
        return Colors.red;
    }
  }
}
