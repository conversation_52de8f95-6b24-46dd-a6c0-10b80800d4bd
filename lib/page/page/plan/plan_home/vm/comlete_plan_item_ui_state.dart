import 'package:flutter_kexue/page/page/plan/plan_home/vm/plant_item_type.dart';

/// 已完成计划项UI状态
class CompletedPlanItemUIState {
  final String id;
  final String title;
  final String subtitle;
  final String date;
  final CompletedPlanStatus status;
  final String? progressText;
  final double progress;
  final PlanItemType type;

  CompletedPlanItemUIState({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.date,
    required this.status,
    required this.progress,
    required this.progressText,
    required this.type,
  });
}

/// 已完成计划状态
enum CompletedPlanStatus {
  success, // 成功
  terminated, // 终止
  negative, // 负面记录
}

/// 筛选类型
enum CompletedPlanFilter {
  all, // 全部
  success, // 成功
  terminated, // 破戒
  stopped, // 终止
}
