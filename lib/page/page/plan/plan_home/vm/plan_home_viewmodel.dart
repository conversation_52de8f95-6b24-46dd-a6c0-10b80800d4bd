import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/comlete_plan_item_ui_state.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';

import "plan_home_us.dart";

/// @date 2025/07/10
/// @description PlanHome页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class PlanHomeViewModel {
  var us = PlanHomeUS();

  PlanHomeViewModel() {
    fetchData();
  }

  /// 初始化TabController
  void initTabController(TickerProvider vsync) {
    us.initTabController(vsync);
  }

  /// 处理Tab切换
  void onTabChanged(int index) {
    us.currentTabIndex = index;
    print('切换到Tab: ${index == 0 ? "进行中" : "已结束"}');
  }

  /// 处理添加计划按钮点击
  void onAddPlanPressed() {
    print('点击添加计划');
    ToastUtil.showToast('添加计划功能开发中');
    // TODO: 跳转到添加计划页面
  }

  /// 处理计划项点击
  void onPlanItemTap(PlanItemUIState plan) {
    print('点击计划: ${plan.title}');
    // TODO: 跳转到计划详情页面
  }

  /// 处理计划项添加按钮点击
  void onPlanItemAddPressed(PlanItemUIState plan) {
    print('点击计划添加按钮: ${plan.title}');
    ToastUtil.showToast('添加目标功能开发中');
    // TODO: 跳转到添加目标页面
  }

  /// 处理子项点击
  void onSubItemTap(PlanSubItemUIState subItem) {
    print('点击子项: ${subItem.title}');
  }

  /// 处理筛选选择
  void onFilterSelected(CompletedPlanFilter filter) {
    us.selectedFilter = filter;
    print('选择筛选: ${_getFilterText(filter)}');
  }

  /// 处理搜索输入
  void onSearchChanged(String keyword) {
    us.searchKeyword = keyword;
    print('搜索关键词: $keyword');
  }

  /// 处理已完成计划项点击
  void onCompletedPlanTap(CompletedPlanItemUIState plan) {
    print('点击已完成计划: ${plan.title}');
    // TODO: 跳转到计划详情页面
  }

  /// 获取筛选文本
  String _getFilterText(CompletedPlanFilter filter) {
    switch (filter) {
      case CompletedPlanFilter.all:
        return '全部';
      case CompletedPlanFilter.success:
        return '成功';
      case CompletedPlanFilter.terminated:
        return '破戒';
      case CompletedPlanFilter.stopped:
        return '终止';
    }
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      // 同步获取数据
      print('PlanHomeViewModel: 获取计划数据');
    } catch (e) {
      print('PlanHomeViewModel: 获取数据失败 - $e');
    } finally {
      // 清理资源
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState() {}

  /// 释放资源
  void dispose() {
    us.dispose();
  }
}
