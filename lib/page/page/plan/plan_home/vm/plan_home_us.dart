import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/comlete_plan_item_ui_state.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/vm/plant_item_type.dart';
import 'package:get/get.dart';

/// @date 2025/07/10
/// @description PlanHome页UI状态
class PlanHomeUS {

  /// Tab控制器
  late TabController tabController;

  /// 当前选中的Tab索引 (0: 进行中, 1: 已结束)
  final _currentTabIndex = 0.obs;

  /// 进行中的计划列表
  final _ongoingPlans = <PlanItemUIState>[
    PlanItemUIState(
      id: '1',
      title: '戒色戒色戒色戒色戒色戒色...',
      subtitle: '',
      progress: 0.8682,
      progressText: '总进度86.82%',
      status: PlanStatus.ongoing,
      items: [
        PlanSubItemUIState(
          id: '1-1',
          title: '戒撸100天',
          subtitle: '剩30天',
          progress: 0.56,
          progressText: '完成20次(56%)',
          type: PlanItemType.abstinence,
        ),
        PlanSubItemUIState(
          id: '1-2',
          title: '打八段锦',
          subtitle: '无期限',
          progress: 0.0,
          progressText: '完成20次',
          type: PlanItemType.exercise,
          completedCount: 3,
        ),
        PlanSubItemUIState(
          id: '1-3',
          title: '冥想',
          subtitle: '2025年6月27日开始',
          progress: 0.0,
          progressText: '完成0次(0%)',
          type: PlanItemType.meditation,
        ),
      ],
    ),
    PlanItemUIState(
      id: '2',
      title: '减肥',
      subtitle: '',
      progress: 0.2361,
      progressText: '总进度23.61%',
      status: PlanStatus.ongoing,
      items: [],
    ),
  ].obs;

  /// 已结束的计划列表
  final _completedPlans = <CompletedPlanItemUIState>[
    CompletedPlanItemUIState(
      id: '1',
      title: '戒撸100天',
      subtitle: '共50天',
      date: '2025年7月2日',
      status: CompletedPlanStatus.success,
      progressText: '完成200次',
      progress: 0.56,
      type: PlanItemType.abstinence,
    ),
    CompletedPlanItemUIState(
      id: '2',
      title: '戒撸100天',
      subtitle: '共40天',
      date: '2025年7月1日',
      status: CompletedPlanStatus.terminated,
      progressText: '完成200次',
      progress: 0.56,
      type: PlanItemType.abstinence,
    ),
    CompletedPlanItemUIState(
      id: '3',
      title: '手艺活',
      subtitle: '距离上次245天',
      date: '2025年7月1日',
      status: CompletedPlanStatus.negative,
      progressText: '',
      progress: 0.56,
      type: PlanItemType.negative,
    ),
    CompletedPlanItemUIState(
      id: '4',
      title: '戒撸100天',
      subtitle: '共50天',
      date: '2025年6月30日',
      status: CompletedPlanStatus.terminated,
      progressText: '完成200次',
      progress: 0.56,
      type: PlanItemType.abstinence,
    ),
    CompletedPlanItemUIState(
      id: '5',
      title: '手艺活',
      subtitle: '无',
      date: '2025年6月30日',
      status: CompletedPlanStatus.negative,
      progressText: '',
      progress: 0.56,
      type: PlanItemType.negative,
    ),
  ].obs;

  /// 筛选状态
  final _selectedFilter = CompletedPlanFilter.all.obs;

  /// 搜索关键词
  final _searchKeyword = ''.obs;

  /// 搜索控制器
  final TextEditingController searchController = TextEditingController();

  // Getters
  int get currentTabIndex => _currentTabIndex.value;
  List<PlanItemUIState> get ongoingPlans => _ongoingPlans;
  List<CompletedPlanItemUIState> get completedPlans => _completedPlans;
  CompletedPlanFilter get selectedFilter => _selectedFilter.value;
  String get searchKeyword => _searchKeyword.value;

  /// 获取当前Tab的计划列表
  List<PlanItemUIState> get currentPlans {
    return currentTabIndex == 0 ? ongoingPlans : [];
  }

  /// 获取筛选后的已完成计划列表
  List<CompletedPlanItemUIState> get filteredCompletedPlans {
    var plans = completedPlans.where((plan) {
      // 筛选条件
      bool matchesFilter = true;
      switch (selectedFilter) {
        case CompletedPlanFilter.all:
          matchesFilter = true;
          break;
        case CompletedPlanFilter.success:
          matchesFilter = plan.status == CompletedPlanStatus.success;
          break;
        case CompletedPlanFilter.terminated:
          matchesFilter = plan.status == CompletedPlanStatus.terminated;
          break;
        case CompletedPlanFilter.stopped:
          matchesFilter = plan.status == CompletedPlanStatus.terminated;
          break;
      }

      // 搜索条件
      bool matchesSearch = true;
      if (searchKeyword.isNotEmpty) {
        matchesSearch = plan.title.toLowerCase().contains(searchKeyword.toLowerCase()) ||
                       plan.subtitle.toLowerCase().contains(searchKeyword.toLowerCase());
      }

      return matchesFilter && matchesSearch;
    }).toList();

    return plans;
  }

  // Setters
  set currentTabIndex(int value) {
    _currentTabIndex.value = value;
    _currentTabIndex.refresh();
  }

  set selectedFilter(CompletedPlanFilter value) {
    _selectedFilter.value = value;
    _selectedFilter.refresh();
  }

  set searchKeyword(String value) {
    _searchKeyword.value = value;
    _searchKeyword.refresh();
  }

  /// 初始化TabController
  void initTabController(TickerProvider vsync) {
    tabController = TabController(length: 2, vsync: vsync);
    tabController.addListener(() {
      currentTabIndex = tabController.index;
    });

    // 初始化搜索控制器监听
    searchController.addListener(() {
      searchKeyword = searchController.text;
    });
  }

  /// 添加新计划
  void addPlan(PlanItemUIState plan) {
    if (plan.status == PlanStatus.ongoing) {
      _ongoingPlans.add(plan);
      _ongoingPlans.refresh();
    }
    // 注意：已完成计划使用不同的数据类型，需要单独的方法
  }

  /// 添加已完成计划
  void addCompletedPlan(CompletedPlanItemUIState plan) {
    _completedPlans.add(plan);
    _completedPlans.refresh();
  }

  /// 删除计划
  void removePlan(String planId) {
    _ongoingPlans.removeWhere((plan) => plan.id == planId);
    _completedPlans.removeWhere((plan) => plan.id == planId);
    _ongoingPlans.refresh();
    _completedPlans.refresh();
  }

  /// 释放资源
  void dispose() {
    tabController.dispose();
    searchController.dispose();
  }

}

/// 计划项UI状态
class PlanItemUIState {
  final String id;
  final String title;
  final String subtitle;
  final double progress;
  final String progressText;
  final PlanStatus status;
  final List<PlanSubItemUIState> items;

  PlanItemUIState({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.progress,
    required this.progressText,
    required this.status,
    required this.items,
  });
}

/// 计划子项UI状态
class PlanSubItemUIState {
  final String id;
  final String title;
  final String subtitle;
  final double progress;
  final String progressText;
  final PlanItemType type;
  final int? completedCount;

  PlanSubItemUIState({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.progress,
    required this.progressText,
    required this.type,
    this.completedCount,
  });
}

/// 计划状态
enum PlanStatus {
  ongoing,    // 进行中
  completed,  // 已结束
}



