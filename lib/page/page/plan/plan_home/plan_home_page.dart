import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/view/plan_completed_view.dart';
import 'package:flutter_kexue/page/page/plan/plan_home/view/plan_ongoing_view.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'entity/plan_home_props.dart';
import 'vm/plan_home_viewmodel.dart';

/// @date 2025/07/10
/// @param props 页面路由参数
/// @returns
/// @description PlanHome 计划首页
class PlanHomePage extends StatefulWidget {
  const PlanHomePage({super.key, this.props});

  final PlanHomeProps? props;

  @override
  State<PlanHomePage> createState() => _PlanHomePageState();
}

class _PlanHomePageState extends State<PlanHomePage>
    with TickerProviderStateMixin {
  final PlanHomeViewModel viewModel = PlanHomeViewModel();

  @override
  void initState() {
    super.initState();
    viewModel.initTabController(this);
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            height: 90.h,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFDFF6E6), // 顶部颜色
                  Color(0xFFFFFFFF), // 底部颜色
                ],
              ),
            ),
          ),
          Column(
            children: [
              // 顶部Tab栏
              _buildTopTabBar(),
              // 内容区域
              Expanded(
                child: _buildTabBarView(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建顶部Tab栏
  Widget _buildTopTabBar() {
    return SafeArea(
      bottom: false,
      child: Container(
        height: 56.h,
        padding: EdgeInsets.only(left: 20.w, right: 12.w),
        child: Row(
          children: [
            // Tab栏
            Expanded(
              child: TabBar(
                controller: viewModel.us.tabController,
                onTap: viewModel.onTabChanged,
                indicatorColor: Colors.transparent,
                // 透明色,
                dividerHeight: 0,
                isScrollable: true,
                labelPadding: EdgeInsets.only(right: 16.w),
                tabAlignment: TabAlignment.start,
                labelColor: ColorsUtil.textBlack,
                unselectedLabelColor: Colors.grey,
                labelStyle: TextStyle(
                  fontSize: 21.sp,
                  color: ColorsUtil.textBlack,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.gary85,
                  fontWeight: FontWeight.normal,
                ),
                tabs: const [
                  Tab(text: '进行中'),
                  Tab(text: '已结束'),
                ],
              ),
            ),

            // 添加按钮
            GestureDetector(
              onTap: (){
                Get.toNamed(Routes.planManager);
              },
              child: Container(
                padding: EdgeInsets.all(8.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.add,
                      size: 27.w,
                      color: ColorsUtil.textBlack,
                    ),
                    Text(
                      '添加计划',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: ColorsUtil.textBlack,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建TabBarView
  Widget _buildTabBarView() {
    return TabBarView(
      controller: viewModel.us.tabController,
      children: [
        // 进行中
        _buildPlanOngoingList(),
        // 已结束
        _buildPlanCompletedList(),
      ],
    );
  }

  /// 构建计划列表
  Widget _buildPlanOngoingList() {
    return PlanOngoingView(viewModel: viewModel);
  }

  /// 构建计划列表
  Widget _buildPlanCompletedList() {
    return PlanCompletedView(viewModel: viewModel);
  }

}
