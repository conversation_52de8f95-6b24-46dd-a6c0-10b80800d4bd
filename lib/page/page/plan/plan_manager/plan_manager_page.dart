import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/vm/plan_uistate.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_or_edit/entity/record_add_or_edit_props.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/plan_dialog/plan_edit_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/plan_manager_props.dart';
import 'vm/plan_manager_viewmodel.dart';

///行动系统管理 弹窗
class PlanManagerPage extends StatelessWidget {
  PlanManagerPage({super.key, this.props});

  final PlanManagerProps? props;
  final PlanManagerViewModel viewModel = PlanManagerViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildCustomAppBar(context),
      backgroundColor: Colors.white,
      body: contentView(),
    );
  }

  /// 构建自定义AppBar（复用日记编辑页面样式）
  PreferredSizeWidget _buildCustomAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: () => Get.back(),
      ),
      title: Text(
        '行动系统管理',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      ),
      centerTitle: true,
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleRecordManagerVMEvent(props.vm)
    return Column(
      children: [
        Divider(height: 6, color: Color(0xFFF5F5F6), thickness: 6),
        SizedBox(height: 8),
        // 添加
        _buildAddSection(),

        SizedBox(height: 8),

        Expanded(
          child: _buildListView(),
        ),
      ],
    );
  }

  Widget _buildAddSection() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(Routes.planAddOrEdit, arguments: PlanAddOrEditProps());
      },
      child: Container(
        height: 42,
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(horizontal: 13),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Color(0xFFCECECE),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.add, color: ColorsUtil.textBlack, size: 25),
            Text("添加",
                style: TextStyle(fontSize: 15, color: ColorsUtil.textBlack)),
          ],
        ),
      ),
    );
  }

  Widget _buildListView() {
    return Obx(
      () => ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: viewModel.us.plans.length,
        itemBuilder: (BuildContext context, int index) {
          return _buildItemView(viewModel.us.plans[index], index);
        },
      ),
    );
  }

  /// 构建单个习惯项
  Widget _buildItemView(PlanUIState state, int index) {
    return GestureDetector(
      onTap: () => {
        PlanEditDialog.show(
          state: state,
          onConfirm: (PlanUIState state) {
            // 更新当前列表的state
            viewModel.updatePlan(state, index);
          },
        )
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 15),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFE0E0E0),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                state.plan_name,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.textBlack,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            _buildHabitRightContent(),
          ],
        ),
      ),
    );
  }

  /// 构建习惯右侧内容
  Widget _buildHabitRightContent() {
    // 编辑模式显示三根横线
    return Row(
      spacing: 8,
      children: [
        Text(
          '系统',
          style: TextStyle(fontSize: 16.sp, color: ColorsUtil.garyCE),
        ),
        Image.asset(
          'assets/images/common/ic_arrow_right.png',
          width: 20.w,
          height: 20.h,
        ),
        Image.asset(
          'assets/images/common/ic_mune.png',
          width: 20.w,
          height: 20.h,
        ),
      ],
    );
  }
}
