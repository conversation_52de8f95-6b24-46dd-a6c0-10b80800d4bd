import "package:flutter_kexue/data/plan/ds/model/net/plans_net_model.dart";
import "package:flutter_kexue/data/plan/repo/plan_repo.dart";
import "package:flutter_kexue/page/page/plan/plan_add_goal/vm/plan_uistate.dart";

import "plan_manager_us.dart";

/// @date 2025/07/06
/// @description MultipleRecordManager页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class PlanManagerViewModel {
  final _repo = PlanRepo();
  var us = PlanManagerUS();

  PlanManagerViewModel() {
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      var response = await _repo.getPlanList();
      if (response.isSuccess) {
        _coverPlansList(response.data);
      }
    } catch (e) {
    } finally {}
  }

  void _coverPlansList(List<PlanNetModel>? data) {
    var list = data
            ?.map((item) => PlanUIState(
                  id: item.id,
                  plan_name: item.plan_name,
                  completion: item.completion,
                  status: item.status,
                  brief_introduction: item.brief_introduction,
                ))
            .toList() ??
        [];
    us.setPlans(list);
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState() {}

  /// 更新计划
  /// @param state 更新后的计划状态
  /// @param index 计划在列表中的索引
  void updatePlan(PlanUIState state, int index) {
    try {
      // 获取当前计划列表
      final currentPlans = List<PlanUIState>.from(us.plans);

      // 检查索引是否有效
      if (index >= 0 && index < currentPlans.length) {
        // 更新指定索引的计划
        currentPlans[index] = state;

        // 更新UI状态
        us.setPlans(currentPlans);

        print('计划更新成功: ${state.plan_name}');
      } else {
        print('更新计划失败: 索引超出范围 index=$index, length=${currentPlans.length}');
      }
    } catch (e) {
      print('更新计划失败: $e');
    }
  }

  /// 根据计划名称更新计划
  /// @param planName 新的计划名称
  /// @param index 计划在列表中的索引
  void updatePlanByName(String planName, int index) {
    try {
      // 获取当前计划列表
      final currentPlans = List<PlanUIState>.from(us.plans);

      // 检查索引是否有效
      if (index >= 0 && index < currentPlans.length) {
        // 创建更新后的计划状态
        final updatedPlan = PlanUIState(
          id: currentPlans[index].id,
          plan_name: planName,
          completion: currentPlans[index].completion,
          status: currentPlans[index].status,
          brief_introduction: currentPlans[index].brief_introduction,
        );

        // 更新指定索引的计划
        currentPlans[index] = updatedPlan;

        // 更新UI状态
        us.setPlans(currentPlans);

        print('计划名称更新成功: $planName');
      } else {
        print('更新计划名称失败: 索引超出范围 index=$index, length=${currentPlans.length}');
      }
    } catch (e) {
      print('更新计划名称失败: $e');
    }
  }
}
