import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/vm/plan_uistate.dart';
import 'package:get/get.dart';

/// @date 2025/07/06
/// @description JDetail页UI状态
class PlanManagerUS {
  final _plans = <PlanUIState>[].obs;

  List<PlanUIState> get plans => _plans.value;

  setPlans(List<PlanUIState> value) {
    _plans.value = value;
    _plans.refresh();
  }
}
