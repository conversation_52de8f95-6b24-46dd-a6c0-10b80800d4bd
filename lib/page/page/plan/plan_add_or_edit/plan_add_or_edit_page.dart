import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/plan_dialog/plan_lib_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/record_add_or_edit_props.dart';
import 'vm/plan_add_or_edit_viewmodel.dart';

/// 新增行动系统 或者 编辑行动系统
class PlanAddOrEditPage extends StatefulWidget {
  const PlanAddOrEditPage({super.key, this.props});

  final PlanAddOrEditProps? props;

  @override
  State<PlanAddOrEditPage> createState() => _PlanAddOrEditPageState();
}

class _PlanAddOrEditPageState extends State<PlanAddOrEditPage> {
  final PlanAddOrEditViewModel viewModel = PlanAddOrEditViewModel();

  @override
  void initState() {
    super.initState();
    // 处理初始化参数
    final props = widget.props ?? Get.arguments as PlanAddOrEditProps?;
    if (props != null) {
      viewModel.handleInitialParams(
        planId: props.planId,
        planName: props.planName,
        planIntro: props.planIntro,
      );
    }
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildCustomAppBar(),
      backgroundColor: Colors.white,
      body: _buildBody(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildCustomAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: () => {Get.back()},
      ),
      title: Obx(() => Text(
            viewModel.us.isAdd == true ? '新增行动系统' : '编辑行动系统',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: ColorsUtil.textBlack,
            ),
          )),
      centerTitle: true,
      actions: [
        TextButton(
          onPressed: () => viewModel.onSavePressed(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              '保存',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 14.h),

          // 记录名称输入
          _buildRecordNameSection(),

          SizedBox(height: 14.h),

          // 体系简介输入
          _buildPlanIntroSection(),
        ],
      ),
    );
  }

  /// 构建记录名称输入区域
  Widget _buildRecordNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '行动系统名称',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: ColorsUtil.textBlack,
              ),
            ),
            Spacer(),
            GestureDetector(
              onTap: () {
                PlanLibDialog.show(
                    context: context,
                    isShowBottomView: false,
                    onConfirm: () => {});
              },
              child: Text(
                "系统库",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            )
          ],
        ),
        SizedBox(height: 6.h),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(minHeight: 37),
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: ColorsUtil.garyF2,
            borderRadius: BorderRadius.circular(7),
          ),
          alignment: Alignment.centerLeft,
          child: TextField(
            controller: viewModel.planNameController,
            maxLines: 1,
            keyboardType: TextInputType.multiline,
            decoration: InputDecoration(
              hintText: '输入行动系统名称',
              hintStyle: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.gary99,
              ),
              border: InputBorder.none,
              isDense: true, // 减少默认的内部间距
            ),
            style: TextStyle(
              fontSize: 16,
              color: ColorsUtil.textBlack,
              height: 1.0, // 设置为1.0以减少行高
            ),
            onChanged: (value) => viewModel.onRecordNameChanged(value),
          ),
        ),
      ],
    );
  }

  /// 构建体系简介输入区域
  Widget _buildPlanIntroSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '行动系统简称',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: ColorsUtil.textBlack,
              ),
            ),
            Obx(() => Text(
                  '${viewModel.us.planIntro.length}/4',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.gary58,
                  ),
                )),
          ],
        ),
        SizedBox(height: 6.h),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(minHeight: 37),
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: ColorsUtil.garyF2,
            borderRadius: BorderRadius.circular(7),
          ),
          alignment: Alignment.centerLeft,
          child: TextField(
            controller: viewModel.planIntroController,
            onChanged: (value) => viewModel.onPlanIntroChanged(value),
            maxLines: 1,
            maxLength: 4,
            keyboardType: TextInputType.multiline,
            decoration: InputDecoration(
              hintText: '输入行动系统简称',
              hintStyle: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.gary99,
              ),
              border: InputBorder.none,
              isDense: true,
              // 减少默认的内部间距
              counterText: '', // 隐藏字符计数器
            ),
            style: TextStyle(
              fontSize: 16,
              color: ColorsUtil.textBlack,
              height: 1.0, // 设置为1.0以减少行高
            ),
          ),
        ),
      ],
    );
  }
}
