import 'package:get/get.dart';

/// @date 2025/07/07
/// @description 新增记录页面UI状态
class PlanAddOrEditUS {
  /// 体系名字
  final _planName = ''.obs;

  /// 体系简介
  final _planIntro = ''.obs;

  /// 是否是新增
  final _isAdd = true.obs;

  String get planName => _planName.value;

  String get planIntro => _planIntro.value;

  get isAdd => _isAdd.value;

  setPlanName(String value) {
    _planName.value = value;
    _planName.refresh();
  }

  setPlanIntro(String value) {
    _planIntro.value = value;
    _planIntro.refresh();
  }

  setIsAdd(bool value) {
    _isAdd.value = value;
    _isAdd.refresh();
  }
}
