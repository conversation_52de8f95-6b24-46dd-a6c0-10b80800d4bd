import 'package:flutter/widgets.dart';
import 'package:flutter_kexue/data/plan/ds/model/param/plan_create_params_model.dart';
import 'package:flutter_kexue/data/plan/ds/model/param/plan_edit_params_model.dart';
import 'package:flutter_kexue/data/plan/repo/plan_repo.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';
import 'plan_add_or_edit_us.dart';

/// @date 2025/07/07
/// @description 新增记录页面ViewModel
class PlanAddOrEditViewModel {
  final _planRepo = PlanRepo();
  var us = PlanAddOrEditUS();
  String? _planId;

  /// 记录名称输入控制器
  final TextEditingController planNameController = TextEditingController();
  final TextEditingController planIntroController = TextEditingController();

  PlanAddOrEditViewModel() {
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    // 监听记录名称输入变化
    planNameController.addListener(() {
      us.setPlanName(planNameController.text);
    });

    // 监听体系简介输入变化
    planIntroController.addListener(() {
      us.setPlanIntro(planIntroController.text);
    });
  }

  /// 处理记录名称输入
  void onRecordNameChanged(String name) {
    us.setPlanName(name);
  }

  /// 处理体系简介输入
  void onPlanIntroChanged(String intro) {
    us.setPlanIntro(intro);
  }

  /// 处理保存按钮点击
  void onSavePressed() {
    // 模拟保存逻辑
    _saveRecord();
  }

  /// 保存记录
  Future<void> _saveRecord() async {
    try {
      if (us.isAdd) {
        var request = PlanCreateParams(plan_name: us.planName);
        var response = await _planRepo.createPlan(request);
        if (response.isSuccess) {
          ToastUtil.showToast('创建成功');
          // 返回上一页
          Get.back(result: {
            'name': us.planName,
            'intro': us.planIntro,
            'success': true,
          });
        } else {
          ToastUtil.showToast(response.message);
        }
      } else {
        if (_planId?.isEmpty == true) {
          ToastUtil.showToast('计划ID不能为空');
          return;
        }
        var request = PlanEditParams(plan_id: '$_planId', plan_name: us.planName);
        var response = await _planRepo.updatePlan(request);
        if (response.isSuccess) {
          ToastUtil.showToast('修改成功');
          // 返回上一页
          Get.back(result: {
            'name': us.planName,
            'intro': us.planIntro,
            'success': true,
          });
        } else {
          ToastUtil.showToast(response.message);
        }
      }
    } catch (e) {
      ToastUtil.showToast('保存失败，请重试');
    }
  }

  /// 处理页面初始化参数
  void handleInitialParams({
    String? planId,
    String? planName,
    String? planIntro,
  }) {
    _planId = planId;
    us.setPlanName(planName ?? "");
    us.setPlanIntro(planIntro ?? "");
    us.setIsAdd(planId == null || planId == '');
    planNameController.text = planName ?? "";
    planIntroController.text = planIntro ?? "";
  }

  /// 释放资源
  void dispose() {
    planNameController.dispose();
    planIntroController.dispose();
  }
}
