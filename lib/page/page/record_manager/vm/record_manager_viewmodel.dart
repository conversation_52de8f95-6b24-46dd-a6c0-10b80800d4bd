import "package:flutter_kexue/data/mock/mock_data.dart";

import "record_manager_us.dart";

/// @date 2025/07/06
/// @description RecordManager页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class RecordManagerViewModel {
  var us = RecordManagerUS();

  RecordManagerViewModel() {
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      us.setRecordList(MockData.getRecordList());
      // 同步获取数据
      // var result = uiRep.getStatus();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
    } finally {}
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState() {}
}
