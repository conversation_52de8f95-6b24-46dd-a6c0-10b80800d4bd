import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/record_uistate.dart';
import 'package:flutter_kexue/utils/ui_util/appbar_util.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/record_lib_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'entity/record_manager_props.dart';
import 'vm/record_manager_viewmodel.dart';

/// @date 2025/07/06
/// @param props 页面路由参数
/// @returns
/// @description RecordManager页面入口 记录管理
class RecordManagerPage extends StatelessWidget {
  RecordManagerPage({super.key, this.props});

  final RecordManagerProps? props;
  final RecordManagerViewModel viewModel = RecordManagerViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(title: "记录管理"),
      backgroundColor: Colors.white,
      body: contentView(context),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView(BuildContext context) {
    // 进行事件处理
    // handleRecordManagerVMEvent(props.vm)
    return Column(
      children: [
        Divider(height: 6, color: Color(0xFFF5F5F6), thickness: 6),
        SizedBox(height: 8),
        // 添加
        _buildAddSection(context),

        SizedBox(height: 8),

        Expanded(
          child: _buildListView(),
        ),
      ],
    );
  }

  Widget _buildAddSection(BuildContext context) {
    return GestureDetector(
      onTap: () {
        RecordLibDialog.show(context: context, onConfirm: () => {});
      },
      child: Container(
        height: 42,
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(horizontal: 13),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Color(0xFFCECECE),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.add, color: ColorsUtil.textBlack, size: 25),
            Text("添加",
                style: TextStyle(fontSize: 15, color: ColorsUtil.textBlack)),
          ],
        ),
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: viewModel.us.recordList.length,
      itemBuilder: (BuildContext context, int index) {
        return _buildItemView(viewModel.us.recordList[index], index);
      },
    );
  }

  /// 构建单个习惯项
  Widget _buildItemView(RecordUIState state, int index) {
    return GestureDetector(
      onTap: () => {},
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 15),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFE0E0E0),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            //左边圆角竖线
            Container(
              width: 5,
              height: 17,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(5)),
                color: ColorsUtil.gary58,
              ),
            ),

            const SizedBox(width: 8),

            Expanded(
              child: Text(
                state.title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 16,
                  color: ColorsUtil.textBlack,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            _buildHabitRightContent(state)
          ],
        ),
      ),
    );
  }

  /// 构建习惯右侧内容
  Widget _buildHabitRightContent(RecordUIState state) {
    // 编辑模式显示三根横线
    return Row(
      spacing: 8,
      mainAxisSize: MainAxisSize.min,
      children: [
        Visibility(
            visible: state.system,
            child: Text(
              '系统',
              style: TextStyle(fontSize: 16.sp, color: ColorsUtil.garyCE),
            )),
        Icon(
          Icons.arrow_forward_ios,
          size: 20,
          color: Colors.grey.shade400,
        ),
        Icon(
          Icons.drag_handle,
          color: Colors.grey.shade400,
          size: 20,
        ),
      ],
    );
  }
}
