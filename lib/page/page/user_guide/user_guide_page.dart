import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/user_guide/vm/user_guide_viewmodel.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 新用户引导页面
class UserGuidePage extends StatefulWidget {
  const UserGuidePage({super.key});

  @override
  State<UserGuidePage> createState() => _UserGuidePageState();
}

class _UserGuidePageState extends State<UserGuidePage> {
  late UserGuideViewModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = UserGuideViewModel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildContent(),
            _buildActionSystems(),
            const Spacer(),
            _buildSkipButton(),
            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 40.h),
      child: Column(
        children: [
          // Logo图标
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check,
              color: Colors.white,
              size: 40.sp,
            ),
          ),
          
          SizedBox(height: 32.h),
          
          // 欢迎文本
          Text(
            '欢迎来到棵学戒',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: ColorsUtil.textBlack,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          // 副标题
          RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 16.sp,
                color: ColorsUtil.textBlack,
              ),
              children: [
                const TextSpan(text: '选择你的第一个'),
                TextSpan(
                  text: '行动系统',
                  style: TextStyle(
                    color: ColorsUtil.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  /// 构建行动系统选择
  Widget _buildActionSystems() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSystemCard(
                icon: Icons.smoke_free,
                title: '戒烟',
                color: const Color(0xFFFFE5E5),
                iconColor: const Color(0xFFFF6B6B),
                onTap: () => viewModel.onSystemSelected('戒烟'),
              ),
              _buildSystemCard(
                icon: Icons.spa,
                title: '戒手艺',
                color: const Color(0xFFE5F5E5),
                iconColor: const Color(0xFF4CAF50),
                onTap: () => viewModel.onSystemSelected('戒手艺'),
              ),
              _buildSystemCard(
                icon: Icons.computer,
                title: '减肥',
                color: const Color(0xFFE5E5FF),
                iconColor: const Color(0xFF6B6BFF),
                onTap: () => viewModel.onSystemSelected('减肥'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建系统卡片
  Widget _buildSystemCard({
    required IconData icon,
    required String title,
    required Color color,
    required Color iconColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80.w,
        child: Column(
          children: [
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 28.sp,
              ),
            ),
            
            SizedBox(height: 8.h),
            
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.textBlack,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建跳过按钮
  Widget _buildSkipButton() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          Text(
            '跳过，我要自定义目标',
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.gary85,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          GestureDetector(
            onTap: () => viewModel.onSkipPressed(),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Text(
                '跳过',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.gary85,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    viewModel.dispose();
    super.dispose();
  }
}
