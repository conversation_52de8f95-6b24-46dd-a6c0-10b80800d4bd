import 'package:get/get.dart';

/// 新用户引导页面UI状态
class UserGuideUS {
  /// 是否正在加载
  final _isLoading = false.obs;

  /// 选中的行动系统
  final _selectedSystem = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get selectedSystem => _selectedSystem.value;

  // Setters
  set isLoading(bool value) {
    _isLoading.value = value;
    _isLoading.refresh();
  }

  set selectedSystem(String value) {
    _selectedSystem.value = value;
    _selectedSystem.refresh();
  }

  /// 重置状态
  void reset() {
    _isLoading.value = false;
    _selectedSystem.value = '';
  }
}
