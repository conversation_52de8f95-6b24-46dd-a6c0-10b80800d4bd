import 'package:flutter/foundation.dart';
import 'package:flutter_kexue/data/user_guide/repo/user_guide_repo.dart';
import 'package:flutter_kexue/page/page/user_guide/vm/user_guide_us.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

/// 新用户引导页面ViewModel
class UserGuideViewModel {
  final _repo = UserGuideRepo();
  var us = UserGuideUS();

  UserGuideViewModel() {
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    debugPrint('UserGuideViewModel: 初始化新用户引导页面');
  }

  /// 检查是否是新用户
  Future<bool> checkIsNewUser() async {
    try {
      final isNewUser = await _repo.checkIsNewUser();
      debugPrint('UserGuideViewModel: 检查新用户状态 - $isNewUser');
      return isNewUser;
    } catch (e) {
      debugPrint('UserGuideViewModel: 检查新用户状态失败 - $e');
      return false; // 默认不是新用户
    }
  }

  /// 处理行动系统选择
  void onSystemSelected(String systemName) {
    debugPrint('UserGuideViewModel: 选择行动系统 - $systemName');
    
    // 标记用户已完成引导
    _markGuideCompleted();
    
    // 显示提示
    ToastUtil.showToast('已选择：$systemName');
    
    // 跳转到主页面
    _navigateToHome();
  }

  /// 处理跳过按钮点击
  void onSkipPressed() {
    debugPrint('UserGuideViewModel: 用户选择跳过引导');
    
    // 标记用户已完成引导
    _markGuideCompleted();
    
    // 跳转到主页面
    _navigateToHome();
  }

  /// 标记引导已完成
  Future<void> _markGuideCompleted() async {
    try {
      await _repo.markGuideCompleted();
      debugPrint('UserGuideViewModel: 引导完成状态已保存');
    } catch (e) {
      debugPrint('UserGuideViewModel: 保存引导完成状态失败 - $e');
    }
  }

  /// 跳转到主页面
  void _navigateToHome() {
    // 替换当前页面，防止用户返回到引导页
    Get.offAllNamed('/home');
  }

  /// 释放资源
  void dispose() {
    debugPrint('UserGuideViewModel: 释放资源');
  }
}
