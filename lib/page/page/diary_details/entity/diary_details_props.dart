/// @date 2025/07/20
/// @description DiaryDetails页入参
class DiaryDetailsProps {
  /// 日记id
  String? id;

  /// 日记的时间
  DateTime? dateTime;

  /// 日记的内容
  String? content;

  /// 日记的图片
  List<String>? images;

  /// 是否可编辑
  bool canEdit;

  DiaryDetailsProps({
    this.id,
    this.dateTime,
    this.content,
    this.images,
    this.canEdit = true,
  });

  @override
  String toString() {
    return 'DiaryDetailsProps{id: $id, dateTime: $dateTime, content: $content, images: $images, canEdit: $canEdit}';
  }
}