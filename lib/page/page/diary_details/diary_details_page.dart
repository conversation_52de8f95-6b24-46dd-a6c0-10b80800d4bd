import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/diary_editor/entity/diary_editor_props.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/common_dialog.dart';
import 'package:flutter_kexue/widget/dialog/list_picker_dialog.dart';
import 'package:flutter_kexue/widget/image_grid_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/diary_details_props.dart';
import 'vm/diary_details_us.dart';
import 'vm/diary_details_viewmodel.dart';

/// 日记详情页面
class DiaryDetailsPage extends StatelessWidget {
  DiaryDetailsPage({super.key, DiaryDetailsProps? props})
      : props = props ?? Get.arguments as DiaryDetailsProps?;

  final DiaryDetailsProps? props;
  final DiaryDetailsViewModel viewModel = DiaryDetailsViewModel();

  /// 处理更多按钮点击
  void _onMorePressed(BuildContext context) {
    final items = [
      ListPickerItem(
        text: '全部日记',
        textColor: ColorsUtil.textBlack,
        data: 'all',
      ),
      ListPickerItem(
        text: '删除日记',
        textColor: ColorsUtil.red,
        data: 'delete',
      ),
    ];
    ListPickerDialog.show(
        context: context,
        items: items,
        onItemSelected: (index, item) {
          if (item.data == 'delete') {
            // 删除日记
            showCommonDialog(CommonDialogConfig(
              hiddenTitle: true,
              content: '确定要删除这张图片吗？',
              negative: '取消',
              positive: '确定',
              onPositive: () {},
            ));
          }
        });
  }

  /// 处理编辑按钮点击
  void _onEditPressed() {
    // 跳转到编辑页面
    var props = DiaryEditorProps(
      dateTime: viewModel.us.diaryData.dateTime,
      content: viewModel.us.diaryData.content,
      images: viewModel.us.diaryData.images,
      id: viewModel.us.diaryData.id,
    );
    Get.toNamed(Routes.diaryEditor, arguments: props);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(context),
      body: contentView(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Text(
        '日记',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      ),
      centerTitle: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      actions: [
        // 编辑按钮
        Obx(() {
          final canEdit = viewModel.us.diaryData.canEdit ?? false;
          if (!canEdit) return const SizedBox.shrink();

          return IconButton(
            onPressed: _onEditPressed,
            icon: const Icon(Icons.edit_outlined),
          );
        }),

        // 更多按钮
        IconButton(
          onPressed: () {
            _onMorePressed(context);
          },
          icon: const Icon(Icons.more_horiz),
        ),
      ],
    );
  }

  /// 实际展示的视图
  Widget contentView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期标题
          _buildDateSection(),

          SizedBox(height: 10.h),

          // 内容区域
          _buildContentArea(),
        ],
      ),
    );
  }

  /// 构建日期显示部分
  Widget _buildDateSection() {
    final now = props?.dateTime ?? DateTime.now();
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[now.weekday - 1];

    return Row(
      children: [
        Text(
          '${now.year}年${now.month}月${now.day}日',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ColorsUtil.textBlack,
          ),
        ),
        const SizedBox(width: 10),
        Text(
          weekday,
          style: TextStyle(
            fontSize: 16,
            color: ColorsUtil.garyB8,
          ),
        ),
        Icon(
          Icons.arrow_drop_down,
          size: 16,
        ),
      ],
    );
  }

  /// 构建内容区域
  Widget _buildContentArea() {
    return Obx(() {
      final diaryData = viewModel.us.diaryData;
      // 如果没有内容，显示空状态
      // if (!diaryData.hasContent && !diaryData.hasImages) {
      //   return _buildEmptyState();
      // }
      return SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 文本内容
            if (diaryData.hasContent) ...[
              _buildTextContent(diaryData),
              if (diaryData.hasImages) SizedBox(height: 4.h),
            ],
            // 图片网格
            if (diaryData.hasImages) _buildImageGrid(diaryData),
          ],
        ),
      );
    });
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          SizedBox(height: 180.h),
          Text(
            '无日记',
            style: TextStyle(
              fontSize: 16.sp,
              color: ColorsUtil.garyB8,
            ),
          ),
          SizedBox(height: 8.h),
          GestureDetector(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: ColorsUtil.primaryColor,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                '记一笔',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建文本内容
  Widget _buildTextContent(DiaryDetailUIState diaryData) {
    final content = diaryData.content ?? '';
    final shouldShowExpandButton = viewModel.us.shouldShowExpandButton();
    final isExpanded = viewModel.us.isContentExpanded;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          content,
          maxLines: shouldShowExpandButton && !isExpanded ? 5 : null,
          overflow: shouldShowExpandButton && !isExpanded
              ? TextOverflow.ellipsis
              : null,
          style: TextStyle(
            fontSize: 16.sp,
            color: ColorsUtil.textBlack,
            height: 1.6,
          ),
        ),

        // 展开/收起按钮
        if (shouldShowExpandButton) ...[
          SizedBox(height: 6.h),
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: viewModel.onContentToggle,
              child: Text(
                isExpanded ? '收起' : '全部展开',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid(DiaryDetailUIState diaryData) {
    return ImageGridWidget(
      images: diaryData.images ?? [],
      itemsPerRow: 3,
      spacing: 5.w,
      enableCustomLayout: true,
    );
  }
}
