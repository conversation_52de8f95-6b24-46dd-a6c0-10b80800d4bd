import "../entity/diary_details_props.dart";
import "diary_details_us.dart";

/// @date 2025/07/20
/// @description DiaryDetails页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class DiaryDetailsViewModel {
  var us = DiaryDetailsUS();

  /// 页面参数
  DiaryDetailsProps? props;

  init(DiaryDetailsProps? props) {
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      // 根据props初始化数据
      if (props != null) {
        convertPropsToUIState(props!);
      }
    } catch (e) {
      print('获取日记详情失败: $e');
    }
  }

  /// 将props数据转换为UIState
  void convertPropsToUIState(DiaryDetailsProps props) {
    final diaryData = DiaryDetailUIState(
      id: props.id,
      dateTime: props.dateTime ?? DateTime.now(),
      content: props.content ?? '',
      images: props.images ?? [],
      canEdit: props.canEdit,
    );

    us.diaryData = diaryData;
  }

  /// 处理内容展开/收起
  void onContentToggle() {
    us.toggleContentExpanded();
  }
}
