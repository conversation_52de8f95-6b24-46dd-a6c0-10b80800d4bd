import 'package:get/get.dart';

/// @date 2025/07/20
/// @description DiaryDetails页UI状态
class DiaryDetailsUS {
  /// 日记内容是否展开
  final _isContentExpanded = false.obs;

  /// 日记数据
  final _diaryData = DiaryDetailUIState().obs;

  // Getters
  bool get isContentExpanded => _isContentExpanded.value;

  DiaryDetailUIState get diaryData => _diaryData.value;

  // Setters
  set isContentExpanded(bool value) {
    _isContentExpanded.value = value;
  }

  set diaryData(DiaryDetailUIState value) {
    _diaryData.value = value;
  }

  /// 切换内容展开状态
  void toggleContentExpanded() {
    _isContentExpanded.value = !_isContentExpanded.value;
  }

  /// 检查内容是否需要展开收起功能
  bool shouldShowExpandButton() {
    final content = _diaryData.value.content ?? '';
    return content.length > 300;
  }
}

/// 日记详情UI状态
class DiaryDetailUIState {
  final String? id;
  final DateTime? dateTime;
  final String? content;
  final List<String>? images;
  final bool canEdit;

  DiaryDetailUIState({
    this.id,
    this.dateTime,
    this.content =
        '今天是戒色的第七天，清晨醒来，意识还在混沌与清醒间徘徊时，熟悉的冲动如潮水般涌来。我死死攥住被子，指甲几乎掐进掌心，强迫自己坐起身，拉开窗帘让阳光刺进双眼。那瞬间的刺痛，像一记警钟，将我从危险边缘拽回今天是戒色的第七天，清晨醒来，意识还在混沌与清醒间徘徊时，熟悉的冲动如潮水般涌来。我死死攥住被子，指甲几乎掐进掌心，强迫自己坐起身，拉开窗帘让阳光刺进双眼。那瞬间的刺痛，像一记警钟，将我从危险边缘拽回今天是戒色的第七天，清晨醒来，意识还在混沌与清醒间徘徊时，熟悉的冲动如潮水般涌来。我死死攥住被子，指甲几乎掐进掌心，强迫自己坐起身，拉开窗帘让阳光刺进双眼。那瞬间的刺痛，像一记警钟，将我从危险边缘拽回今天是戒色的第七天，清晨醒来，意识还在混沌与清醒间徘徊时，熟悉的冲...',
    this.images = const [
      "https://picsum.photos/200/300",
      "https://picsum.photos/200/300",
      "https://picsum.photos/200/300",
      "https://picsum.photos/200/300",
      "https://picsum.photos/200/300",
      "https://picsum.photos/200/300",
      "https://picsum.photos/200/300",
    ],
    this.canEdit = true,
  });

  /// 是否有内容
  bool get hasContent => content?.isNotEmpty == true;

  /// 是否有图片
  bool get hasImages => images?.isNotEmpty == true;

  /// 格式化日期
  String get formattedDate {
    if (dateTime == null) return '';

    final now = DateTime.now();
    final date = dateTime!;

    // 判断是否是今天
    if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day) {
      return '今天';
    }

    // 判断是否是昨天
    final yesterday = now.subtract(const Duration(days: 1));
    if (date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day) {
      return '昨天';
    }

    // 其他日期
    return '${date.year}年${date.month}月${date.day}日';
  }

  /// 格式化星期
  String get formattedWeekday {
    if (dateTime == null) return '';

    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    return weekdays[dateTime!.weekday - 1];
  }
}
