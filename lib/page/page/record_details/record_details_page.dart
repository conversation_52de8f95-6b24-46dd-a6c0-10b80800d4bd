import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/page/image_preview/entity/image_preview_props.dart';
import 'package:flutter_kexue/page/page/record_details/view/record_details_statistics.dart';
import 'package:flutter_kexue/page/page/record_details/view/record_details_top_view.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/appbar_util.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/combined_filter_widget.dart';
import 'package:flutter_kexue/widget/image_grid_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'entity/record_details_props.dart';
import 'vm/record_details_viewmodel.dart';

/// @date 2025/07/08
/// @param props 页面路由参数
/// @returns
/// @description RecordDetails页面入口
class RecordDetailsPage extends StatelessWidget {
  RecordDetailsPage({super.key, this.props});

  final RecordDetailsProps? props;
  final RecordDetailsViewModel viewModel = RecordDetailsViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(title: "记录详情"),
      body: contentView(),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleRecordDetailsVMEvent(props.vm)
    return Column(
      children: [
        SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 10.h),
              buildRecordDetailsTop(),
              SizedBox(height: 10.h),
              CombinedFilterWidget(
                onSelectAll: () {},
                onFilterChanged: (filter) {},
              ),
              SizedBox(height: 10.h),
              // 统计
              buildRecordDetailsStatistics(),

              //列表
              _buildStatisticsFlowList(),
            ],
          ),
        ),
      ],
    );
  }

  ///构建顶部打卡记录
  Widget buildRecordDetailsTop() {
    return Obx(() => RecordDetailsTop(recordList: viewModel.us.recordList));
  }

  ///构建统计布局
  Widget buildRecordDetailsStatistics() {
    return Obx(
        () => RecordDetailsStatistics(uiState: viewModel.us.statisticsData));
  }

  ///构建列表布局
  Widget _buildStatisticsFlowList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: viewModel.us.dailyClockList.length,
        separatorBuilder: (context, index) => Divider(
          height: 0,
          thickness: 0,
          color: Colors.transparent,
        ),
        itemBuilder: (context, index) {
          final item = viewModel.us.dailyClockList[index];
          return Column(
            children: [
              //标题不收起
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16),
                child:
                    _buildItemTitleSection(item.systemUIState.shortNameOrName),
              ),

              ///可折叠的内容列表，带动画
              Container(
                padding: const EdgeInsets.only(left: 10, right: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(7),
                  boxShadow: ShadowsUtil.cardShadow,
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: item.list.length,
                  separatorBuilder: (context, listIndex) => const Divider(
                    height: 5,
                    thickness: 5,
                    color: Colors.transparent,
                  ),
                  itemBuilder: (context, listIndex) {
                    return _buildItemContentSection(item.list[listIndex]);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildItemTitleSection(String? date) {
    return Container(
        height: 37.h,
        alignment: Alignment.centerLeft,
        child: Text("2025年7月2日",
            style: TextStyle(fontSize: 13, color: ColorsUtil.gary85)));
  }

  Widget _buildItemContentSection(ClockUIState uiState) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          Row(
            children: [
              //左边圆角竖线
              Container(
                width: 5,
                height: 17,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                  color: uiState.goalType == TargetType.clock
                      ? ColorsUtil.primaryColor
                      : Colors.red,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  uiState.target ?? "",
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.textBlack,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ///构建一个√的icon
                  _buildRightContentSection(uiState),
                  Icon(
                    Icons.arrow_forward_ios_outlined,
                    color: Colors.grey.shade400,
                    size: 16,
                  ),
                ],
              ),
            ],
          ),
          Container(
            alignment: Alignment.topLeft,
            padding: const EdgeInsets.symmetric(horizontal: 14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //内容
                if (uiState.motivation?.isNotEmpty == true) ...[
                  Text(
                    uiState.motivation ?? "",
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorsUtil.garyB1,
                    ),
                  ),
                ],
                //图片
                if (uiState.images.isNotEmpty) ...[
                  const SizedBox(height: 6),
                  ImageGridWidget(
                      images: uiState.images,
                      onImageTap: (index) {
                        var params = ImagePreviewProps(
                          images: uiState.images,
                          initialIndex: index,
                        );
                        Get.toNamed(Routes.imagePreview, arguments: params);
                      }),
                ]
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildRightContentSection(ClockUIState uiState) {
    if (uiState.goalType == TargetType.clock) {
      if (uiState.dailyTotalCount == uiState.dailyCount) {
        return Icon(
          Icons.check,
          color: ColorsUtil.primaryColor,
          size: 16,
        );
      }
      if (uiState.dailyTotalCount != 0 &&
          uiState.dailyCount < uiState.dailyTotalCount) {
        return Text(
          "+${uiState.dailyCount}",
          style: TextStyle(
            fontSize: 16,
            color: ColorsUtil.primaryColor,
          ),
        );
      }
      return SizedBox.shrink();
    } else {
      if (uiState.dailyTotalCount != 0) {
        if (uiState.dailyCount >= uiState.dailyTotalCount) {
          return Text(
            "+${uiState.dailyCount}(破戒)",
            style: TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
          );
        }
        return Text(
          "+${uiState.dailyCount}",
          style: TextStyle(
            fontSize: 16,
            color: Colors.red,
          ),
        );
      }
      return SizedBox.shrink();
    }
  }
}
