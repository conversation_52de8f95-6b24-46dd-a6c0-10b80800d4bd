import 'package:flutter_kexue/page/common_uistate/daily_clock_uistate.dart';
import 'package:get/get.dart';

/// @date 2025/07/08
/// @description RecordDetails页UI状态
class RecordDetailsUS {
  /// 记录列表
  final _recordList = <RecordItemUIState>[
    // 打卡类型记录
    RecordItemUIState(
      id: '1',
      title: '打八段锦',
      subtitle: '',
      type: RecordType.positive,
      status: RecordStatus.permanent,
      completedCount: 3,
      totalCount: null,
      completedText: '完成20次',
      actions: [RecordAction.edit, RecordAction.archive, RecordAction.delete],
    ),
    // RecordItemUIState(
    //   id: '2',
    //   title: '戒撸100天',
    //   subtitle: '共50天',
    //   type: RecordType.positive,
    //   status: RecordStatus.completed,
    //   completedCount: null,
    //   completedText: '完成200次',
    //   actions: [
    //     RecordAction.edit,
    //     RecordAction.archive,
    //     RecordAction.delete
    //   ],
    // ),
    // RecordItemUIState(
    //   id: '3',
    //   title: '戒撸100天',
    //   subtitle: '共50天',
    //   type: RecordType.positive,
    //   status: RecordStatus.terminated,
    //   completedCount: null,
    //   completedText: '完成200次',
    //   actions: [
    //     RecordAction.edit,
    //     RecordAction.archive,
    //     RecordAction.delete
    //   ],
    // ),
    // RecordItemUIState(
    //   id: '4',
    //   title: '看黄',
    //   subtitle: '每3次记录成',
    //   type: RecordType.negative,
    //   completedCount: null,
    //   totalCount: null,
    //   completedText: '上次263天前',
    //   actions: [
    //     RecordAction.edit,
    //     RecordAction.archive,
    //     RecordAction.delete
    //   ],
    // ),
    // RecordItemUIState(
    //   id: '5',
    //   title: '日记',
    //   type: RecordType.diary,
    //   status: RecordStatus.systemProtected,
    //   completedText: '共20次记录',
    //   actions: [], // 系统记录不可编辑
    // ),
    // RecordItemUIState(
    //   id: '6',
    //   title: '自定义记录',
    //   type: RecordType.custom,
    //   completedText: '共20次记录',
    //   actions: [
    //     RecordAction.edit,
    //     RecordAction.archive,
    //     RecordAction.delete
    //   ],
    // ),
  ].obs;

  List<RecordItemUIState> get recordList => _recordList.value;

  /// 统计数据
  final _statisticsData = StatisticsDataUIState(
    title: '日记',
    totalCount: 30000,
    subtotalCount: 300,
  ).obs;

  StatisticsDataUIState get statisticsData => _statisticsData.value;

  /// 更新记录列表
  void updateRecordList(List<RecordItemUIState> newList) {
    _recordList.value = newList;
    _recordList.refresh();
  }

  ///=======================================统计=======================================
  ///=======================================统计=======================================
  final RxList<DailyClockUIState> _diaryClockList = <DailyClockUIState>[].obs;

  List<DailyClockUIState> get dailyClockList => _diaryClockList.value;

  setDailyClockList(List<DailyClockUIState> list) {
    _diaryClockList.value = list;
    _diaryClockList.refresh();
  }
}

/// 记录项UI状态
class RecordItemUIState {
  final String id;
  final String title;
  final RecordType type;
  final String? subtitle;
  final RecordStatus? status;
  final int? completedCount;
  final int? totalCount;
  final String? completedText;
  final List<RecordAction>? actions;

  RecordItemUIState({
    required this.id,
    required this.title,
    required this.type,
    this.subtitle,
    this.status,
    this.completedCount,
    this.totalCount,
    this.completedText,
    this.actions,
  });
}

/// 记录类型
enum RecordType {
  positive, //正能量
  negative, // 负面
  diary, // 日记
  custom, // 自定义记录
}

/// 记录状态
enum RecordStatus {
  permanent, //永久
  completed, // 已完成
  terminated, // 已终止
  systemProtected, // 系统保护（不可编辑）
}

/// 记录操作
enum RecordAction {
  edit, // 编辑
  archive, // 归档
  delete, // 删除
}

/// 统计数据
class StatisticsDataUIState {
  final String title; // 标题（如：日记）
  final int totalCount; // 总计
  final int subtotalCount; // 小计

  StatisticsDataUIState({
    required this.title,
    required this.totalCount,
    required this.subtotalCount,
  });

  /// 格式化总计显示
  String get formattedTotal {
    if (totalCount >= 10000) {
      return '${(totalCount / 10000).toStringAsFixed(0)}0000次';
    } else if (totalCount >= 1000) {
      return '${(totalCount / 1000).toStringAsFixed(0)}000次';
    } else {
      return '${totalCount}次';
    }
  }

  /// 格式化小计显示
  String get formattedSubtotal {
    return '${subtotalCount}次';
  }
}
