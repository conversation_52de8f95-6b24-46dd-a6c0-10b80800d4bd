import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/record_details/vm/record_details_us.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class RecordDetailsTop extends StatelessWidget {
  final List<RecordItemUIState> recordList;

  const RecordDetailsTop({super.key, required this.recordList});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recordList.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final record = recordList[index];
        return _buildRecordItem(record);
      },
    );
  }

  /// 构建单个记录项
  Widget _buildRecordItem(RecordItemUIState record) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部标题行
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 标题
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        // 左侧状态指示器
                        _buildStatusIndicator(record),
                        SizedBox(width: 8.w),
                        Text(
                          record.title,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: ColorsUtil.textBlack,
                          ),
                        ),
                        if (record.completedCount != null) ...[
                          SizedBox(width: 4.w),
                          Text(
                            'x ${record.completedCount}',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: ColorsUtil.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 15.w),
                      child: _buildSubtitle(record),
                    )
                  ],
                ),
              ),

              // 右侧完成状态
              Column(
                children: [
                  Text(
                    record.completedText ?? '',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  _buildProgressWidget(30),
                ],
              )
            ],
          ),

          // 底部操作按钮
          if (record.status == RecordStatus.systemProtected) ...[
            SizedBox(height: 8.h),
            Container(
              alignment: Alignment.topRight,
              child: Text(
                '系统记录不可编辑',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
              ),
            )
          ] else ...[
            SizedBox(height: 12.h),
            Row(
              children: [
                Text(
                  '复制',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.textBlack,
                  ),
                ),
                const Spacer(),
                ..._buildActionButtons(record),
              ],
            ),
          ]
        ],
      ),
    );
  }

  /// 构建进度条
  Widget _buildProgressWidget(double progress) {
    return SizedBox(
      width: 66,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10), // 圆角
        child: LinearProgressIndicator(
          value: progress / 100,
          // 设置高度
          minHeight: 5,
          // 进度颜色
          backgroundColor: ColorsUtil.primaryColor20,
          // 背景色
          valueColor: AlwaysStoppedAnimation<Color>(ColorsUtil.primaryColor),
        ),
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator(RecordItemUIState record) {
    Color color;
    switch (record.type) {
      case RecordType.positive:
        color = ColorsUtil.primaryColor;
        break;
      case RecordType.negative:
        color = Colors.red;
        break;
      case RecordType.diary:
        color = Colors.grey;
        break;
      case RecordType.custom:
        color = Colors.grey;
        break;
    }

    return Container(
      width: 5.w,
      height: 17.h,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(2.5.r),
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildSubtitle(RecordItemUIState record) {
    Color color;
    String text;
    switch (record.status) {
      case RecordStatus.completed:
        color = ColorsUtil.primaryColor;
        text = '完成';
        break;
      case RecordStatus.terminated:
        color = Colors.red;
        text = '终止';
        break;
      case RecordStatus.permanent:
        color = Colors.grey;
        text = '永久';
        break;
      default:
        color = Colors.grey;
        text = '';
    }

    return Row(
      children: [
        if (text.isNotEmpty) ...[
          Text(
            text,
            style: TextStyle(
              fontSize: 12.sp,
              color: color,
            ),
          ),
          SizedBox(width: 10.h),
        ],
        if (record.subtitle?.isNotEmpty == true) ...[
          Text(
            record.subtitle ?? "",
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建操作按钮
  List<Widget> _buildActionButtons(RecordItemUIState record) {
    final buttons = <Widget>[];
    final actions = record.actions ?? [];

    for (int i = 0; i < actions.length; i++) {
      final action = actions[i];
      if (i > 0) {
        buttons.add(SizedBox(width: 16.w));
      }

      buttons.add(
        GestureDetector(
          onTap: () => _handleAction(action, record),
          child: Text(
            _getActionText(action),
            style: TextStyle(
              fontSize: 14.sp,
              color: _getActionColor(action),
            ),
          ),
        ),
      );
    }

    return buttons;
  }

  /// 获取操作文本
  String _getActionText(RecordAction action) {
    switch (action) {
      case RecordAction.edit:
        return '编辑';
      case RecordAction.archive:
        return '归档';
      case RecordAction.delete:
        return '删除';
    }
  }

  /// 获取操作颜色
  Color _getActionColor(RecordAction action) {
    switch (action) {
      case RecordAction.edit:
        return ColorsUtil.primaryColor;
      case RecordAction.archive:
        return ColorsUtil.primaryColor;
      case RecordAction.delete:
        return Colors.red;
    }
  }

  /// 处理操作点击
  void _handleAction(RecordAction action, RecordItemUIState record) {
    switch (action) {
      case RecordAction.edit:
        _editRecord(record);
        break;
      case RecordAction.archive:
        _archiveRecord(record);
        break;
      case RecordAction.delete:
        _deleteRecord(record);
        break;
    }
  }

  /// 复制记录
  void _copyRecord(RecordItemUIState record) {
    print('复制记录: ${record.title}');
    // TODO: 实现复制逻辑
  }

  /// 编辑记录
  void _editRecord(RecordItemUIState record) {
    print('编辑记录: ${record.title}');
    // TODO: 跳转到编辑页面
  }

  /// 归档记录
  void _archiveRecord(RecordItemUIState record) {
    print('归档记录: ${record.title}');
    // TODO: 实现归档逻辑
  }

  /// 删除记录
  void _deleteRecord(RecordItemUIState record) {
    print('删除记录: ${record.title}');
  }
}
