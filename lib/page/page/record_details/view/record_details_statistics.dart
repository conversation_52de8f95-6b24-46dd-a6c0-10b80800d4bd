import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_kexue/page/page/record_details/vm/record_details_us.dart';

class RecordDetailsStatistics extends StatelessWidget {
  final StatisticsDataUIState uiState;

  const RecordDetailsStatistics({super.key, required this.uiState});


  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(7),
        boxShadow: ShadowsUtil.cardShadow,
      ),
      child: Row(
        children: [
          // 左侧标题
          Text(
            uiState.title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: ColorsUtil.primaryColor,
            ),
          ),

          const Spacer(),

          // 右侧统计数据
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 总计
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '总计：',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: ColorsUtil.textBlack,
                    ),
                  ),
                  Text(
                    uiState.formattedTotal,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 4.h),

              // 小计
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '小计：',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: ColorsUtil.textBlack,
                    ),
                  ),
                  Text(
                    uiState.formattedSubtotal,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
