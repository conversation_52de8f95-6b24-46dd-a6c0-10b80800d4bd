import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/shadows_util.dart';
import 'package:flutter_kexue/widget/dialog/habit_check_in_dialog.dart';
import 'package:get/get.dart';

import 'entity/habit_manager_props.dart';
import 'vm/habit_manager_viewmodel.dart';

/// @date 2025/06/29
/// @param props 页面路由参数
/// @returns
/// @description HabitManager页面入口 习惯管里页面
class HabitManagerPage extends StatelessWidget {
  HabitManagerPage({super.key, this.props});

  final HabitManagerProps? props;
  final HabitManagerViewModel viewModel = HabitManagerViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildCustomAppBar(context),
      // 移除默认的 AppBar
      body: Stack(
        children: [
          // 自定义内容层（包含虚拟状态栏高度）
          contentView(),
          // 底部悬浮按钮
          _buildFloatingButton(context),
        ],
      ),
    );
  }

  /// 构建自定义AppBar（复用日记编辑页面样式）
  PreferredSizeWidget _buildCustomAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: ColorsUtil.textBlack, size: 20),
        onPressed: () => Get.back(),
      ),
      title: Text(
        '习惯管理',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ColorsUtil.textBlack,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: () => viewModel.onAddHabitPressed(),
          icon: Icon(
            Icons.add,
            color: ColorsUtil.textBlack,
            size: 24,
          ),
        ),
      ],
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Obx(() => Column(
          children: [
            // 筛选栏目
            _buildFilterSection(),

            // 习惯列表
            Expanded(
              child: _buildHabitList(),
            ),
          ],
        ));
  }

  /// 构建筛选栏目
  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterButton('全部', 0, ColorsUtil.primaryColor),
                  const SizedBox(width: 8),
                  _buildFilterButton('正能量', 1, ColorsUtil.primaryColor),
                  const SizedBox(width: 8),
                  _buildFilterButton('负能量', 2, Colors.orange),
                  const SizedBox(width: 8),
                  _buildFilterButton('破戒', 3, Colors.red),
                  const SizedBox(width: 8),
                  _buildFilterButton('多选记录', 4, ColorsUtil.gary58),
                ],
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 排序按钮
          GestureDetector(
            onTap: () => viewModel.onSortPressed(),
            child: Text(
              '排序',
              style: TextStyle(
                fontSize: 14,
                color: ColorsUtil.gary58,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建筛选按钮
  Widget _buildFilterButton(String text, int filterType, Color color) {
    final isSelected = viewModel.us.selectedFilter.value == filterType;

    return GestureDetector(
      onTap: () => viewModel.onFilterChanged(filterType),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 1,
          ),
          borderRadius: isSelected ? BorderRadius.circular(5) : null,
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: isSelected ? Colors.white : color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 构建习惯列表
  Widget _buildHabitList() {
    final filteredHabits = viewModel.getFilteredHabits();
    return Obx(() {
      final isEditMode = viewModel.us.isEditMode.value;
      if (isEditMode) {
        // 编辑模式使用 ReorderableListView
        return ReorderableListView.builder(
          itemCount: filteredHabits.length,
          onReorder: (oldIndex, newIndex) {
            viewModel.onReorderHabits(oldIndex, newIndex);
          },
          itemBuilder: (context, index) {
            final habit = filteredHabits[index];
            return _buildReorderListView(habit, index);
          },
        );
      } else {
        // 正常模式使用 ListView
        return ListView.builder(
          itemCount: filteredHabits.length,
          itemBuilder: (context, index) {
            return _buildHabitItem(filteredHabits[index], index);
          },
        );
      }
    });
  }

  /// 构建可重排序的习惯项
  Widget _buildReorderListView(ClockUIState habit, int index) {
    return Container(
      key: ValueKey(index.toString()),
      child: _buildHabitItem(habit, index),
    );
  }

  /// 构建单个习惯项
  Widget _buildHabitItem(ClockUIState habit, int index) {
    final isEditMode = viewModel.us.isEditMode.value;
    return GestureDetector(
      onTap: () => viewModel.onHabitItemPressed(habit),
      onLongPress:
          isEditMode ? null : () => viewModel.onHabitLongPressed(habit),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFE0E0E0),
              width: 0.5,
            ),
          ),
        ),
        child: SizedBox(
          height: 48,
          child: Row(
            children: [
              // 左侧内容
              Expanded(
                child: Row(
                  children: [
                    // 眼睛图标
                    Icon(
                      Icons.visibility_outlined,
                      color: ColorsUtil.gary58,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    // 习惯图标
                    Text(
                      "",
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 8),
                    // 习惯标题
                    Text(
                      habit.motivation ?? "",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: ColorsUtil.textBlack,
                      ),
                    ),

                    if (habit.systemUIState == true) ...[
                      const SizedBox(width: 8),
                      // 系统标签
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: Color(0xFFEFEFEF),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: Text(
                          '系统',
                          style: TextStyle(
                            fontSize: 10,
                            color: ColorsUtil.gary58,
                          ),
                        ),
                      ),
                    ]
                  ],
                ),
              ),

              // 右侧内容 - 自动靠右
              _buildHabitRightContent(habit, isEditMode),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建习惯右侧内容
  Widget _buildHabitRightContent(ClockUIState habit, bool isEditMode) {
    if (isEditMode) {
      // 编辑模式显示三根横线
      return Icon(
        Icons.drag_handle,
        color: ColorsUtil.gary58,
        size: 20,
      );
    } else {
      // 正常模式显示完成次数和箭头
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildRightContent(habit),
          const SizedBox(width: 8),
          Icon(
            Icons.arrow_forward_ios,
            color: ColorsUtil.garyB1,
            size: 14,
          ),
        ],
      );
    }
  }

  Widget _buildRightContent(ClockUIState habit) {
    if (habit.motivation?.isNotEmpty == true) {
      return Text(
        habit.motivation ?? "",
        style: TextStyle(
          fontSize: 12,
          color: ColorsUtil.gary58,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }
    return Text(
      '共完成${habit.totalCount}次',
      style: TextStyle(
        fontSize: 12,
        color: ColorsUtil.garyB1,
      ),
    );
  }

  /// 构建底部悬浮按钮
  Widget _buildFloatingButton(BuildContext context) {
    return Positioned(
      bottom: 24,
      left: 0,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: () => _showAddHabitDialog(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 8),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(7),
              boxShadow: ShadowsUtil.cardShadow,
            ),
            child: Text(
              "添加习惯",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 显示添加习惯弹窗
  void _showAddHabitDialog(BuildContext context) {
    HabitCheckInDialog.showAddHabitDialog(
      context,
      onConfirm: (habitUIState) {
        viewModel.onAddHabit(habitUIState);
        Get.snackbar('成功', '习惯添加成功');
      },
      onCancel: () {
        debugPrint('取消添加习惯');
      },
    );
  }
}
