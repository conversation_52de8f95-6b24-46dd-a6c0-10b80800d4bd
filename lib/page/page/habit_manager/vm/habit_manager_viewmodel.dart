import "package:flutter_kexue/data/enums/target_type.dart";
import "package:flutter_kexue/page/common_uistate/clock_uistate.dart";
import "package:get/get.dart";

import "habit_manager_us.dart";

/// @date 2025/06/29
/// @description HabitManager页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class HabitManagerViewModel extends GetxController {
  var us = HabitManagerUS();

  HabitManagerViewModel() {
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {}

  /// 处理筛选变化
  void onFilterChanged(int filterType) {
    us.selectedFilter.value = filterType;
    print('筛选类型变更为: $filterType');
  }

  /// 处理排序按钮点击
  void onSortPressed() {
    us.isEditMode.value = !us.isEditMode.value;
    print('编辑模式: ${us.isEditMode.value}');
  }

  /// 处理添加习惯按钮点击
  void onAddHabitPressed() {
    print('点击添加习惯');
  }

  /// 处理习惯项点击
  void onHabitItemPressed(ClockUIState habit) {
    if (us.isEditMode.value) {
    } else {}
  }

  /// 处理习惯项长按
  void onHabitLongPressed(ClockUIState habit) {
    // 进入编辑模式
    us.isEditMode.value = true;
  }

  /// 添加新习惯
  void onAddHabit(ClockUIState newHabit) {
    us.habitList.add(newHabit);
  }

  /// 处理习惯重排序
  void onReorderHabits(int oldIndex, int newIndex) {
    print('重排序: 从 $oldIndex 移动到 $newIndex');

    // 获取当前筛选后的列表
    final filteredHabits = getFilteredHabits();

    if (oldIndex < filteredHabits.length && newIndex <= filteredHabits.length) {
      // 调整 newIndex（ReorderableListView 的特殊处理）
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }

      // 获取要移动的习惯
      final movedHabit = filteredHabits[oldIndex];

      // 在原始列表中找到对应的索引
      final originalOldIndex = us.habitList.indexOf(movedHabit);

      // 从原始列表中移除
      us.habitList.removeAt(originalOldIndex);

      // 计算在原始列表中的新位置
      int originalNewIndex;
      if (newIndex >= filteredHabits.length - 1) {
        // 移动到最后
        originalNewIndex = us.habitList.length;
      } else {
        // 找到新位置对应的习惯在原始列表中的位置
        final targetHabit = filteredHabits[newIndex];
        originalNewIndex = us.habitList.indexOf(targetHabit);
        if (originalNewIndex == -1) {
          originalNewIndex = us.habitList.length;
        }
      }

      // 插入到新位置
      us.habitList.insert(originalNewIndex, movedHabit);
    }
  }

  /// 获取筛选后的习惯列表
  List<ClockUIState> getFilteredHabits() {
    switch (us.selectedFilter.value) {
      case 0: // 全部
        return us.habitList;
      case 1: // 正能量
        return us.habitList
            .where((habit) => habit.goalType == TargetType.clock)
            .toList();
      case 2: // 负能量
        return us.habitList
            .where((habit) => habit.goalType == TargetType.persist)
            .toList();
      default:
        return us.habitList;
    }
  }
}
