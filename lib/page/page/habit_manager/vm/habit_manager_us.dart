import 'package:flutter_kexue/data/mock/mock_data.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:get/get.dart';

/// @date 2025/06/29
/// @description JDetail页UI状态
class HabitManagerUS {
  // 习惯管理相关状态
  var selectedFilter = 0.obs; // 0-全部, 1-正能量, 2-负能量, 3-破戒, 4-多选记录
  var isEditMode = false.obs; // 是否处于编辑模式
  var habitList = <ClockUIState>[].obs; // 习惯列表
}
