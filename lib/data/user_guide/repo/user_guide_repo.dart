import 'package:flutter/foundation.dart';
import 'package:flutter_kexue/data/user_guide/ds/user_guide_lds.dart';
import 'package:flutter_kexue/data/user_guide/ds/user_guide_rds.dart';

/// 新用户引导仓库
class UserGuideRepo {
  final UserGuideRds _rds = UserGuideRds();
  final UserGuideLds _lds = UserGuideLds();

  /// 检查是否是新用户
  Future<bool> checkIsNewUser() async {
    try {
      // 先检查本地是否已完成引导
      final hasCompletedGuide = await _lds.hasCompletedGuide();
      if (hasCompletedGuide) {
        debugPrint('UserGuideRepo: 本地记录显示已完成引导');
        return false;
      }

      // 调用远程接口检查（目前返回默认值）
      final isNewUser = await _rds.checkIsNewUser();
      debugPrint('UserGuideRepo: 远程接口返回新用户状态 - $isNewUser');
      
      return isNewUser;
    } catch (e) {
      debugPrint('UserGuideRepo: 检查新用户状态失败 - $e');
      // 发生错误时，检查本地状态
      return !(await _lds.hasCompletedGuide());
    }
  }

  /// 标记引导已完成
  Future<void> markGuideCompleted() async {
    try {
      // 保存到本地
      await _lds.markGuideCompleted();
      
      // 可选：同步到服务器
      // await _rds.markGuideCompleted();
      
      debugPrint('UserGuideRepo: 引导完成状态已保存');
    } catch (e) {
      debugPrint('UserGuideRepo: 保存引导完成状态失败 - $e');
      rethrow;
    }
  }

  /// 重置引导状态（用于测试）
  Future<void> resetGuideStatus() async {
    try {
      await _lds.resetGuideStatus();
      debugPrint('UserGuideRepo: 引导状态已重置');
    } catch (e) {
      debugPrint('UserGuideRepo: 重置引导状态失败 - $e');
      rethrow;
    }
  }
}
