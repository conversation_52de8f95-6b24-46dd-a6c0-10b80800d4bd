import 'package:flutter/foundation.dart';

/// 新用户引导远程数据源
class UserGuideRds {

  /// 检查是否是新用户
  /// 目前没有接口，返回默认状态（新用户）
  Future<bool> checkIsNewUser() async {
    try {
      // TODO: 替换为真实的API接口
      // final response = await _httpClient.get(
      //   '/api/user/check_new_user',
      //   fromJson: (json) => json,
      // );
      // 
      // if (response.isSuccess) {
      //   return response.data['is_new_user'] ?? true;
      // }
      
      // 模拟网络请求延迟
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 默认返回新用户状态
      debugPrint('UserGuideRds: 模拟接口返回 - 新用户');
      return true;
      
    } catch (e) {
      debugPrint('UserGuideRds: 检查新用户状态失败 - $e');
      // 网络错误时默认返回新用户
      return true;
    }
  }

  /// 标记引导已完成（发送到服务器）
  Future<void> markGuideCompleted() async {
    try {
      // TODO: 替换为真实的API接口
      // final response = await _httpClient.post(
      //   '/api/user/complete_guide',
      //   data: {
      //     'completed_at': DateTime.now().toIso8601String(),
      //   },
      //   fromJson: (json) => json,
      // );
      // 
      // if (!response.isSuccess) {
      //   throw Exception('标记引导完成失败: ${response.message}');
      // }
      
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 300));
      
      debugPrint('UserGuideRds: 模拟发送引导完成状态到服务器');
      
    } catch (e) {
      debugPrint('UserGuideRds: 发送引导完成状态失败 - $e');
      rethrow;
    }
  }
}
