import 'package:flutter/foundation.dart';
import 'package:flutter_kexue/utils/store_util/shared_prefs.dart';

/// 新用户引导本地数据源
class UserGuideLds {
  static const String _keyGuideCompleted = 'user_guide_completed';
  static const String _keyGuideCompletedTime = 'user_guide_completed_time';

  final SharedPreferencesHelper _prefs = SharedPreferencesHelper();

  /// 检查是否已完成引导
  Future<bool> hasCompletedGuide() async {
    try {
      // 确保SharedPreferences已初始化
      if (!_prefs.isInitialized()) {
        await _prefs.init();
      }
      
      final completed = _prefs.getBool(_keyGuideCompleted) ?? false;
      debugPrint('UserGuideLds: 本地引导完成状态 - $completed');
      return completed;
    } catch (e) {
      debugPrint('UserGuideLds: 获取引导完成状态失败 - $e');
      return false;
    }
  }

  /// 标记引导已完成
  Future<void> markGuideCompleted() async {
    try {
      // 确保SharedPreferences已初始化
      if (!_prefs.isInitialized()) {
        await _prefs.init();
      }
      
      await _prefs.setBool(_keyGuideCompleted, true);
      await _prefs.setString(_keyGuideCompletedTime, DateTime.now().toIso8601String());
      
      debugPrint('UserGuideLds: 引导完成状态已保存到本地');
    } catch (e) {
      debugPrint('UserGuideLds: 保存引导完成状态失败 - $e');
      rethrow;
    }
  }

  /// 获取引导完成时间
  Future<DateTime?> getGuideCompletedTime() async {
    try {
      // 确保SharedPreferences已初始化
      if (!_prefs.isInitialized()) {
        await _prefs.init();
      }
      
      final timeStr = _prefs.getString(_keyGuideCompletedTime);
      if (timeStr != null && timeStr.isNotEmpty) {
        return DateTime.tryParse(timeStr);
      }
      return null;
    } catch (e) {
      debugPrint('UserGuideLds: 获取引导完成时间失败 - $e');
      return null;
    }
  }

  /// 重置引导状态（用于测试）
  Future<void> resetGuideStatus() async {
    try {
      // 确保SharedPreferences已初始化
      if (!_prefs.isInitialized()) {
        await _prefs.init();
      }
      
      await _prefs.remove(_keyGuideCompleted);
      await _prefs.remove(_keyGuideCompletedTime);
      
      debugPrint('UserGuideLds: 引导状态已重置');
    } catch (e) {
      debugPrint('UserGuideLds: 重置引导状态失败 - $e');
      rethrow;
    }
  }
}
