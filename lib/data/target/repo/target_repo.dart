import 'package:flutter_kexue/data/target/ds/model/net/targets_net_model.dart';
import 'package:flutter_kexue/data/target/ds/model/param/target_create_params_model.dart';
import 'package:flutter_kexue/data/target/ds/target_lds.dart';
import 'package:flutter_kexue/data/target/ds/target_rds.dart';
import 'package:flutter_kexue/net/model/base_response.dart';

/// 目标仓库
class TargetRepo {
  final TargetRds _targetRds = TargetRds();
  final TargetLds _targetLds = TargetLds();

  /// 创建目标
  Future<BaseResponse<dynamic>> createTarget(TargetCreateParams request) async {
    try {
      // 先尝试网络请求
      final response = await _targetRds.createTarget(request);
      return response;
    } catch (e) {
      return BaseResponse<dynamic>(
        code: -1,
        message: '创建目标失败: ${e.toString()}',
        data: null,
      );
    }
  }

  /// 更新目标
  Future<BaseResponse<dynamic>> updateTarget(TargetCreateParams request) async {
    try {
      // 先尝试网络请求
      final response = await _targetRds.updateTarget(request);
      return response;
    } catch (e) {
      return BaseResponse<dynamic>(
        code: -1,
        message: '更新目标失败: ${e.toString()}',
        data: null,
      );
    }
  }

  ///缓存目标天数
  cacheTargetDays(int targetDays) {
    _targetLds.cacheTargetDays(targetDays);
  }

  ///获取缓存天数
  int? getCachedTargetDays() {
    return _targetLds.getCachedTargetDays();
  }


  /// 获取目标列表
  Future<BaseResponse<TargetListNetModel>> getTargets(String planId) async {
    try {
      // 先尝试网络请求
      final response = await _targetRds.getTargets(planId);
      return response;
    } catch (e) {
      return BaseResponse<TargetListNetModel>(
        code: -1,
        message: '获取目标库失败: ${e.toString()}',
        data: null,
      );
    }
  }



}
