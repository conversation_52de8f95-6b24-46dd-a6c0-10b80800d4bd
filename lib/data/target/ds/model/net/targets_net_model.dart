

/// 目标管理网络模型
class TargetListNetModel {
  final List<TargetNetModel>? clock;
  final List<TargetNetModel>? control;

  TargetListNetModel({
    this.clock,
    this.control,
  });

  factory TargetListNetModel.fromJson(Map<String, dynamic> json) =>
      TargetListNetModel(
        clock: json['clock'] == null
            ? null
            : List<TargetNetModel>.from(
                json['clock'].map((x) => TargetNetModel.fromJson(x))),
        control: json['control'] == null
            ? null
            : List<TargetNetModel>.from(
                json['control'].map((x) => TargetNetModel.fromJson(x))),
      );
}

class TargetNetModel {
  final int id;
  final String target_name;
  final int target_type;
  final int? target_status;
  final int? target_day;
  final String? target_motivate;

  TargetNetModel({
    required this.id,
    required this.target_name,
    required this.target_type,
    this.target_status,
    this.target_day,
    this.target_motivate,
  });

  factory TargetNetModel.fromJson(Map<String, dynamic> json) => TargetNetModel(
        id: json['id'] ?? 0,
        target_name: json['target_name'] ?? '',
        target_type: json['target_type'] ?? 0,
        target_status: json['target_status'] ?? 0,
        target_day: json['target_day'] ?? 0,
        target_motivate: json['target_motivate'] ?? '',
      );
}
