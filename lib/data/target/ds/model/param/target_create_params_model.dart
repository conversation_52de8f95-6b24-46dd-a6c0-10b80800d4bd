import 'package:get/get.dart';

/// 创建目标请求参数模型
class TargetCreateParams {
  /// 目标ID
  final String? target_id;

  /// 计划ID
  final String plan_id;

  /// 目标名称
  final String target_name;

  /// 目标类型
  final int target_type;

  /// 目标开始日期
  final String? target_start_day;

  /// 目标结束日期
  final String? target_end_day;

  /// 目标休息天数
  final int? target_rest_day;

  /// 目标激励语
  final String? target_motivate;

  /// 目标日志开关
  final int target_log_switch;

  /// 打卡频率
  final String? clock_frequency;

  /// 目标天数
  final int? target_day;

  /// 目标每日数量
  final int? target_day_num;

  TargetCreateParams({
    required this.plan_id,
    required this.target_name,
    required this.target_type,
    this.target_start_day,
    this.target_end_day,
    this.target_rest_day,
    this.target_motivate,
    this.target_log_switch = 0,
    this.clock_frequency,
    this.target_day,
    this.target_day_num,
    this.target_id,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    json['plan_id'] = plan_id;
    json['target_name'] = target_name;
    json['target_type'] = target_type;
    if (target_id?.isNotEmpty == true) {
      json['target_id'] = target_id;
    }
    if (target_start_day?.isNotEmpty == true) {
      json['target_start_day'] = target_start_day;
    }

    if (target_end_day?.isNotEmpty == true) {
      json['target_end_day'] = target_end_day;
    }

    if (target_rest_day != null) {
      json['target_rest_day'] = target_rest_day;
    }

    if (target_motivate?.isNotEmpty == true) {
      json['target_motivate'] = target_motivate;
    }

    json['target_log_switch'] = target_log_switch;

    if (clock_frequency?.isNotEmpty == true) {
      json['clock_frequency'] = clock_frequency;
    }

    if (target_day != null) {
      json['target_day'] = target_day;
    }

    if (target_day_num != null) {
      json['target_day_num'] = target_day_num;
    }

    return json;
  }
}
