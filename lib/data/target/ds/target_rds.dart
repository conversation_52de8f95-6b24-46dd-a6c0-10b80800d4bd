import 'package:flutter_kexue/data/target/ds/model/net/targets_net_model.dart';
import 'package:flutter_kexue/data/target/ds/model/param/target_create_params_model.dart';
import 'package:flutter_kexue/net/http_client.dart';
import 'package:flutter_kexue/net/model/base_response.dart';

/// 目标远程数据源
class TargetRds {
  /// 创建目标
  Future<BaseResponse<dynamic>> createTarget(TargetCreateParams request) async {
    return await HttpClient.instance.post(
      '/api/target/create',
      data: request.toJson(),
      isShowLoading: true,
      fromJson: (json) => json,
    );
  }

  /// 更新目标
  Future<BaseResponse<dynamic>> updateTarget(TargetCreateParams request) async {
    return await HttpClient.instance.post(
      '/api/target/edit',
      data: request.toJson(),
      isShowLoading: true,
      fromJson: (json) => json,
    );
  }

  /// 获取目标列表
  /// ``` plan_id: 计划ID ```
  Future<BaseResponse<TargetListNetModel>> getTargets(String planId) async {
    return await HttpClient.instance.get(
      '/api/target/get_targets',
      queryParameters : {
        "plan_id": int.parse(planId)
      },
      isShowLoading: true,
      fromJson: (json) => TargetListNetModel.fromJson(json as Map<String, dynamic>),
    );
  }
}
