import 'package:flutter_kexue/utils/store_util/shared_prefs.dart';
import 'package:flutter_kexue/utils/system_util/jprint.dart';
import 'package:flutter_kexue/utils/user_manager.dart';

/// 目标本地数据源
class TargetLds {
  /// 缓存键前缀
  static const String _cacheKeyPrefix = 'target_days_cache_';

  /// SharedPreferences实例
  final _prefs = SharedPreferencesHelper();

  /// 根据用户ID缓存目标天数
  /// [targetDays] 目标天数，无限期传入-1
  Future<void> cacheTargetDays(int targetDays) async {
    try {
      final userId = UserManager.instance.userId;
      if (userId != null && userId.isNotEmpty) {
        final cacheKey = '$_cacheKeyPrefix$userId';
        await _prefs.setInt(cacheKey, targetDays);
        jprint('缓存目标天数成功: cacheKey=$cacheKey, targetDays=$targetDays');
      }
    } catch (e) {
      jprint('缓存目标天数失败: $e');
    }
  }

  int? getCachedTargetDays() {
    try {
      final userId = UserManager.instance.userId;
      if (userId != null && userId.isNotEmpty) {
        final cacheKey = '$_cacheKeyPrefix$userId';

        // 确保SharedPreferences已初始化
        if (!_prefs.isInitialized()) {
          jprint('SharedPreferences未初始化，无法获取缓存');
          return null;
        }

        final cachedDays = _prefs.getInt(cacheKey);
        jprint('获取缓存目标天数: cacheKey=$cacheKey, cachedDays=$cachedDays');
        return cachedDays;
      }
      jprint('用户ID为空，无法获取缓存');
      return null;
    } catch (e) {
      jprint('获取缓存目标天数异常: $e');
      return null;
    }
  }
}
