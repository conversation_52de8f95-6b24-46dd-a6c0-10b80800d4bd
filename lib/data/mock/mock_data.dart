

import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/data/enums/record_type.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/record_uistate.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item.dart';

class MockData {
  static List<RecordUIState> getRecordList() {
    return [
      RecordUIState(
        system: true,
        type: RecordType.photo,
        icon: "👤",
        title: "今天的我",
      ),
      RecordUIState(
        system: true,
        type: RecordType.diary,
        icon: "📖",
        title: "日记",
        content: "无",
      ),
      RecordUIState(
        system: true,
        type: RecordType.multiple,
        icon: "😷",
        title: "感受",
        content: "头痛、乏力、失眠、腰酸...",
      ),
      RecordUIState(
        system: false,
        type: RecordType.multiple,
        title: "自定义日记",
        content: "无",
      ),
      RecordUIState(
        system: false,
        type: RecordType.multiple,
        title: "自定义多选",
        content: "头痛、乏力、失眠、腰酸...",
      ),
    ];
  }

  static List<ClockUIState> getClockList() {
    return [
      ClockUIState(
        target: "锻炼",
        goalType: TargetType.persist,
        goalDay: 10,
        totalCount: 30,
        dailyCount: 3,
        motivation: "今天成都下大暴雨，我的心情很差，今天成都下大暴雨...",
        progress: 50,
      ),
      ClockUIState(
        target: "锻炼",
        goalType: TargetType.clock,
        goalDay: 10,
        totalCount: 30,
        dailyCount: 3,
        motivation: "今天成都下大暴雨，我的心情很差，今天成都下大暴雨...",
        progress: 50,
      ),
      ClockUIState(
        target: "打八段锦",
        goalType: TargetType.clock,
        goalDay: 10,
        totalCount: 30,
        dailyCount: 3,
        motivation: "今天成都下大暴雨，我的心情很差，今天成都下大暴雨...",
        progress: 50,
      ),
      ClockUIState(
        target: "冥想",
        goalType: TargetType.clock,
        goalDay: 10,
        totalCount: 30,
        dailyCount: 3,
        motivation: "今天成都下大暴雨，我的心情很差，今天成都下大暴雨...",
        progress: 50,
        images: [
          "https://picsum.photos/200/300",
          "https://picsum.photos/200/300",
          "https://picsum.photos/200/300",
        ],
      ),
      ClockUIState(
        target: "看黄",
        goalType: TargetType.clock,
        goalDay: 10,
        totalCount: 30,
        dailyCount: 3,
        motivation: "不到最后绝不轻言放弃",
        progress: 50,
      ),
    ];
  }

  /// 创建 Grid 测试数据
  static List<TDActionSheetItem> createGridMockData() {
    return [
      TDActionSheetItem(label: '早睡'),
      TDActionSheetItem(label: '健康饮食'),
      TDActionSheetItem(label: '运动无极信息'),
      TDActionSheetItem(label: '早睡'),
      TDActionSheetItem(label: '健康饮食'),
      TDActionSheetItem(label: '运动无极信息'),
      TDActionSheetItem(label: '拒绝拖延症'),
      TDActionSheetItem(label: '定期联系朋友'),
      TDActionSheetItem(label: '坚持复盘时间管理'),
      TDActionSheetItem(label: '定期联系朋友'),
      TDActionSheetItem(label: '运动无极信息'),
      TDActionSheetItem(label: '拒绝拖延拒绝'),
      TDActionSheetItem(label: '添加', isAddButton: true),
    ];
  }

  /// 创建 List 测试数据
  static List<TDActionSheetItem> createListMockData() {
    return [
      TDActionSheetItem(id: '1', label: '手艺活', count: 10),
      TDActionSheetItem(id: '2', label: '夫妻之欢', count: 0),
      TDActionSheetItem(id: '3', label: '早睡', count: 5),
      TDActionSheetItem(id: '4', label: '健康饮食', count: 3),
      TDActionSheetItem(id: '5', label: '运动锻炼', count: 8),
      TDActionSheetItem(id: '6', label: '阅读学习', count: 2),
    ];
  }


}
