
/// 账户业务模型，包含基本的信息
class AccountBizModel {
  /// 用户ID
  final String? userId;

  /// 用户名
  final String? username;

  /// 手机号
  final String? phone;

  /// 昵称
  final String? nickname;

  /// 头像URL
  final String? avatar;

  /// 邮箱
  final String? email;

  /// 性别 0-未知 1-男 2-女
  final int? gender;

  /// 生日
  final String? birthday;

  /// 用户状态 0-正常 1-禁用
  final int? status;

  /// 访问令牌
  final String? accessToken;

  /// 令牌过期时间
  final String? tokenExpireTime;

  const AccountBizModel({
    this.userId,
    this.username,
    this.phone,
    this.nickname,
    this.avatar,
    this.email,
    this.gender,
    this.birthday,
    this.status,
    this.accessToken,
    this.tokenExpireTime,
  });

  /// 从 JSON 创建实例
  factory AccountBizModel.fromJson(Map<String, dynamic> json) {
    return AccountBizModel(
      userId: json['userId'] as String?,
      username: json['username'] as String?,
      phone: json['phone'] as String?,
      nickname: json['nickname'] as String?,
      avatar: json['avatar'] as String?,
      email: json['email'] as String?,
      gender: json['gender'] as int?,
      birthday: json['birthday'] as String?,
      status: json['status'] as int?,
      accessToken: json['accessToken'] as String?,
      tokenExpireTime: json['tokenExpireTime'] as String?,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'username': username,
      'phone': phone,
      'nickname': nickname,
      'avatar': avatar,
      'email': email,
      'gender': gender,
      'birthday': birthday,
      'status': status,
      'accessToken': accessToken,
    };
  }

  /// 复制并修改部分字段
  AccountBizModel copyWith({
    String? userId,
    String? username,
    String? phone,
    String? nickname,
    String? avatar,
    String? email,
    int? gender,
    String? birthday,
    String? registerTime,
    String? lastLoginTime,
    int? status,
    String? accessToken,
    String? refreshToken,
    String? tokenExpireTime,
  }) {
    return AccountBizModel(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      email: email ?? this.email,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
      status: status ?? this.status,
      accessToken: accessToken ?? this.accessToken,
      tokenExpireTime: tokenExpireTime ?? this.tokenExpireTime,
    );
  }

  /// 判断是否已登录（有有效的访问令牌）
  bool get isLoggedIn {
    return accessToken != null && accessToken!.isNotEmpty;
  }

  /// 判断令牌是否过期
  bool get isTokenExpired {
    if (tokenExpireTime == null) return true;

    try {
      final expireTime = DateTime.parse(tokenExpireTime!);
      return DateTime.now().isAfter(expireTime);
    } catch (e) {
      return true;
    }
  }

  @override
  String toString() {
    return 'AccountBizModel{userId: $userId, username: $username, phone: $phone, nickname: $nickname, isLoggedIn: $isLoggedIn}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AccountBizModel &&
        other.userId == userId &&
        other.username == username &&
        other.phone == phone &&
        other.accessToken == accessToken;
  }

  @override
  int get hashCode {
    return userId.hashCode ^
        username.hashCode ^
        phone.hashCode ^
        accessToken.hashCode;
  }
}