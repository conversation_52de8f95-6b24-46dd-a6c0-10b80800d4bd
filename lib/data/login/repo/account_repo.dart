import 'package:flutter/widgets.dart';
import 'package:flutter_kexue/data/login/ds/account_lds.dart';
import 'package:flutter_kexue/data/login/repo/model/account_biz_model.dart';
import 'package:flutter_kexue/net/model/base_response.dart';

/// 账户仓库
/// 负责账户相关的业务逻辑处理，作为数据源和业务层之间的桥梁
class AccountRepo {
  final AccountRds _accountRds = AccountRds();

  /// 获取当前用户信息
  /// 从本地存储获取用户账户信息
  Future<AccountBizModel?> getCurrentUser() async {
    try {
      return await _accountRds.getUserInfo();
    } catch (e) {
      debugPrint('AccountRepo: 获取当前用户信息失败 - $e');
      return null;
    }
  }

  /// 保存用户信息
  /// 将用户信息保存到本地存储
  Future<bool> saveUser(AccountBizModel userInfo) async {
    try {
      final success = await _accountRds.saveUserInfo(userInfo);
      if (success) {
        debugPrint('AccountRepo: 用户信息保存成功');
      } else {
        debugPrint('AccountRepo: 用户信息保存失败');
      }
      return success;
    } catch (e) {
      debugPrint('AccountRepo: 保存用户信息异常 - $e');
      return false;
    }
  }

  /// 检查用户是否已登录
  /// 检查本地存储的登录状态和令牌有效性
  Future<bool> isUserLoggedIn() async {
    try {
      return await _accountRds.isLoggedIn();
    } catch (e) {
      debugPrint('AccountRepo: 检查登录状态失败 - $e');
      return false;
    }
  }

  /// 获取访问令牌
  /// 获取当前有效的访问令牌
  Future<String?> getAccessToken() async {
    try {
      // 首先检查是否已登录
      final isLoggedIn = await isUserLoggedIn();
      if (!isLoggedIn) {
        return null;
      }
      
      return await _accountRds.getAccessToken();
    } catch (e) {
      debugPrint('AccountRepo: 获取访问令牌失败 - $e');
      return null;
    }
  }

  /// 获取刷新令牌
  /// 获取当前的刷新令牌
  Future<String?> getRefreshToken() async {
    try {
      // return await _accountRds.getRefreshToken();
      return null;
    } catch (e) {
      debugPrint('AccountRepo: 获取刷新令牌失败 - $e');
      return null;
    }
  }

  /// 刷新访问令牌
  /// 使用刷新令牌更新访问令牌
  Future<bool> refreshAccessToken(String newAccessToken, {String? newRefreshToken, String? expireTime}) async {
    try {
      final success = await _accountRds.updateAccessToken(
        newAccessToken,
        refreshToken: newRefreshToken,
        expireTime: expireTime,
      );
      
      if (success) {
        debugPrint('AccountRepo: 访问令牌刷新成功');
      } else {
        debugPrint('AccountRepo: 访问令牌刷新失败');
      }
      
      return success;
    } catch (e) {
      debugPrint('AccountRepo: 刷新访问令牌异常 - $e');
      return false;
    }
  }

  /// 用户登录
  /// 处理用户登录逻辑，保存登录信息
  Future<BaseResponse<AccountBizModel>> login({
    required String phone,
    required String verificationCode,
    String? username,
    String? password,
  }) async {
    try {
      // 这里应该调用登录API
      // 暂时模拟登录成功的情况
      
      // 模拟API返回的用户信息
      final userInfo = AccountBizModel(
        userId: 'user_${DateTime.now().millisecondsSinceEpoch}',
        username: username ?? phone,
        phone: phone,
        nickname: username ?? '用户$phone',
        accessToken: 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
        status: 0,
      );
      
      // 保存用户信息到本地
      final saveSuccess = await saveUser(userInfo);
      if (!saveSuccess) {
        return BaseResponse.error(message: '保存用户信息失败');
      }
      return BaseResponse.success(data: userInfo);
    } catch (e) {
      return BaseResponse.error(message:'登录失败: $e');
    }
  }

  /// 用户退出登录
  /// 清除本地存储的用户信息
  Future<bool> logout() async {
    try {
      final success = await _accountRds.clearUserInfo();
      if (success) {
        debugPrint('AccountRepo: 用户退出登录成功');
      } else {
        debugPrint('AccountRepo: 用户退出登录失败');
      }
      return success;
    } catch (e) {
      debugPrint('AccountRepo: 用户退出登录异常 - $e');
      return false;
    }
  }

  /// 更新用户资料
  /// 更新用户的基本信息
  Future<bool> updateUserProfile({
    String? nickname,
    String? avatar,
    String? email,
    int? gender,
    String? birthday,
  }) async {
    try {
      final success = await _accountRds.updateUserProfile(
        nickname: nickname,
        avatar: avatar,
        email: email,
        gender: gender,
        birthday: birthday,
      );
      
      if (success) {
        debugPrint('AccountRepo: 用户资料更新成功');
      } else {
        debugPrint('AccountRepo: 用户资料更新失败');
      }
      
      return success;
    } catch (e) {
      debugPrint('AccountRepo: 更新用户资料异常 - $e');
      return false;
    }
  }

  /// 检查令牌是否即将过期
  /// 检查访问令牌是否需要刷新
  Future<bool> shouldRefreshToken() async {
    try {
      return await _accountRds.isTokenExpiringSoon();
    } catch (e) {
      debugPrint('AccountRepo: 检查令牌过期状态失败 - $e');
      return true;
    }
  }

  /// 获取用户基本信息摘要
  /// 获取用户的基本显示信息
  Future<Map<String, String?>> getUserSummary() async {
    try {
      final userInfo = await getCurrentUser();
      if (userInfo == null) {
        return {};
      }
      
      return {
        'userId': userInfo.userId,
        'username': userInfo.username,
        'phone': userInfo.phone,
        'nickname': userInfo.nickname,
        'avatar': userInfo.avatar,
        'isLoggedIn': userInfo.isLoggedIn.toString(),
      };
    } catch (e) {
      debugPrint('AccountRepo: 获取用户摘要信息失败 - $e');
      return {};
    }
  }

  /// 验证当前会话是否有效
  /// 检查用户登录状态和令牌有效性
  Future<bool> validateSession() async {
    try {
      final isLoggedIn = await isUserLoggedIn();
      if (!isLoggedIn) {
        return false;
      }
      
      final userInfo = await getCurrentUser();
      if (userInfo == null) {
        return false;
      }
      
      return true;
    } catch (e) {
      debugPrint('AccountRepo: 验证会话失败 - $e');
      return false;
    }
  }
}
