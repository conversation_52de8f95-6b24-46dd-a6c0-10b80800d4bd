
import 'package:flutter_kexue/data/login/ds/model/param/login_params_model.dart';
import 'package:flutter_kexue/data/login/ds/user_rds.dart';
import 'package:flutter_kexue/net/model/base_response.dart';

import '../ds/model/net/login_net_model.dart';

class UserRepo {

  final _userRds = UserRds();

  Future<BaseResponse<LoginResponse>> login(LoginRequest request) async {
    return await _userRds.login(request);
  }

  Future<BaseResponse<String>> sendVerificationCode(String tel) async {
    return await _userRds.sendVerificationCode(tel);
  }

}