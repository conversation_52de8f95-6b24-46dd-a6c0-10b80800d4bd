import 'package:flutter_kexue/net/http_client.dart';
import 'package:flutter_kexue/net/model/base_response.dart';

import 'model/net/login_net_model.dart';
import 'model/param/login_params_model.dart';

/// 用户API服务
class UserRds {
   final HttpClient _httpClient = HttpClient.instance;

  /// 发送验证码
   Future<BaseResponse<String>> sendVerificationCode(String phone) async {
    return await _httpClient.get<String>(
      '/api/sms/login',
      params: {'tel': phone},
      isShowLoading: true,
      fromJson: (json) => json.toString(),
    );
  }

  /// 用户登录
   Future<BaseResponse<LoginResponse>> login(LoginRequest request) async {
    return await _httpClient.post<LoginResponse>(
      '/api/user/login',
      data: request.toJson(),
      isShowLoading: true,
      fromJson: (json) => LoginResponse.fromJson(json as Map<String, dynamic>),
    );
  }
}
