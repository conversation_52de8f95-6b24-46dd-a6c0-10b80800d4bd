import 'package:json_annotation/json_annotation.dart';

part 'login_net_model.g.dart';

/// 登录响应模型
@JsonSerializable()
class LoginResponse {
  final String userId;
  final String username;
  final String avatar;
  final String token;

  LoginResponse({
    required this.userId,
    required this.username,
    required this.avatar,
    required this.token,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}
