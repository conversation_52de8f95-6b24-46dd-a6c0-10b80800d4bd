part 'login_net_model.g.dart';

/// 登录响应模型
class LoginResponse {
  final String userId;
  final String username;
  final String avatar;
  final String token;

  LoginResponse({
    required this.userId,
    required this.username,
    required this.avatar,
    required this.token,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        userId: json['userId'] as String,
        username: json['username'] as String,
        avatar: json['avatar'] as String,
        token: json['token'] as String,
      );
}
