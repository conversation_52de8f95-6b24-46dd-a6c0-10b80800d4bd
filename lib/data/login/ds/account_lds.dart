import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_kexue/data/login/repo/model/account_biz_model.dart';

/// 账户远程数据源 (Remote Data Source)
/// 负责用户账户信息的本地存储和获取
class AccountRds {
  static const String _keyUserInfo = 'user_info';
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyAccessToken = 'access_token';

  /// 获取 SharedPreferences 实例
  Future<SharedPreferences> get _prefs async {
    return await SharedPreferences.getInstance();
  }

  /// 获取用户信息
  /// 返回存储的用户账户信息，如果没有则返回 null
  Future<AccountBizModel?> getUserInfo() async {
    try {
      final prefs = await _prefs;
      final userInfoJson = prefs.getString(_keyUserInfo);
      
      if (userInfoJson == null || userInfoJson.isEmpty) {
        return null;
      }
      
      final Map<String, dynamic> userMap = jsonDecode(userInfoJson);
      return AccountBizModel.fromJson(userMap);
    } catch (e) {
      print('获取用户信息失败: $e');
      return null;
    }
  }

  /// 存储用户信息
  /// 将用户账户信息序列化为 JSON 并存储到本地
  Future<bool> saveUserInfo(AccountBizModel userInfo) async {
    try {
      final prefs = await _prefs;
      final userInfoJson = jsonEncode(userInfo.toJson());
      
      // 存储用户信息
      await prefs.setString(_keyUserInfo, userInfoJson);
      
      // 存储登录状态
      await prefs.setBool(_keyIsLoggedIn, userInfo.isLoggedIn);
      
      // 单独存储令牌信息，方便快速访问
      if (userInfo.accessToken != null) {
        await prefs.setString(_keyAccessToken, userInfo.accessToken!);
      }
      

      return true;
    } catch (e) {
      print('存储用户信息失败: $e');
      return false;
    }
  }

  /// 判断是否已登录
  /// 检查本地存储的登录状态和令牌有效性
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await _prefs;
      
      // 首先检查登录状态标志
      final isLoggedInFlag = prefs.getBool(_keyIsLoggedIn) ?? false;
      if (!isLoggedInFlag) {
        return false;
      }
      
      // 检查是否有访问令牌
      final accessToken = prefs.getString(_keyAccessToken);
      if (accessToken == null || accessToken.isEmpty) {
        return false;
      }
      
      // 获取完整用户信息检查令牌是否过期
      final userInfo = await getUserInfo();
      if (userInfo == null) {
        return false;
      }
      
      return true;
    } catch (e) {
      print('检查登录状态失败: $e');
      return false;
    }
  }

  /// 获取访问令牌
  /// 快速获取存储的访问令牌
  Future<String?> getAccessToken() async {
    try {
      final prefs = await _prefs;
      return prefs.getString(_keyAccessToken);
    } catch (e) {
      print('获取访问令牌失败: $e');
      return null;
    }
  }

  /// 更新访问令牌
  /// 更新用户信息中的访问令牌
  Future<bool> updateAccessToken(String accessToken, {String? refreshToken, String? expireTime}) async {
    try {
      final userInfo = await getUserInfo();
      if (userInfo == null) {
        return false;
      }
      
      final updatedUserInfo = userInfo.copyWith(accessToken: accessToken);
      
      return await saveUserInfo(updatedUserInfo);
    } catch (e) {
      print('更新访问令牌失败: $e');
      return false;
    }
  }

  /// 清除用户信息
  /// 清除所有存储的用户相关信息，用于退出登录
  Future<bool> clearUserInfo() async {
    try {
      final prefs = await _prefs;
      
      await prefs.remove(_keyUserInfo);
      await prefs.remove(_keyIsLoggedIn);
      await prefs.remove(_keyAccessToken);

      return true;
    } catch (e) {
      print('清除用户信息失败: $e');
      return false;
    }
  }

  /// 更新用户基本信息
  /// 更新用户的基本信息（不包括令牌）
  Future<bool> updateUserProfile({
    String? nickname,
    String? avatar,
    String? email,
    int? gender,
    String? birthday,
  }) async {
    try {
      final userInfo = await getUserInfo();
      if (userInfo == null) {
        return false;
      }
      
      final updatedUserInfo = userInfo.copyWith(
        nickname: nickname ?? userInfo.nickname,
        avatar: avatar ?? userInfo.avatar,
        email: email ?? userInfo.email,
        gender: gender ?? userInfo.gender,
        birthday: birthday ?? userInfo.birthday,
      );
      
      return await saveUserInfo(updatedUserInfo);
    } catch (e) {
      print('更新用户资料失败: $e');
      return false;
    }
  }

  /// 检查令牌是否即将过期
  /// 检查令牌是否在指定时间内过期（默认30分钟）
  Future<bool> isTokenExpiringSoon({Duration threshold = const Duration(minutes: 30)}) async {
    try {
      final userInfo = await getUserInfo();
      if (userInfo == null || userInfo.tokenExpireTime == null) {
        return true;
      }
      
      final expireTime = DateTime.parse(userInfo.tokenExpireTime!);
      final now = DateTime.now();
      final timeUntilExpiry = expireTime.difference(now);
      
      return timeUntilExpiry <= threshold;
    } catch (e) {
      print('检查令牌过期时间失败: $e');
      return true;
    }
  }
}
