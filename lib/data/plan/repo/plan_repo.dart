import 'package:flutter_kexue/data/plan/ds/model/net/plan_create_net_model.dart';
import 'package:flutter_kexue/data/plan/ds/model/net/plans_net_model.dart';
import 'package:flutter_kexue/data/plan/ds/model/param/plan_create_params_model.dart';
import 'package:flutter_kexue/data/plan/ds/model/param/plan_edit_params_model.dart';
import 'package:flutter_kexue/data/plan/ds/plan_rds.dart';
import 'package:flutter_kexue/net/model/base_response.dart';

/// 计划仓库
class PlanRepo {
  final PlanRds _planRds = PlanRds();

  /// 创建计划
  Future<BaseResponse<PlanCreateNetModel>> createPlan(
      PlanCreateParams params) async {
    try {
      final response = await _planRds.createPlan(params);
      return response;
    } catch (e) {
      return BaseResponse<PlanCreateNetModel>(
        code: -1,
        message: '创建计划失败: ${e.toString()}',
        data: null,
      );
    }
  }

  /// 修改计划
  Future<BaseResponse<dynamic>> updatePlan(PlanEditParams params) async {
    try {
      final response = await _planRds.updatePlan(params);
      return response;
    } catch (e) {
      return BaseResponse<dynamic>(
        code: -1,
        message: '修改计划失败: ${e.toString()}',
      );
    }
  }

  ///获取计划列表
  Future<BaseResponse<List<PlanNetModel>>> getPlanList() async {
    try {
      final response = await _planRds.getPlanList();
      return response;
    } catch (e) {
      return BaseResponse<List<PlanNetModel>>(
        code: -1,
        message: '获取计划列表失败: ${e.toString()}',
        data: null,
      );
    }
  }
}
