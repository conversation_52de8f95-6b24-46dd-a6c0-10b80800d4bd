class PlanNetModel {
  final int id;
  final String plan_name;
  final int completion;
  final int status;
  final String? brief_introduction;

  PlanNetModel({
    required this.id,
    required this.plan_name,
    required this.completion,
    required this.status,
    this.brief_introduction,
  });

  factory PlanNetModel.fromJson(Map<String, dynamic> json) => PlanNetModel(
        id: (json['id'] as num).toInt(),
        plan_name: json['plan_name']?.toString() ?? '',
        completion: (json['completion'] as num).toInt(),
        status: (json['status'] as num).toInt(),
        brief_introduction: json['brief_introduction']?.toString() ?? '',
      );
}
