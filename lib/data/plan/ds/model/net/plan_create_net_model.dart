/// 创建计划响应模型
class PlanCreateNetModel {
  /// 计划ID
  final int? id;

  /// 计划名称
  final String? plan_name;

  /// 简介
  final String? brief_introduction;

  const PlanCreateNetModel({
    this.id,
    this.plan_name,
    this.brief_introduction,
  });

  factory PlanCreateNetModel.fromJson(Map<String, dynamic> json) =>
      PlanCreateNetModel(
        id: (json['id'] as num).toInt(),
        plan_name: json['plan_name']?.toString() ?? '',
        brief_introduction: json['brief_introduction']?.toString() ?? '',
      );
}
