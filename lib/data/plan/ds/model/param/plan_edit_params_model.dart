
/// 修改计划请求参数模型
class PlanEditParams {
  /// 计划名称
  final String plan_id;

  /// 计划名称
  final String plan_name;

  /// 简介
  final String? brief_introduction;


  PlanEditParams({
    required this.plan_id,
    required this.plan_name,
    this.brief_introduction,
  });

  Map<String, dynamic> toJson() {
    return {
      'plan_name': plan_name,
      'plan_id': plan_id,
      'brief_introduction': brief_introduction,
    };
  }
}
