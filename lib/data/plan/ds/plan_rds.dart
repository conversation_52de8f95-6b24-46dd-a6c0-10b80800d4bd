import 'package:flutter_kexue/data/plan/ds/model/net/plan_create_net_model.dart';
import 'package:flutter_kexue/data/plan/ds/model/net/plans_net_model.dart';
import 'package:flutter_kexue/data/plan/ds/model/param/plan_create_params_model.dart';
import 'package:flutter_kexue/data/plan/ds/model/param/plan_edit_params_model.dart';
import 'package:flutter_kexue/net/http_client.dart';
import 'package:flutter_kexue/net/model/base_response.dart';

/// 计划远程数据源
class PlanRds {
  final HttpClient _httpClient = HttpClient.instance;

  /// 创建计划
  Future<BaseResponse<PlanCreateNetModel>> createPlan(
      PlanCreateParams params) async {
    return await _httpClient.post(
      '/api/plan/create',
      data: params.toJson(),
      isShowLoading: true,
      fromJson: (json) =>
          PlanCreateNetModel.fromJson(json as Map<String, dynamic>),
    );
  }

  ///修改计划
  Future<BaseResponse<dynamic>> updatePlan(PlanEditParams params) async {
    return await _httpClient.post(
      '/api/plan/edit',
      data: params.toJson(),
      isShowLoading: true,
      fromJson: (json) => json,
    );
  }

  ///计划列表
  Future<BaseResponse<List<PlanNetModel>>> getPlanList() async {
    return await _httpClient.get(
      '/api/plan/get_plans',
      isShowLoading: true,
      fromJson: (json) => List<PlanNetModel>.from(json.map((x) => PlanNetModel.fromJson(x))),
    );
  }
}
