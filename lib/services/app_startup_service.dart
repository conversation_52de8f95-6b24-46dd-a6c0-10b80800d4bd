import 'package:flutter/foundation.dart';
import 'package:flutter_kexue/data/user_guide/repo/user_guide_repo.dart';
import 'package:get/get.dart';

/// 应用启动服务
/// 负责检查新用户状态并决定启动流程
class AppStartupService {
  static final AppStartupService _instance = AppStartupService._internal();
  factory AppStartupService() => _instance;
  AppStartupService._internal();

  final UserGuideRepo _userGuideRepo = UserGuideRepo();

  /// 检查并处理应用启动流程
  Future<void> handleAppStartup() async {
    try {
      debugPrint('AppStartupService: 开始检查应用启动流程');
      
      // 检查是否是新用户
      final isNewUser = await _userGuideRepo.checkIsNewUser();
      
      if (isNewUser) {
        debugPrint('AppStartupService: 检测到新用户，跳转到引导页面');
        // 跳转到新用户引导页面
        Get.offAllNamed('/user_guide');
      } else {
        debugPrint('AppStartupService: 老用户，跳转到主页面');
        // 跳转到主页面
        Get.offAllNamed('/home');
      }
      
    } catch (e) {
      debugPrint('AppStartupService: 处理启动流程失败 - $e');
      // 发生错误时默认跳转到主页面
      Get.offAllNamed('/home');
    }
  }

  /// 强制显示引导页面（用于测试）
  Future<void> forceShowGuide() async {
    try {
      debugPrint('AppStartupService: 强制显示引导页面');
      await _userGuideRepo.resetGuideStatus();
      Get.offAllNamed('/user_guide');
    } catch (e) {
      debugPrint('AppStartupService: 强制显示引导页面失败 - $e');
    }
  }

  /// 检查是否需要显示引导页面
  Future<bool> shouldShowGuide() async {
    try {
      return await _userGuideRepo.checkIsNewUser();
    } catch (e) {
      debugPrint('AppStartupService: 检查是否需要显示引导页面失败 - $e');
      return false;
    }
  }
}
