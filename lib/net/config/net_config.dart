import 'package:flutter_kexue/utils/user_manager.dart';

/// 网络请求配置
class NetConfig {
  /// 基础URL
  static const String baseUrl = "http://api.kexueplan.com";

  /// 连接超时时间（毫秒）
  static const int connectTimeout = 15000;

  /// 接收超时时间（毫秒）
  static const int receiveTimeout = 15000;

  /// 发送超时时间（毫秒）
  static const int sendTimeout = 15000;

  /// 是否启用日志
  static const bool enableLog = true;

  /// 成功状态码列表
  static const List<int> successCodes = [200, 0];

  /// 请求头
  static Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'token': UserManager.instance.accessToken ?? "",
  };

  /// API版本
  static const String apiVersion = 'v1';

  /// 是否启用缓存
  static const bool enableCache = false;

  /// 缓存最大时间（秒）
  static const int cacheMaxAge = 300;

  /// 重试次数
  static const int retryCount = 3;

  /// 重试间隔（毫秒）
  static const int retryInterval = 1000;
}
