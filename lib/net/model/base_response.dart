import 'package:flutter/foundation.dart';

/// 基础响应模型
class BaseResponse<T> {
  /// 响应码
  final int code;

  /// 响应消息
  final String message;

  /// 响应数据
  final T? data;

  const BaseResponse({
    required this.code,
    required this.message,
    this.data,
  });

  /// 是否成功
  bool get isSuccess => code == 200 || code == 0;

  /// 是否失败
  bool get isFailure => !isSuccess;

  /// 工厂构造函数，从JSON创建
  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) {
    try {
      debugPrint('BaseResponse.fromJson - 输入JSON: $json');

      // 如果JSON中没有code和message字段，说明是直接返回data的格式
      if (!json.containsKey('code') && !json.containsKey('message')) {
        debugPrint('BaseResponse.fromJson - 检测到非标准格式，缺少code和message字段');

        // 检查是否有data字段
        if (json.containsKey('data')) {
          debugPrint('BaseResponse.fromJson - 找到data字段，提取data部分');
          // 有data字段，提取data部分
          return BaseResponse<T>(
            code: 200,
            message: 'success',
            data: fromJsonT(json['data']),
          );
        } else {
          debugPrint('BaseResponse.fromJson - 没有data字段，直接处理整个json');
          // 没有data字段，直接将整个json作为data处理
          return BaseResponse<T>(
            code: 200,
            message: 'success',
            data: fromJsonT(json),
          );
        }
      }
      debugPrint('BaseResponse.fromJson - 检测到有code和message字段，进行类型转换');
      // 手动构造BaseResponse
      return BaseResponse<T>(
        code: (json['code'] as num).toInt(),
        message: json['message']?.toString() ?? '',
        data: json.containsKey('data') ? fromJsonT(json['data']) : null,
      );
    } catch (e) {
      debugPrint('BaseResponse.fromJson - 解析失败: $e');
      // 解析失败时返回错误响应
      return BaseResponse<T>(
        code: -1,
        message: 'JSON解析失败: ${e.toString()}',
        data: null,
      );
    }
  }

  /// 创建成功响应
  factory BaseResponse.success({
    T? data,
    String message = 'success',
    int code = 200,
  }) {
    return BaseResponse<T>(
      code: code,
      message: message,
      data: data,
    );
  }

  /// 创建失败响应
  factory BaseResponse.error({
    required String message,
    int code = -1,
    T? data,
  }) {
    return BaseResponse<T>(
      code: code,
      message: message,
      data: data,
    );
  }

  @override
  String toString() {
    return 'BaseResponse{code: $code, msg: $message, data: $data}';
  }
}
