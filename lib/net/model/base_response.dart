import 'package:json_annotation/json_annotation.dart';

part 'base_response.g.dart';

/// 基础响应模型
@JsonSerializable(genericArgumentFactories: true)
class BaseResponse<T> {
  /// 响应码
  final int code;

  /// 响应消息
  final String message;

  /// 响应数据
  final T? data;

  const BaseResponse({
    required this.code,
    required this.message,
    this.data,
  });

  /// 是否成功
  bool get isSuccess => code == 200 || code == 0;

  /// 是否失败
  bool get isFailure => !isSuccess;

  /// 工厂构造函数，从JSON创建
  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$BaseResponseFromJson(json, fromJsonT);

  /// 转换为JSON
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$BaseResponseToJson(this, toJsonT);

  /// 创建成功响应
  factory BaseResponse.success({
    T? data,
    String message = 'success',
    int code = 200,
  }) {
    return BaseResponse<T>(
      code: code,
      message: message,
      data: data,
    );
  }

  /// 创建失败响应
  factory BaseResponse.error({
    required String message,
    int code = -1,
    T? data,
  }) {
    return BaseResponse<T>(
      code: code,
      message: message,
      data: data,
    );
  }

  @override
  String toString() {
    return 'BaseResponse{code: $code, msg: $message, data: $data}';
  }
}
