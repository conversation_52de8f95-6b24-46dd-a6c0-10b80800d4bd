import 'package:dio/dio.dart';

/// 网络异常类
class NetException implements Exception {
  /// 错误码
  final int code;
  
  /// 错误消息
  final String message;
  
  /// 原始异常
  final dynamic originalException;

  const NetException({
    required this.code,
    required this.message,
    this.originalException,
  });

  getMessage() => message;

  /// 从DioException创建NetException
  factory NetException.fromDioException(DioException dioException) {
    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
        return const NetException(
          code: -1001,
          message: '连接超时，请检查网络设置',
        );
      case DioExceptionType.sendTimeout:
        return const NetException(
          code: -1002,
          message: '请求超时，请稍后重试',
        );
      case DioExceptionType.receiveTimeout:
        return const NetException(
          code: -1003,
          message: '响应超时，请稍后重试',
        );
      case DioExceptionType.badResponse:
        return NetException._handleBadResponse(dioException);
      case DioExceptionType.cancel:
        return const NetException(
          code: -1005,
          message: '请求已取消',
        );
      case DioExceptionType.connectionError:
        return const NetException(
          code: -1006,
          message: '网络连接失败，请检查网络设置',
        );
      case DioExceptionType.badCertificate:
        return const NetException(
          code: -1007,
          message: '证书验证失败',
        );
      case DioExceptionType.unknown:
      default:
        return NetException(
          code: -1000,
          message: '网络请求失败：${dioException.message ?? "未知错误"}',
          originalException: dioException,
        );
    }
  }

  /// 处理HTTP错误响应
  static NetException _handleBadResponse(DioException dioException) {
    final statusCode = dioException.response?.statusCode ?? -1;
    final statusMessage = dioException.response?.statusMessage ?? '';
    
    switch (statusCode) {
      case 400:
        return const NetException(
          code: 400,
          message: '请求参数错误',
        );
      case 401:
        return const NetException(
          code: 401,
          message: '未授权，请重新登录',
        );
      case 403:
        return const NetException(
          code: 403,
          message: '拒绝访问',
        );
      case 404:
        return const NetException(
          code: 404,
          message: '请求的资源不存在',
        );
      case 405:
        return const NetException(
          code: 405,
          message: '请求方法不被允许',
        );
      case 500:
        return const NetException(
          code: 500,
          message: '服务器内部错误',
        );
      case 502:
        return const NetException(
          code: 502,
          message: '网关错误',
        );
      case 503:
        return const NetException(
          code: 503,
          message: '服务不可用',
        );
      case 504:
        return const NetException(
          code: 504,
          message: '网关超时',
        );
      default:
        return NetException(
          code: statusCode,
          message: 'HTTP错误：$statusCode $statusMessage',
          originalException: dioException,
        );
    }
  }

  /// 创建自定义异常
  factory NetException.custom({
    required int code,
    required String message,
    dynamic originalException,
  }) {
    return NetException(
      code: code,
      message: message,
      originalException: originalException,
    );
  }

  @override
  String toString() {
    return 'NetException{code: $code, message: $message}';
  }
}
