import 'package:dio/dio.dart';
import 'package:flutter_kexue/utils/store_util/shared_prefs.dart';

/// 认证拦截器
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加认证token
    final token = _getToken();
    if (token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    
    // 添加用户ID
    final userId = _getUserId();
    if (userId.isNotEmpty) {
      options.headers['User-Id'] = userId;
    }
    
    // 添加设备信息
    options.headers['Device-Type'] = 'mobile';
    options.headers['App-Version'] = '1.0.0';
    
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 处理响应，可以在这里更新token等
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 处理401未授权错误
    if (err.response?.statusCode == 401) {
      // 清除本地token
      _clearToken();
      // 可以在这里跳转到登录页面
      // Get.offAllNamed(Routes.login);
    }
    
    super.onError(err, handler);
  }

  /// 获取token
  String _getToken() {
    try {
      final prefs = SharedPreferencesHelper();
      return prefs.getString('access_token') ?? '';
    } catch (e) {
      return '';
    }
  }

  /// 获取用户ID
  String _getUserId() {
    try {
      final prefs = SharedPreferencesHelper();
      return prefs.getString('user_id') ?? '';
    } catch (e) {
      return '';
    }
  }

  /// 清除token
  void _clearToken() {
    try {
      final prefs = SharedPreferencesHelper();
      prefs.setString('access_token', '');
      prefs.setString('user_id', '');
    } catch (e) {
      // 忽略错误
    }
  }
}
