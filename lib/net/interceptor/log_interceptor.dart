import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 日志拦截器
class KXLogInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      _printRequest(options);
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      _printResponse(response);
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      _printError(err);
    }
    super.onError(err, handler);
  }

  /// 打印请求信息
  void _printRequest(RequestOptions options) {
    debugPrint('┌─────────────────────────────────────────────────────────────');
    debugPrint('│ 🚀 REQUEST');
    debugPrint('├─────────────────────────────────────────────────────────────');
    debugPrint('│ Method: ${options.method}');
    debugPrint('│ URL: ${options.uri}');
    debugPrint('│ Headers: ${_formatJson(options.headers)}');
    
    if (options.queryParameters.isNotEmpty) {
      debugPrint('│ Query Parameters: ${_formatJson(options.queryParameters)}');
    }
    
    if (options.data != null) {
      debugPrint('│ Body: ${_formatData(options.data)}');
    }
    
    debugPrint('└─────────────────────────────────────────────────────────────');
  }

  /// 打印响应信息
  void _printResponse(Response response) {
    debugPrint('┌─────────────────────────────────────────────────────────────');
    debugPrint('│ ✅ RESPONSE');
    debugPrint('├─────────────────────────────────────────────────────────────');
    debugPrint('│ Status Code: ${response.statusCode}');
    debugPrint('│ URL: ${response.requestOptions.uri}');
    debugPrint('│ Headers: ${_formatJson(response.headers.map)}');
    debugPrint('│ Data: ${_formatData(response.data)}');
    debugPrint('└─────────────────────────────────────────────────────────────');
  }

  /// 打印错误信息
  void _printError(DioException err) {
    debugPrint('┌─────────────────────────────────────────────────────────────');
    debugPrint('│ ❌ ERROR');
    debugPrint('├─────────────────────────────────────────────────────────────');
    debugPrint('│ Type: ${err.type}');
    debugPrint('│ Message: ${err.message}');
    debugPrint('│ URL: ${err.requestOptions.uri}');
    
    if (err.response != null) {
      debugPrint('│ Status Code: ${err.response?.statusCode}');
      debugPrint('│ Response Data: ${_formatData(err.response?.data)}');
    }
    
    debugPrint('└─────────────────────────────────────────────────────────────');
  }

  /// 格式化JSON数据
  String _formatJson(Map<String, dynamic> json) {
    try {
      return const JsonEncoder.withIndent('  ').convert(json);
    } catch (e) {
      return json.toString();
    }
  }

  /// 格式化数据
  String _formatData(dynamic data) {
    if (data == null) return 'null';
    
    try {
      if (data is Map || data is List) {
        return const JsonEncoder.withIndent('  ').convert(data);
      } else {
        return data.toString();
      }
    } catch (e) {
      return data.toString();
    }
  }
}
