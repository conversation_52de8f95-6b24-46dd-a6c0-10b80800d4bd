import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_kexue/net/config/net_config.dart';
import 'package:flutter_kexue/net/exception/net_exception.dart';
import 'package:flutter_kexue/net/interceptor/log_interceptor.dart';
import 'package:flutter_kexue/net/model/base_response.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';

/// HTTP客户端
class HttpClient {
  static HttpClient? _instance;
  late Dio _dio;

  /// 单例模式
  static HttpClient get instance {
    _instance ??= HttpClient._internal();
    return _instance!;
  }

  /// 私有构造函数
  HttpClient._internal() {
    _initDio();
  }

  /// 初始化Dio
  void _initDio() {
    _dio = Dio(BaseOptions(
      baseUrl: NetConfig.baseUrl,
      connectTimeout: Duration(milliseconds: NetConfig.connectTimeout),
      receiveTimeout: Duration(milliseconds: NetConfig.receiveTimeout),
      sendTimeout: Duration(milliseconds: NetConfig.sendTimeout),
      headers: NetConfig.defaultHeaders,
    ));

    // 添加拦截器
    if (NetConfig.enableLog && kDebugMode) {
      _dio.interceptors.add(KXLogInterceptor());
    }
    // _dio.interceptors.add(AuthInterceptor());
  }

  /// GET请求
  Future<BaseResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
    isShowLoading = false,
    isShowToast = true,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      if (isShowLoading) {
        ToastUtil.showLoading();
      }
      final response = await _dio.get(
        path,
        queryParameters: params,
        options: options,
        cancelToken: cancelToken,
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      var netException = NetException.fromDioException(e);
      if (isShowToast) {
        ToastUtil.showToast(netException.message);
      }
      return BaseResponse.error(
          message: netException.message, code: netException.code);
    } catch (e) {
      var netException = NetException.custom(
        code: -1,
        message: '请求失败：$e',
        originalException: e,
      );
      if (isShowToast) {
        ToastUtil.showToast(netException.message);
      }
      return BaseResponse.error(
          message: netException.message, code: netException.code);
    } finally {
      if (isShowLoading) {
        ToastUtil.hideLoading();
      }
    }
  }

  /// POST请求
  Future<BaseResponse<T>> post<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    isShowLoading = false,
    isShowToast = true,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      if (isShowLoading) {
        ToastUtil.showLoading();
      }
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      var netException = NetException.fromDioException(e);
      if (isShowToast) {
        ToastUtil.showToast(netException.message);
      }
      return BaseResponse.error(
          message: netException.message, code: netException.code);
    } catch (e) {
      var netException = NetException.custom(
        code: -1,
        message: '请求失败：$e',
        originalException: e,
      );
      if (isShowToast) {
        ToastUtil.showToast(netException.message);
      }
      return BaseResponse.error(
          message: netException.message, code: netException.code);
    } finally {
      if (isShowLoading) {
        ToastUtil.hideLoading();
      }
    }
  }

  /// PUT请求
  Future<BaseResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      throw NetException.fromDioException(e);
    } catch (e) {
      throw NetException.custom(
        code: -1,
        message: '请求失败：$e',
        originalException: e,
      );
    }
  }

  /// DELETE请求
  Future<BaseResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      throw NetException.fromDioException(e);
    } catch (e) {
      throw NetException.custom(
        code: -1,
        message: '请求失败：$e',
        originalException: e,
      );
    }
  }

  /// 上传文件
  Future<BaseResponse<T>> upload<T>(
    String path,
    FormData formData, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: formData,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      throw NetException.fromDioException(e);
    } catch (e) {
      throw NetException.custom(
        code: -1,
        message: '上传失败：$e',
        originalException: e,
      );
    }
  }

  /// 下载文件
  Future<Response> download(
    String urlPath,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.download(
        urlPath,
        savePath,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw NetException.fromDioException(e);
    } catch (e) {
      throw NetException.custom(
        code: -1,
        message: '下载失败：$e',
        originalException: e,
      );
    }
  }

  /// 处理响应数据
  BaseResponse<T> _handleResponse<T>(
      Response response, T Function(dynamic)? fromJson) {
    final data = response.data;
    try {
      BaseResponse<T> response =
          BaseResponse<T>.fromJson(data, fromJson ?? (json) => json as T);
      if (response.isSuccess) {
        return response;
      } else {
        return BaseResponse.error(message: response.message, code: response.code);
      }
    } on DioException catch (e) {
      // 如果解析失败，创建一个包装的响应
      return BaseResponse.error(
          message: e.message ?? "网络请求失败", code: e.response?.statusCode ?? -1);
    }
  }

  /// 获取Dio实例（用于特殊需求）
  Dio get dio => _dio;
}
