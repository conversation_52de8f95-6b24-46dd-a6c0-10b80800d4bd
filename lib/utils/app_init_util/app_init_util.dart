
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_kexue/utils/store_util/shared_prefs.dart';
import 'package:flutter_kexue/utils/store_util/sp_keys.dart';


class AppInitUtil {
  static delayInit() {
    //三方sdk或者涉及到隐私的需要在隐私政策同意后方可调用
    // UmengUtil.init();
    // UmengUtil.initUMVerify();
    // VolcanoUtil.init();
  }

  static initIgnorePrivacy() async {
    WidgetsFlutterBinding.ensureInitialized();
    final SharedPreferencesHelper prefsHelper = SharedPreferencesHelper();
    await prefsHelper.init();
  }

  static appInit() async {
    _logMode();
    await initIgnorePrivacy();
    bool agreePrivacy = SharedPreferencesHelper().getBool(StoreKeys.key_agreement_accepted, defaultValue: false) ?? false;
    if (agreePrivacy) {
      delayInit();
    }
  }

  static void _logMode() {
    if (kReleaseMode) {
      debugPrint('当前为 Release 模式');
    } else if (kProfileMode) {
      debugPrint('当前为 Profile 模式');
    } else {
      debugPrint('当前为 Debug 模式');
    }
  }
}