import 'package:flutter/material.dart';
import 'package:flutter_kexue/generated/assets.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ButtonUtil {
  /// 通用蓝色按钮静态方法
  static Widget buildCommonButton({
    Key? key,
    required String text,
    required VoidCallback onPressed,
    double? width,
    double? height,
    bool enabled = true, // 默认可点击
    double borderRadius = 4.0,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    TextStyle? textStyle,
    double? fontSize = 18.0,
  }) {
    return GestureDetector(
      onTap: enabled ? onPressed : null, // 根据 enabled 控制点击
      child: Container(
        width: width ?? double.infinity,
        // 自定义宽度或默认无限宽
        height: height ?? 44.h,
        // 自定义高度或默认60
        padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: enabled
              ? (backgroundColor ?? Colors.blue) // 可点击时的颜色
              : (backgroundColor ?? Colors.blue).withValues(alpha: 0.5),
          // 禁用时半透明
          borderRadius: BorderRadius.circular(borderRadius.r),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: textStyle ??
              TextStyle(
                color: Colors.white,
                fontSize: fontSize?.sp,
                fontWeight: FontWeight.w600,
              ),
        ),
      ),
    );
  }

  /// 通用蓝色框+浅蓝色背景按钮
  static Widget buildCommonButtonWithBorder({
    Key? key,
    required String text,
    required VoidCallback onPressed,
    bool selected = false, // 默认可点击
    double? width,
    double? height,
  }) {
    return GestureDetector(
        onTap: onPressed, // 根据 enabled 控制点击
        child: Container(
            width: width,
            height: height,
            padding: (width != null || height != null)
                ? null
                : EdgeInsets.symmetric(horizontal: 10.4.w, vertical: 5.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              color: selected
                  ? ColorsUtil.primaryColor
                  : ColorsUtil.primaryColor15,
              border: Border.all(
                color: ColorsUtil.primaryColor,
                width: 1.w,
              ),
            ),
            alignment: Alignment.center,
            child: Text(
              text,
              style: TextStyle(
                color: selected ? Colors.white : ColorsUtil.primaryColor,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            )));
  }

  /// 灰色筛选按钮
  static Widget buildGreyButtonSelect({
    Key? key,
    required String text,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
        onTap: onPressed,
        child: Container(
            constraints: BoxConstraints(minWidth: 80.w),
            padding:
                EdgeInsets.only(left: 7.w, right: 4.w, top: 6.h, bottom: 6.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              color: ColorsUtil.inputBgColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 12.sp,
                    overflow: TextOverflow.ellipsis,
                    color: Color(0xFF393939),
                  ),
                ),
                SizedBox(width: 4.w),
                Image(
                  image: AssetImage(Assets.commonIconTagSelect),
                  width: 16.w,
                  height: 16.h,
                )
              ],
            )));
  }

  /// 灰色筛选按钮
  static Widget buildGreyButtonDelete({
    Key? key,
    required String text,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
        onTap: onPressed,
        child: Container(
            padding: EdgeInsets.only(
                left: 8.1.w, right: 7.w, top: 6.5.h, bottom: 6.5.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              color: ColorsUtil.inputBgColor,
            ),
            child: Row(
              children: [
                Text(text,
                    style: TextStyle(
                      fontSize: 13.sp,
                    )),
                SizedBox(width: 2.6.w),
                Image(
                  image: AssetImage(Assets.commonIconTagSelect),
                  width: 12.2.w,
                  height: 13.3.h,
                )
              ],
            )));
  }

  /// 纯文本按钮
  static Widget buildTextButton({
    Key? key,
    required String text,
    required VoidCallback onPressed,
    bool enabled = true,
    Color? textColor,
    double? fontSize = 18.0,
    FontWeight? fontWeight = FontWeight.w400,
    EdgeInsetsGeometry? padding,
    TextDecoration? decoration,
  }) {
    return GestureDetector(
      onTap: enabled ? onPressed : null,
      child: Container(
        padding:
            padding ?? EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        child: Text(
          text,
          style: TextStyle(
            color: enabled
                ? (textColor ?? ColorsUtil.primaryColor)
                : (textColor ?? ColorsUtil.primaryColor).withValues(alpha: 0.5),
            fontSize: fontSize?.sp,
            fontWeight: fontWeight,
            decoration: decoration,
          ),
        ),
      ),
    );
  }
}
