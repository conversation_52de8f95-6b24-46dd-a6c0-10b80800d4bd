import 'package:flutter/material.dart';
import 'package:flutter_kexue/generated/assets.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class AppBarUtil {
  // 通用样式 AppBar
  static AppBar buildCommonAppBar({
    required String title,
    VoidCallback? onBackTap,
    Color backgroundColor = Colors.white,
    Color iconColor = Colors.black,
  }) {
    return AppBar(
      backgroundColor: backgroundColor,
      leading: GestureDetector(
          onTap: onBackTap ?? () => Get.back(),
          child: Container(
            padding: const EdgeInsets.only(left: 8.0),
            child: Image(
              image: AssetImage(Assets.commonIconArrowBack),
            ),
          )),
      leadingWidth: 38,
      titleSpacing: 08,
      title: Text(title,
          style: const TextStyle(
              color: Colors.black, fontSize: 22, fontWeight: FontWeight.w600)),
      centerTitle: false,
    );
  }

  // 含资源位的 AppBar（右侧带一个可定制的 Widget）
  static AppBar buildWithResourceWidget({
    required String title,
    VoidCallback? onBackTap,
    Color backgroundColor = Colors.white,
    String? resourceText,
    String? resourceIcon,
    VoidCallback? onResourceTap,
  }) {
    return AppBar(
      backgroundColor: backgroundColor,
      leading: GestureDetector(
          onTap: onBackTap ?? () => Get.back(),
          child: Container(
            padding: const EdgeInsets.only(left: 8.0),
            child: Image(
              image: AssetImage(Assets.commonIconArrowBack),
            ),
          )),
      leadingWidth: 38,
      titleSpacing: 08,
      title: Text(title,
          style: const TextStyle(
              color: Colors.black, fontSize: 22, fontWeight: FontWeight.w600)),
      centerTitle: false,
      actions: [
        GestureDetector(
          onTap: onResourceTap,
          child: Container(
            margin: const EdgeInsets.only(right: 16.0),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor15,
              borderRadius: BorderRadius.circular(14.0),
            ),
            child: Row(
              children: [
                if (resourceIcon != null)
                  Image.asset(resourceIcon, width: 18, height: 18),
                if (resourceText != null)
                  Text(
                    resourceText,
                    style: TextStyle(
                        color: ColorsUtil.primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
