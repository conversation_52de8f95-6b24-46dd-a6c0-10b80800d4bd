import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import 'colors_util.dart';

class ToastUtil {
  /// 显示普通toast
  static void showToast(String message) {
    _showCustomToast(message, null);
  }

  /// 显示网络异常
  static void showNetErrorToast(
      {String title = '网络异常，请稍候再试~', String tips = '错误码：404'}) {
    SmartDialog.showToast(
      '',
      alignment: Alignment.center,
      builder: (context) => Align(
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            color: ColorsUtil.black65,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(title, style: const TextStyle(color: Colors.white, fontSize: 15)),
              const SizedBox(height: 6),
              Text(tips,
                  style: TextStyle(color: ColorsUtil.white45, fontSize: 12))
            ],
          ),
        ),
      ),
    );
  }

  /// 显示loading
  static void showLoading({String message = '数据加载中...'}) {
    SmartDialog.showLoading(
        maskColor: Colors.transparent,
        alignment: Alignment.center,
        builder: (context) => Align(
              child: Container(
                width: 120.0,
                height: 120.0,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: const Color(0xAA000000),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const LoadingAnimation(),
                    const SizedBox(height: 16),
                    Text(message,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 14)),
                  ],
                ),
              ),
            ));
  }

  /// 关闭loading
  static void hideLoading() {
    SmartDialog.dismiss();
  }

  /// 显示自定义toast
  static void _showCustomToast(String message, Widget? icon, {bool isNetError = false}) {
    SmartDialog.showToast(
      '',
      displayType: SmartToastType.onlyRefresh,
      alignment: Alignment.center,
      builder: (context) => Align(
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          decoration: BoxDecoration(
            color: const Color(0xAA000000),
            borderRadius: BorderRadius.circular(10),
          ),
          constraints: BoxConstraints(
            maxWidth: isNetError ? Get.width : 240,
          ),
          child: icon == null
              ? Text(
                  message,
                  style: const TextStyle(color: Colors.white, fontSize: 15),
                  textAlign: TextAlign.center,
                )
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    icon,
                    const SizedBox(height: 6),
                    Text(message,
                        style: const TextStyle(color: Colors.white),
                        textAlign: TextAlign.center),
                  ],
                ),
        ),
      ),
    );
  }
}

/// 加载动画
class LoadingAnimation extends StatefulWidget {
  const LoadingAnimation({super.key});

  @override
  State<LoadingAnimation> createState() => _LoadingAnimationState();
}

class _LoadingAnimationState extends State<LoadingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _animation,
      child: const Image(
        width: 36,
        height: 36,
        image: AssetImage('assets/images/common/net_loading.png'),
      ),
    );
  }
}
