import 'dart:math';
import 'dart:ui';

class ColorsUtil {
  /// 获取随机颜色
  static Color get randomColor {
    return Color.fromARGB(255, Random().nextInt(255), Random().nextInt(255),
        Random().nextInt(255));
  }

  /// 主色调
  static Color get primaryColor => Color(0xFF4ACE7F);
  static Color get primaryColor20 => rgba(74,206,127,0.2);

  /// 背景色
  static Color get bgColor => Color(0xFFF6F6F6);

  /// 文字颜色
  static Color get textBlack => Color(0xFF031C3E);

  static Color get gary58 => Color(0xFF586E8E);
  static Color get gary85 => Color(0xFF858585);
  static Color get garyB1 => Color(0xFFB1BCCB);
  static Color get garyF5 => Color(0xFFF5F5F5);
  static Color get garyCE => Color(0xFFCECECE);
  static Color get garyF2 => Color(0xFFF2F2F2);
  static Color get garyE5 => Color(0xFFE5E5E5);
  static Color get garyEF => Color(0xFFEFEFEF);
  static Color get gary99 => Color(0xFF999999);
  static Color get gary9A => Color(0xFF9A9A9A);
  static Color get garyB8 => Color(0xFFB8B8B8);
  static Color get yellow => Color(0xFFFFB000);
  static Color get red => Color(0xFFF92800);
  static Color get black10 => Color(0xFF101010);
  static Color get black14 => Color(0xFF141414);

  /// 黑色85%
  static Color get black85 => rgba(0, 0, 0, 0.85);

  /// 黑色65%
  static Color get black65 => rgba(0, 0, 0, 0.65);

  /// 黑色45%
  static Color get black45 => rgba(0, 0, 0, 0.45);

  /// 黑色50%
  static Color get black50 => rgba(0, 0, 0, 0.5);

  /// 黑色38%
  static Color get black38 => rgba(0, 0, 0, 0.38);

  /// 黑色25%
  static Color get black25 => rgba(0, 0, 0, 0.25);

  /// 黑色5%
  static Color get black5 => rgba(0, 0, 0, 0.05);

  /// 白色45%
  static Color get white45 => rgba(255, 255, 255, 0.45);

  /// 主色调15%透明度
  static Color get primaryColor15 => rgba(74, 206, 127, 0.15);

  /// 输入框背景色
  static Color get inputBgColor => Color(0xFFF5F5F5);

  /// 登录按钮未激活颜色
  static Color get loginButtonInactive => Color(0xFFAAE6C2);

  /// 灰色100%
  static Color get divideLineColor => rgba(238, 238, 238, 1);

  /// 根据rgb和透明度获取颜色
  static Color rgba(int r, int g, int b, double a) {
    return Color.fromRGBO(r, g, b, a);
  }

  /// 根据rgb获取颜色
  static Color rgb(int r, int g, int b) {
    return Color.fromRGBO(r, g, b, 1);
  }
}
