import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_kexue/data/login/ds/account_lds.dart';
import 'package:flutter_kexue/data/login/repo/model/account_biz_model.dart';

/// 用户信息管理单例
/// 提供同步获取用户信息的能力，内部维护缓存和自动刷新机制
class UserManager {
  static UserManager? _instance;
  static final Object _lock = Object();

  /// 获取单例实例
  static UserManager get instance {
    if (_instance == null) {
      synchronized(_lock, () {
        _instance ??= UserManager._internal();
      });
    }
    return _instance!;
  }

  /// 私有构造函数
  UserManager._internal() {
    _accountRds = AccountRds();
    _initializeUserInfo();
  }

  late final AccountRds _accountRds;
  AccountBizModel? _cachedUserInfo;
  bool _isInitialized = false;
  bool _isInitializing = false;
  final Completer<void> _initCompleter = Completer<void>();

  /// 用户信息变化流控制器
  final StreamController<AccountBizModel?> _userInfoController = 
      StreamController<AccountBizModel?>.broadcast();

  /// 用户信息变化流
  Stream<AccountBizModel?> get userInfoStream => _userInfoController.stream;

  /// 初始化用户信息
  Future<void> _initializeUserInfo() async {
    if (_isInitializing || _isInitialized) return;
    
    _isInitializing = true;
    try {
      _cachedUserInfo = await _accountRds.getUserInfo();
      _isInitialized = true;
      _initCompleter.complete();
      
      // 通知用户信息变化
      _userInfoController.add(_cachedUserInfo);
    } catch (e) {
      debugPrint('初始化用户信息失败: $e');
      _initCompleter.complete();
    } finally {
      _isInitializing = false;
    }
  }

  /// 等待初始化完成
  Future<void> _ensureInitialized() async {
    if (!_isInitialized && !_isInitializing) {
      await _initializeUserInfo();
    } else if (_isInitializing) {
      await _initCompleter.future;
    }
  }

  /// 同步获取用户信息（如果未初始化会返回null）
  AccountBizModel? get currentUser {
    return _cachedUserInfo;
  }

  /// 异步获取用户信息（确保获取到最新数据）
  Future<AccountBizModel?> getUserInfo({bool forceRefresh = false}) async {
    await _ensureInitialized();
    
    if (forceRefresh || _cachedUserInfo == null) {
      await refreshUserInfo();
    }
    
    return _cachedUserInfo;
  }

  /// 刷新用户信息
  Future<AccountBizModel?> refreshUserInfo() async {
    try {
      _cachedUserInfo = await _accountRds.getUserInfo();
      
      // 通知用户信息变化
      _userInfoController.add(_cachedUserInfo);
      
      return _cachedUserInfo;
    } catch (e) {
      debugPrint('刷新用户信息失败: $e');
      return _cachedUserInfo;
    }
  }

  /// 同步检查是否已登录
  bool get isLoggedIn {
    return _cachedUserInfo?.isLoggedIn ?? false;
  }

  /// 异步检查是否已登录（更准确）
  Future<bool> checkLoginStatus() async {
    await _ensureInitialized();
    
    try {
      final isLoggedIn = await _accountRds.isLoggedIn();
      
      // 如果登录状态发生变化，刷新用户信息
      if (isLoggedIn != this.isLoggedIn) {
        await refreshUserInfo();
      }
      
      return isLoggedIn;
    } catch (e) {
      debugPrint('检查登录状态失败: $e');
      return isLoggedIn;
    }
  }

  /// 同步获取用户ID
  String? get userId => _cachedUserInfo?.userId;

  /// 同步获取用户名
  String? get username => _cachedUserInfo?.username;

  /// 同步获取昵称
  String? get nickname => _cachedUserInfo?.nickname;

  /// 同步获取头像
  String? get avatar => _cachedUserInfo?.avatar;

  /// 同步获取手机号
  String? get phone => _cachedUserInfo?.phone;

  /// 同步获取邮箱
  String? get email => _cachedUserInfo?.email;

  /// 同步获取访问令牌
  String? get accessToken => _cachedUserInfo?.accessToken;

  /// 同步检查令牌是否过期
  bool get isTokenExpired => _cachedUserInfo?.isTokenExpired ?? true;

  /// 保存用户信息
  Future<bool> saveUserInfo(AccountBizModel userInfo) async {
    try {
      final success = await _accountRds.saveUserInfo(userInfo);
      
      if (success) {
        _cachedUserInfo = userInfo;
        _isInitialized = true;
        
        // 通知用户信息变化
        _userInfoController.add(_cachedUserInfo);
      }
      
      return success;
    } catch (e) {
      debugPrint('保存用户信息失败: $e');
      return false;
    }
  }

  /// 更新用户基本信息
  Future<bool> updateUserProfile({
    String? nickname,
    String? avatar,
    String? email,
    int? gender,
    String? birthday,
  }) async {
    try {
      final success = await _accountRds.updateUserProfile(
        nickname: nickname,
        avatar: avatar,
        email: email,
        gender: gender,
        birthday: birthday,
      );
      
      if (success) {
        await refreshUserInfo();
      }
      
      return success;
    } catch (e) {
      debugPrint('更新用户资料失败: $e');
      return false;
    }
  }

  /// 更新访问令牌
  Future<bool> updateAccessToken(String accessToken, {String? refreshToken, String? expireTime}) async {
    try {
      final success = await _accountRds.updateAccessToken(
        accessToken,
        refreshToken: refreshToken,
        expireTime: expireTime,
      );
      
      if (success) {
        await refreshUserInfo();
      }
      
      return success;
    } catch (e) {
      debugPrint('更新访问令牌失败: $e');
      return false;
    }
  }

  /// 清除用户信息（退出登录）
  Future<bool> clearUserInfo() async {
    try {
      final success = await _accountRds.clearUserInfo();
      
      if (success) {
        _cachedUserInfo = null;
        
        // 通知用户信息变化
        _userInfoController.add(null);
      }
      
      return success;
    } catch (e) {
      debugPrint('清除用户信息失败: $e');
      return false;
    }
  }

  /// 检查令牌是否即将过期
  Future<bool> isTokenExpiringSoon({Duration threshold = const Duration(minutes: 30)}) async {
    try {
      return await _accountRds.isTokenExpiringSoon(threshold: threshold);
    } catch (e) {
      debugPrint('检查令牌过期时间失败: $e');
      return true;
    }
  }

  /// 获取用户显示名称（优先昵称，其次用户名）
  String get displayName {
    if (_cachedUserInfo?.nickname?.isNotEmpty == true) {
      return _cachedUserInfo!.nickname!;
    }
    if (_cachedUserInfo?.username?.isNotEmpty == true) {
      return _cachedUserInfo!.username!;
    }
    return '未知用户';
  }

  /// 获取用户头像（如果没有则返回默认头像）
  String get userAvatar {
    if (_cachedUserInfo?.avatar?.isNotEmpty == true) {
      return _cachedUserInfo!.avatar!;
    }
    return 'assets/images/default_avatar.png'; // 默认头像路径
  }

  /// 销毁单例（主要用于测试）
  void dispose() {
    _userInfoController.close();
    _instance = null;
  }

  @override
  String toString() {
    return 'UserManager{isLoggedIn: $isLoggedIn, userId: $userId, username: $username}';
  }
}

/// 简化的同步锁实现
void synchronized(Object lock, void Function() callback) {
  callback();
}

/// 扩展方法，方便使用
extension UserManagerExtension on UserManager {
  /// 快速检查是否为VIP用户（示例）
  bool get isVip {
    // 这里可以根据实际业务逻辑判断
    return _cachedUserInfo?.status == 1;
  }

  /// 快速获取用户性别文本
  String get genderText {
    switch (_cachedUserInfo?.gender) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未知';
    }
  }

  /// 快速检查用户是否完善了基本信息
  bool get isProfileComplete {
    final user = _cachedUserInfo;
    return user != null &&
        user.nickname?.isNotEmpty == true &&
        user.avatar?.isNotEmpty == true;
  }
}
