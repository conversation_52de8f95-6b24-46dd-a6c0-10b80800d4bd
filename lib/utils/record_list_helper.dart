import 'package:flutter_kexue/page/common_uistate/daily_record_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/record_uistate.dart';
/// 记录列表操作辅助类
/// ```dart
/// ViewModel中的使用示例
/// class MyViewModel extends GetxController {
///   final RxList<DailyRecordUIState> _recordList = <DailyRecordUIState>[].obs;
///
///   List<DailyRecordUIState> get recordList => _recordList.value;
///
///   /// 更新列表上的图片
///   void updateImageInRecord(int groupIndex, int itemIndex, String imagePath) {
///     try {
///       final updatedList = RecordListHelper.updateImage(
///         _recordList.value,
///         groupIndex,
///         itemIndex,
///         imagePath,
///       );
///       _recordList.value = updatedList;
///       _recordList.refresh();
///     } catch (e) {
///       print('更新图片失败: $e');
///     }
///   }
///
///   /// 上传图片到服务器
///   Future<void> uploadImageToServer(String imagePath) async {
///     try {
///       // 实际的上传逻辑
///       // final serverUrl = await apiService.uploadImage(imagePath);
///       print('图片上传成功: $imagePath');
///     } catch (e) {
///       print('图片上传失败: $e');
///     }
///   }
/// }
/// ```
class RecordListHelper {
  /// 更新指定位置的图片
  /// [recordList] 原始记录列表
  /// [groupIndex] 分组索引
  /// [itemIndex] 项目索引
  /// [imagePath] 新的图片路径
  /// 返回更新后的记录列表
  static List<DailyRecordUIState> updateImage(
    List<DailyRecordUIState> recordList,
    int groupIndex,
    int itemIndex,
    String imagePath,
  ) {
    if (groupIndex < 0 || 
        groupIndex >= recordList.length ||
        itemIndex < 0 ||
        itemIndex >= recordList[groupIndex].list.length) {
      throw ArgumentError('索引超出范围: groupIndex=$groupIndex, itemIndex=$itemIndex');
    }

    // 创建新的记录列表
    final newRecordList = List<DailyRecordUIState>.from(recordList);
    
    // 获取要更新的分组
    final targetGroup = newRecordList[groupIndex];
    
    // 创建新的记录项列表
    final newRecordItems = List<RecordUIState>.from(targetGroup.list);
    
    // 更新指定位置的记录项
    final oldRecord = newRecordItems[itemIndex];
    final updatedRecord = oldRecord.copyWith(image: imagePath);
    newRecordItems[itemIndex] = updatedRecord;
    
    // 创建新的分组
    final updatedGroup = DailyRecordUIState(
      title: targetGroup.title,
      progress: targetGroup.progress,
      isExpand: targetGroup.isExpand,
      list: newRecordItems,
    );
    
    // 更新分组
    newRecordList[groupIndex] = updatedGroup;
    
    return newRecordList;
  }

  static List<DailyRecordUIState> updateContent(
      List<DailyRecordUIState> recordList,
      int groupIndex,
      int itemIndex,
      String content,
      ) {
    if (groupIndex < 0 ||
        groupIndex >= recordList.length ||
        itemIndex < 0 ||
        itemIndex >= recordList[groupIndex].list.length) {
      throw ArgumentError('索引超出范围: groupIndex=$groupIndex, itemIndex=$itemIndex');
    }

    // 创建新的记录列表
    final newRecordList = List<DailyRecordUIState>.from(recordList);

    // 获取要更新的分组
    final targetGroup = newRecordList[groupIndex];

    // 创建新的记录项列表
    final newRecordItems = List<RecordUIState>.from(targetGroup.list);

    // 更新指定位置的记录项
    final oldRecord = newRecordItems[itemIndex];
    final updatedRecord = oldRecord.copyWith(content: content);
    newRecordItems[itemIndex] = updatedRecord;

    // 创建新的分组
    final updatedGroup = DailyRecordUIState(
      title: targetGroup.title,
      progress: targetGroup.progress,
      isExpand: targetGroup.isExpand,
      list: newRecordItems,
    );

    // 更新分组
    newRecordList[groupIndex] = updatedGroup;

    return newRecordList;
  }

  /// 清除指定位置的图片
  /// [recordList] 原始记录列表
  /// [groupIndex] 分组索引
  /// [itemIndex] 项目索引
  /// 返回更新后的记录列表
  static List<DailyRecordUIState> clearImage(
    List<DailyRecordUIState> recordList,
    int groupIndex,
    int itemIndex,
  ) {
    return updateImage(recordList, groupIndex, itemIndex, '');
  }

  /// 批量清除所有图片
  /// [recordList] 原始记录列表
  /// 返回更新后的记录列表
  static List<DailyRecordUIState> clearAllImages(
    List<DailyRecordUIState> recordList,
  ) {
    return recordList.map((group) {
      final updatedList = group.list.map((record) {
        if (record.image != null && record.image!.isNotEmpty) {
          return record.copyWith(image: null);
        }
        return record;
      }).toList();
      
      return DailyRecordUIState(
        title: group.title,
        progress: group.progress,
        isExpand: group.isExpand,
        list: updatedList,
      );
    }).toList();
  }

  /// 获取有图片的记录数量
  /// [recordList] 记录列表
  /// 返回有图片的记录数量
  static int getImageRecordCount(List<DailyRecordUIState> recordList) {
    int count = 0;
    for (final group in recordList) {
      for (final record in group.list) {
        if (record.image != null && record.image!.isNotEmpty) {
          count++;
        }
      }
    }
    return count;
  }

  /// 获取总记录数量
  /// [recordList] 记录列表
  /// 返回总记录数量
  static int getTotalRecordCount(List<DailyRecordUIState> recordList) {
    int count = 0;
    for (final group in recordList) {
      count += group.list.length;
    }
    return count;
  }

  /// 查找指定记录的位置
  /// [recordList] 记录列表
  /// [predicate] 查找条件
  /// 返回记录位置，如果未找到返回null
  static RecordPosition? findRecord(
    List<DailyRecordUIState> recordList,
    bool Function(RecordUIState record) predicate,
  ) {
    for (int groupIndex = 0; groupIndex < recordList.length; groupIndex++) {
      final group = recordList[groupIndex];
      for (int itemIndex = 0; itemIndex < group.list.length; itemIndex++) {
        final record = group.list[itemIndex];
        if (predicate(record)) {
          return RecordPosition(groupIndex: groupIndex, itemIndex: itemIndex);
        }
      }
    }
    return null;
  }

  /// 验证索引是否有效
  /// [recordList] 记录列表
  /// [groupIndex] 分组索引
  /// [itemIndex] 项目索引
  /// 返回索引是否有效
  static bool isValidIndex(
    List<DailyRecordUIState> recordList,
    int groupIndex,
    int itemIndex,
  ) {
    return groupIndex >= 0 &&
           groupIndex < recordList.length &&
           itemIndex >= 0 &&
           itemIndex < recordList[groupIndex].list.length;
  }
}

/// 记录位置信息
class RecordPosition {
  final int groupIndex;
  final int itemIndex;

  const RecordPosition({
    required this.groupIndex,
    required this.itemIndex,
  });

  @override
  String toString() {
    return 'RecordPosition(groupIndex: $groupIndex, itemIndex: $itemIndex)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecordPosition &&
           other.groupIndex == groupIndex &&
           other.itemIndex == itemIndex;
  }

  @override
  int get hashCode => groupIndex.hashCode ^ itemIndex.hashCode;
}
