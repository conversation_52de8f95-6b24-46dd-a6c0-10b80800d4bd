/// @date 2025/06/22
/// @description 验证工具类
class ValidationUtil {
  /// 验证手机号格式
  /// 支持中国大陆手机号格式：1开头的11位数字
  static bool isValidPhoneNumber(String phone) {
    if (phone.isEmpty) return false;
    
    // 移除所有空格和特殊字符
    String cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // 中国大陆手机号：1开头的11位数字
    RegExp phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    
    return phoneRegex.hasMatch(cleanPhone);
  }
  
  /// 格式化手机号显示（添加空格分隔）
  static String formatPhoneNumber(String phone) {
    if (phone.isEmpty) return phone;
    
    // 移除所有非数字字符
    String cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // 限制最大长度为11位
    if (cleanPhone.length > 11) {
      cleanPhone = cleanPhone.substring(0, 11);
    }
    
    // 格式化为 xxx xxxx xxxx 的形式
    if (cleanPhone.length <= 3) {
      return cleanPhone;
    } else if (cleanPhone.length <= 7) {
      return '${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3)}';
    } else {
      return '${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 7)} ${cleanPhone.substring(7)}';
    }
  }
  
  /// 获取纯数字手机号（移除格式化字符）
  static String getCleanPhoneNumber(String phone) {
    return phone.replaceAll(RegExp(r'[^\d]'), '');
  }
}
