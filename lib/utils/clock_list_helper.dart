import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/page/common_uistate/daily_clock_uistate.dart';

/// 打卡列表操作辅助类
class ClockListHelper {
  ///更新打卡次数
  static List<DailyClockUIState> updateClockDailyCount(
    List<DailyClockUIState> recordList,
    int groupIndex,
    int itemIndex,
  {
    int? dailyCount,
    int? nativeCount,
  }
  ) {
    if (groupIndex < 0 ||
        groupIndex >= recordList.length ||
        itemIndex < 0 ||
        itemIndex >= recordList[groupIndex].list.length) {
      throw ArgumentError(
          '索引超出范围: groupIndex=$groupIndex, itemIndex=$itemIndex');
    }

    // 创建新的记录列表
    final newRecordList = List<DailyClockUIState>.from(recordList);

    // 获取要更新的分组
    final targetGroup = newRecordList[groupIndex];

    // 创建新的记录项列表
    final newRecordItems = List<ClockUIState>.from(targetGroup.list);

    // 更新指定位置的记录项
    final oldRecord = newRecordItems[itemIndex];
    final updatedRecord = oldRecord.copyWith(dailyCount: dailyCount,nativeCount:nativeCount);
    newRecordItems[itemIndex] = updatedRecord;

    // 创建新的分组
    final updatedGroup = DailyClockUIState(
      systemUIState: targetGroup.systemUIState,
      progress: targetGroup.progress,
      isExpand: targetGroup.isExpand,
      list: newRecordItems,
    );

    // 更新分组
    newRecordList[groupIndex] = updatedGroup;

    return newRecordList;
  }
}

/// 记录位置信息
class RecordPosition {
  final int groupIndex;
  final int itemIndex;

  const RecordPosition({
    required this.groupIndex,
    required this.itemIndex,
  });

  @override
  String toString() {
    return 'RecordPosition(groupIndex: $groupIndex, itemIndex: $itemIndex)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecordPosition &&
        other.groupIndex == groupIndex &&
        other.itemIndex == itemIndex;
  }

  @override
  int get hashCode => groupIndex.hashCode ^ itemIndex.hashCode;
}
