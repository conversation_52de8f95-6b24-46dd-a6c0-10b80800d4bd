import 'package:lunar/lunar.dart';

/// 农历工具类
class LunarUtil {
  /// 获取农历日期字符串
  /// [date] 公历日期
  /// [isToday] 是否为今天
  /// 返回农历日期字符串，如果是今天则返回"今天"
  static String getLunarDateString(DateTime date, {bool isToday = false}) {
    if (isToday) {
      return "今天";
    }
    
    try {
      final lunar = Lunar.fromDate(date);
      final day = lunar.getDay();
      final dayInChinese = lunar.getDayInChinese();
      
      // 如果是初一，显示月份
      if (day == 1) {
        return "${lunar.getMonthInChinese()}月";
      }
      
      return dayInChinese;
    } catch (e) {
      // 如果农历计算失败，返回空字符串
      return "";
    }
  }
  
  /// 获取节气信息
  /// [date] 公历日期
  /// 返回节气名称，如果不是节气则返回null
  static String? getSolarTerm(DateTime date) {
    try {
      final lunar = Lunar.fromDate(date);
      final jieQi = lunar.getJieQi();
      return jieQi.isEmpty ? null : jieQi;
    } catch (e) {
      return null;
    }
  }
  
  /// 获取农历节日
  /// [date] 公历日期
  /// 返回农历节日名称，如果不是节日则返回null
  static String? getLunarFestival(DateTime date) {
    try {
      final lunar = Lunar.fromDate(date);
      final festivals = lunar.getFestivals();
      return festivals.isNotEmpty ? festivals.first : null;
    } catch (e) {
      return null;
    }
  }
  
  /// 获取公历节日
  /// [date] 公历日期
  /// 返回公历节日名称，如果不是节日则返回null
  static String? getSolarFestival(DateTime date) {
    try {
      final solar = Solar.fromDate(date);
      final festivals = solar.getFestivals();
      return festivals.isNotEmpty ? festivals.first : null;
    } catch (e) {
      return null;
    }
  }
  
  /// 获取完整的农历信息显示文本
  /// 优先级：节日 > 节气 > 农历日期
  static String getDisplayText(DateTime date, {bool isToday = false}) {
    if (isToday) {
      return "今天";
    }
    
    // // 优先显示公历节日
    // final solarFestival = getSolarFestival(date);
    // if (solarFestival != null && solarFestival.isNotEmpty) {
    //   return solarFestival;
    // }
    //
    // // 其次显示农历节日
    // final lunarFestival = getLunarFestival(date);
    // if (lunarFestival != null && lunarFestival.isNotEmpty) {
    //   return lunarFestival;
    // }
    //
    // // 再次显示节气
    // final solarTerm = getSolarTerm(date);
    // if (solarTerm != null && solarTerm.isNotEmpty) {
    //   return solarTerm;
    // }
    
    // 最后显示农历日期
    return getLunarDateString(date, isToday: isToday);
  }
}
