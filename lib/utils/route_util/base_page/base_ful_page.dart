import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/route_util/base_page/page_lifecycle.dart';
import 'package:flutter_kexue/utils/route_util/observer/route_observer.dart';
import 'package:flutter_kexue/utils/system_util/jprint.dart';

//页面级别的Widget(可以路由）需要继承该基类，其他子View不作要求
abstract class BaseFulPage extends StatefulWidget {
}

//继承自BaseFulPageState，无需重写initState和dispose 方法，可用PageLifecycle替代
abstract class BaseFulPageState<T extends BaseFulPage> extends State<T> with WidgetsBindingObserver,RouteAware, PageLifecycle {

  String TAG = '';

  @override
  void initState() {
    super.initState();
    TAG = "yp_lifecycle ${runtimeType.toString()}";
    jprint("$TAG initState");
    WidgetsBinding.instance.addObserver(this);
    onPageCreate();
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    pageRouteObserver.unsubscribe(this);
    jprint("$TAG dispose");
    onPageDestroy();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    pageRouteObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        jprint('$TAG resumed');
        onPageShow();
        break;
      case AppLifecycleState.inactive:
        jprint('$TAG inactive');
        break;
      case AppLifecycleState.paused:
        jprint('$TAG paused');
        onPageHide();
        break;
      case AppLifecycleState.detached:
        jprint('$TAG detached');
        break;
      case AppLifecycleState.hidden:
        jprint('$TAG hidden');
        break;
    }
  }

  @override
  void didPop() {
    //pop 返回页面时回调，比如B返回A，B会执行这个回调，B会销毁
    super.didPop();
    jprint("$TAG didPop");
    onPageHide();
  }

  @override
  void didPush() {
    //push 进入页面时回调，比如A跳转到B，B回调执行这个方法
    super.didPush();
    jprint("$TAG didPush");
    onPageShow();
  }

  @override
  void didPopNext() {
    //pop 返回页面时回调，比如B返回A，A回调执行这个方法，A执行后，B的生命周期开始动
    super.didPopNext();
    jprint("$TAG didPopNext");
    onPageShow();
  }

  @override
  void didPushNext() {
    //push 进入下一个页面回调，比如A跳转到B，A回调执行这个方法，先走A的生命周期
    super.didPushNext();
    jprint("$TAG didPushNext");
    onPageHide();
  }


  @override
  void onPageCreate() {
    jprint("$TAG onPageCreate");
  }

  @override
  void onPageShow() {
    jprint("$TAG onPageShow");
  }

  @override
  void onPageHide() {
    jprint("$TAG onPageHide");
  }

  @override
  void onPageDestroy() {
    jprint("$TAG onPageDestroy");
  }
}