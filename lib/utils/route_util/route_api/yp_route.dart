import 'package:flutter_kexue/utils/route_util/route_api/route_core.dart';
import 'package:flutter_kexue/utils/route_util/route_api/yp_page_route.dart';

class YPRoute {
  static registerRoute(YPPageRoute ypPageRoute) {
    RouteCore.registerRoute(ypPageRoute);
  }

  static registerRoutes(List<YPPageRoute> ypPageRoutes) {
    RouteCore.registerRoutes(ypPageRoutes);
  }

  static Future<Object?>? openPage(String? routeName, {Object? params}) async {
    return RouteCore.openPage(routeName: routeName, params: params);
  }
}
