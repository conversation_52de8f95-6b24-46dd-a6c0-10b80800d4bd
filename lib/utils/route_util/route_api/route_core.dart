import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/route_util/route_api/yp_page_route.dart';


class RouteCore {
  //避免使用Context，全局持有Context
  static GlobalKey<NavigatorState> routeGlobalKey = GlobalKey<NavigatorState>();

  //路由导航器
  static final NavigatorState? _routeState = routeGlobalKey.currentState;

  //路由映射表
  static final Map<String, YPPageRoute> _routeMap = {};

  static void registerRoute(YPPageRoute ypPageRoute) {
    _routeMap[ypPageRoute.routeName] = ypPageRoute;
  }

  static void registerRoutes(List<YPPageRoute> ypPageRoutes) {
    final map = { for (var item in ypPageRoutes) (item).routeName : item };
    _routeMap.addAll(map);
  }

  static Future<dynamic> openPage(
      {String? routeName, Object? arguments, Object? params}) async {
    if (_routeMap.containsKey(routeName)) {
      YPPageRoute ypPageRoute = _routeMap[routeName]!;
      return _routeState?.push(MaterialPageRoute(
          builder: ypPageRoute.widgetBuilder,
          settings: RouteSettings(
              name: ypPageRoute.routeName, arguments: params)));
    } else {
      return null;
    }
  }
}
