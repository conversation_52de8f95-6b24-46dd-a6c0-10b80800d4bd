import 'dart:async';
import 'package:flutter/material.dart';

/*
      统一点击回调
          CarouselWidget(
            items: imageUrls,
            onTap: (index) {
              // 处理点击事件
            },
          )
      每张图片不同的点击事件
          CarouselWidget(
            items: imageUrls,
            onTapMap: {
              imageUrls[0]: () => _handleImage1Click(),
              imageUrls[1]: () => _handleImage2Click(),
              imageUrls[2]: () => _handleImage3Click(),
            },
          )
 */

/// 图片轮播组件
class CarouselWidget extends StatefulWidget {
  /// 图片URL列表（必填）
  final List<String> items;

  /// 轮播图高度，默认48
  final double height;

  /// 是否自动播放，默认true
  final bool autoPlay;

  /// 自动播放间隔(毫秒)，默认2000
  final int autoPlayInterval;

  /// 是否显示指示器，默认true
  final bool showIndicator;

  /// 指示器颜色，默认#91B2C7
  final Color indicatorColor;

  /// 激活指示器颜色，默认#007AFC
  final Color indicatorActiveColor;

  /// 指示器大小，默认6.0
  final double indicatorSize;

  /// 点击回调
  final Function(int index)? onTap;

  /// 每张图片的点击回调Map，key为图片URL
  final Map<String, VoidCallback>? onTapMap;

  /// 页面切换回调
  final Function(int index)? onPageChanged;

  /// 是否无限循环，默认true
  final bool infiniteLoop;

  /// 图片适配方式，默认BoxFit.cover
  final BoxFit fit;

  /// 圆角半径，默认4.0
  final double borderRadius;

  const CarouselWidget({
    super.key,
    required this.items,
    this.height = 48,
    this.autoPlay = true,
    this.autoPlayInterval = 2000,
    this.showIndicator = true,
    this.indicatorColor = const Color(0x7F91B2C7),
    this.indicatorActiveColor = const Color(0xFF007AFC),
    this.indicatorSize = 6.0,
    this.onTap,
    this.onTapMap,
    this.onPageChanged,
    this.infiniteLoop = true,
    this.fit = BoxFit.cover,
    this.borderRadius = 4.0,
  });

  @override
  State<CarouselWidget> createState() => _CarouselWidgetState();
}

class _CarouselWidgetState extends State<CarouselWidget>
    with WidgetsBindingObserver {
  late PageController _pageController;
  Timer? _timer;
  int _currentIndex = 0;
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeController();
    _startAutoPlay();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 根据应用生命周期管理自动播放
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isVisible = false;
        _pauseAutoPlay();
        break;
      case AppLifecycleState.resumed:
        _isVisible = true;
        _resumeAutoPlay();
        break;
      default:
        break;
    }
  }

  // 暂停自动播放
  void _pauseAutoPlay() {
    _timer?.cancel();
    _timer = null;
  }

  // 恢复自动播放
  void _resumeAutoPlay() {
    if (widget.autoPlay && widget.items.length > 1 && _isVisible && mounted) {
      _startAutoPlay();
    }
  }

  void _initializeController() {
    // 无限循环：从一个大数的中间位置开始
    final initialPage = widget.infiniteLoop ? 10000 : 0;
    _pageController = PageController(initialPage: initialPage);
    _currentIndex = 0;
  }

  void _startAutoPlay() {
    // 先停止现有Timer
    _pauseAutoPlay();

    // 检查自动播放条件
    if (!widget.autoPlay ||
        widget.items.length <= 1 ||
        !mounted ||
        !_isVisible) {
      return;
    }

    _timer = Timer.periodic(
      Duration(milliseconds: widget.autoPlayInterval),
      (timer) {
        if (!mounted || !_isVisible || !_pageController.hasClients) {
          timer.cancel();
          _timer = null;
          return;
        }
        _nextPage();
      },
    );
  }

  void _nextPage() {
    if (!mounted || !_pageController.hasClients) return;

    try {
      final currentPage = _pageController.page?.round() ?? 0;
      _pageController.animateToPage(
        currentPage + 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (e) {
      debugPrint('CarouselWidget: Error in _nextPage: $e');
    }
  }

  void _onPageChanged(int index) {
    if (!mounted) return;

    // 计算真实索引
    _currentIndex = index % widget.items.length;

    setState(() {});
    widget.onPageChanged?.call(_currentIndex);
  }

  Widget _buildItem(String imageUrl, int realIndex) {
    return GestureDetector(
      onTap: () {
        // 优先使用URL映射的回调，否则使用索引回调
        if (widget.onTapMap?.containsKey(imageUrl) == true) {
          widget.onTapMap![imageUrl]!();
        } else {
          widget.onTap?.call(realIndex);
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: Image.network(
          imageUrl,
          fit: widget.fit,
          // 图片加载状态显示
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;

            final progress = loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                : null;

            return Container(
              color: Colors.grey[100],
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.0,
                        value: progress,
                        backgroundColor: Colors.grey[300],
                      ),
                    ),
                    if (progress != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        '${(progress * 100).toInt()}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          },
          // 图片加载失败状态显示
          errorBuilder: (context, error, stackTrace) {
            debugPrint('CarouselWidget: Image load error for $imageUrl: $error');
            return Container(
              color: Colors.grey[200],
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image_outlined,
                      color: Colors.grey[400],
                      size: 28,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '加载失败',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildIndicator() {
    if (!widget.showIndicator || widget.items.length <= 1) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 2.0,
      left: 0,
      right: 0,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(
            widget.items.length,
            (index) => Container(
              width: widget.indicatorSize,
              height: widget.indicatorSize,
              margin: const EdgeInsets.symmetric(horizontal: 2.0),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentIndex
                    ? widget.indicatorActiveColor
                    : widget.indicatorColor,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 空数据状态处理
    if (widget.items.isEmpty) {
      return SizedBox(
        height: widget.height,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_not_supported_outlined,
                  color: Colors.grey[400],
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  '暂无数据',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SizedBox(
      height: widget.height,
      child: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: widget.infiniteLoop ? null : widget.items.length,
            itemBuilder: (context, index) {
              final realIndex = index % widget.items.length;
              return _buildItem(widget.items[realIndex], realIndex);
            },
          ),
          _buildIndicator(),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pauseAutoPlay();
    _pageController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
