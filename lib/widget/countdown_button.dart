import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 倒计时控制器
class CountdownController {
  _CountdownButtonState? _state;

  /// 绑定状态
  void _bind(_CountdownButtonState state) {
    _state = state;
  }

  /// 解绑状态
  void _unbind() {
    _state = null;
  }

  /// 开始倒计时
  void startCountdown() {
    _state?._startCountdown();
  }

  /// 停止倒计时
  void stopCountdown() {
    _state?._stopCountdown();
  }

  /// 重置倒计时
  void resetCountdown() {
    _state?._resetCountdown();
  }

  /// 是否正在倒计时
  bool get isCountingDown => _state?._isCountingDown ?? false;

  /// 当前倒计时数值
  int get currentCount => _state?._currentCount ?? 0;
}

/// 60秒倒计时按钮控件
class CountdownButton extends StatefulWidget {
  /// 默认显示的文字
  final String defaultText;

  /// 倒计时显示的文字格式，{count}会被替换为倒计时数字
  final String countdownText;

  /// 点击事件回调 - 支持异步操作
  final Future<bool> Function()? onPressed;

  /// 倒计时时长（秒），默认60秒
  final int countdownDuration;

  /// 文字样式
  final TextStyle? textStyle;

  /// 是否禁用按钮
  final bool disabled;

  /// 控制器，用于外部控制倒计时
  final CountdownController? controller;

  const CountdownButton({
    super.key,
    this.defaultText = '获取验证码',
    this.countdownText = '{count}s后重新获取',
    this.onPressed,
    this.countdownDuration = 60,
    this.textStyle,
    this.disabled = false,
    this.controller,
  });

  @override
  State<CountdownButton> createState() => _CountdownButtonState();
}

class _CountdownButtonState extends State<CountdownButton> {
  Timer? _timer;
  int _currentCount = 0;
  bool _isCountingDown = false;
  bool _isLoading = false; // 添加加载状态

  @override
  void initState() {
    super.initState();
    // 绑定控制器
    widget.controller?._bind(this);
  }

  @override
  void dispose() {
    _timer?.cancel();
    widget.controller?._unbind();
    super.dispose();
  }

  /// 开始倒计时
  void _startCountdown() {
    if (_isCountingDown || widget.disabled) return;

    setState(() {
      _isCountingDown = true;
      _currentCount = widget.countdownDuration;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentCount--;
      });

      if (_currentCount <= 0) {
        _stopCountdown();
      }
    });
  }

  /// 停止倒计时
  void _stopCountdown() {
    _timer?.cancel();
    setState(() {
      _isCountingDown = false;
      _currentCount = 0;
    });
  }

  /// 重置倒计时
  void _resetCountdown() {
    _timer?.cancel();
    setState(() {
      _isCountingDown = false;
      _currentCount = 0;
      _isLoading = false;
    });
  }

  /// 处理按钮点击
  void _handlePressed() async {
    if (_isCountingDown || widget.disabled || _isLoading) return;

    // 如果没有点击事件，直接开始倒计时
    if (widget.onPressed == null) {
      _startCountdown();
      return;
    }

    // 设置加载状态
    setState(() {
      _isLoading = true;
    });

    try {
      // 执行异步操作
      final success = await widget.onPressed!();

      // 根据结果决定是否开始倒计时
      if (success) {
        _startCountdown();
      }
    } catch (e) {
      // 操作失败，不开始倒计时
      debugPrint('CountdownButton: 操作失败 - $e');
    } finally {
      // 清除加载状态
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 获取当前显示的文字
  String get _displayText {
    if (_isLoading) {
      return '发送中...';
    }
    if (_isCountingDown) {
      return widget.countdownText
          .replaceAll('{count}', _currentCount.toString());
    }
    return widget.defaultText;
  }

  /// 判断按钮是否应该被禁用
  bool get _isDisabled {
    return widget.disabled || _isCountingDown || _isLoading;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: _isDisabled ? null : _handlePressed,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
          child: Text(
            _displayText,
            style: widget.textStyle ??
                TextStyle(
                  color: _isDisabled ? ColorsUtil.garyB1 : ColorsUtil.textBlack,
                  fontSize: 15.sp,
                ),
          ),
        ));
  }
}
