import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_kexue/widget/dialog/image_picker_dialog.dart';
import 'package:flutter_kexue/page/page/image_preview/entity/image_preview_props.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 横向滑动的图片上传组件
class TDUploadH extends StatefulWidget {
  /// 已上传的文件列表
  final List<TDUploadFile> files;

  /// 最大上传数量
  final int max;

  /// 是否支持多选
  final bool multiple;

  /// 图片高度
  final double height;

  /// 图片间距
  final double spacing;

  /// 取消事件
  final Function()? onCancel;

  /// 文件变化回调
  final Function(List<TDUploadFile> fileList, TDUploadType type)? onChange;

  /// 错误回调
  final Function(dynamic error)? onError;

  /// 验证回调
  final Function(TDUploadValidatorError error)? onValidate;

  /// 达到最大限制回调
  final Function()? onMaxLimitReached;

  /// 是否显示添加按钮
  final bool showAddButton;

  /// 是否启用图片预览
  final bool enablePreview;

  /// 是否可以删除图片
  final bool canDelete;

  /// 是否可以下载图片
  final bool canDownload;

  const TDUploadH({
    super.key,
    required this.files,
    this.max = 9,
    this.height = 77.0,
    this.spacing = 10.0,
    this.multiple = true,
    this.showAddButton = true,
    this.enablePreview = true,
    this.canDelete = true,
    this.canDownload = true,
    this.onCancel,
    this.onChange,
    this.onError,
    this.onValidate,
    this.onMaxLimitReached,
  });

  @override
  State<TDUploadH> createState() => _TDUploadHState();
}

class _TDUploadHState extends State<TDUploadH> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height.h,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // 已上传的图片列表
            ...widget.files.asMap().entries.map((entry) {
              final index = entry.key;
              final file = entry.value;
              return Container(
                margin: EdgeInsets.only(
                  right: index < widget.files.length - 1 ? widget.spacing.w : 0,
                ),
                child: _buildImageItem(file, index),
              );
            }),

            // 添加按钮（根据showAddButton属性和最大数量限制）
            if (widget.showAddButton && widget.files.length < widget.max) ...[
              if (widget.files.isNotEmpty) SizedBox(width: widget.spacing.w),
              _buildAddButton(),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建图片项
  Widget _buildImageItem(TDUploadFile file, int index) {
    return Container(
      width: widget.height.w,
      height: widget.height.h,
      child: Stack(
        clipBehavior: Clip.none, // 允许子组件超出边界
        children: [
          // 图片容器
          Container(
            width: widget.height.w,
            height: widget.height.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                color: Colors.grey.shade300,
                width: 0.5,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4.r),
              child: _buildImageContent(file),
            ),
          ),

          // 删除按钮
          if (file.canDelete)
            Positioned(
              top: -8.h,
              right: -8.w,
              child: GestureDetector(
                onTap: () => _removeFile(file),
                child: Container(
                  width: 20.w,
                  height: 20.h,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.close,
                    size: 12.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

          // 加载状态覆盖层
          if (file.status == TDUploadFileStatus.loading)
            Container(
              width: widget.height.w,
              height: widget.height.h,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 20.w,
                      height: 20.h,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        value: file.progress?.toDouble(),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      file.loadingText,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建图片内容
  Widget _buildImageContent(TDUploadFile file) {
    Widget imageWidget;

    if (file.file != null) {
      // 本地文件
      imageWidget = Image.file(
        file.file!,
        width: widget.height.w,
        height: widget.height.h,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildErrorWidget();
        },
      );
    } else if (file.remotePath != null && file.remotePath!.isNotEmpty) {
      // 网络图片
      imageWidget = Image.network(
        file.remotePath!,
        width: widget.height.w,
        height: widget.height.h,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: widget.height.w,
            height: widget.height.h,
            color: Colors.grey[200],
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildErrorWidget();
        },
      );
    } else if (file.assetPath != null && file.assetPath!.isNotEmpty) {
      // 资源图片
      imageWidget = Image.asset(
        file.assetPath!,
        width: widget.height.w,
        height: widget.height.h,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildErrorWidget();
        },
      );
    } else {
      imageWidget = _buildErrorWidget();
    }

    return GestureDetector(
      onTap: () => _handleImageTap(file, widget.files.indexOf(file)),
      child: imageWidget,
    );
  }

  /// 构建错误显示组件
  Widget _buildErrorWidget() {
    return Container(
      width: widget.height.w,
      height: widget.height.h,
      color: Colors.grey[200],
      child: Icon(
        Icons.broken_image,
        size: 24.sp,
        color: Colors.grey[400],
      ),
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton() {
    return GestureDetector(
      onTap: _addImage,
      child: Container(
        width: widget.height.w,
        height: widget.height.h,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(4.r),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              size: 24.sp,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// 添加图片
  void _addImage() {
    if (widget.files.length >= widget.max) {
      widget.onMaxLimitReached?.call();
      return;
    }

    // 使用图片选择对话框
    ImagePickerDialog.show(
      onImageSelected: (imagePath) {
        final newFile = TDUploadFile(
          key: DateTime.now().millisecondsSinceEpoch,
          file: File(imagePath),
          status: TDUploadFileStatus.success,
        );

        widget.onChange?.call([newFile], TDUploadType.add);
      },
    );
  }

  /// 移除文件
  void _removeFile(TDUploadFile file) {
    widget.onChange?.call([file], TDUploadType.remove);
  }

  /// 处理图片点击
  void _handleImageTap(TDUploadFile file, int index) {
    // 如果启用预览功能，则跳转到预览页面
    if (widget.enablePreview) {
      _showImagePreview(index);
    }
  }

  /// 显示图片预览
  void _showImagePreview(int initialIndex) {
    // 获取所有图片路径
    final imagePaths = _getImagePaths();

    if (imagePaths.isEmpty) return;

    try {
      // 创建预览参数
      final previewProps = ImagePreviewProps(
        images: imagePaths,
        initialIndex: initialIndex.clamp(0, imagePaths.length - 1),
        showDeleteButton: widget.canDelete,
        showDownloadButton: widget.canDownload,
        onDelete: widget.canDelete ? _onPreviewDelete : null,
      );

      // 跳转到预览页面
      Get.toNamed(Routes.imagePreview, arguments: previewProps);
    } catch (e) {
      // 如果GetX导航失败，显示提示信息
      debugPrint('图片预览功能需要在GetMaterialApp环境中使用: $e');

      // 可以在这里添加一个简单的对话框或其他预览方式
      _showSimpleImageDialog(imagePaths, initialIndex);
    }
  }

  /// 显示简单的图片对话框（当GetX不可用时）
  void _showSimpleImageDialog(List<String> imagePaths, int initialIndex) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('图片预览'),
        content: Text('当前图片: ${initialIndex + 1}/${imagePaths.length}\n\n'
                     '完整的图片预览功能需要在GetMaterialApp环境中使用。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 获取所有图片路径
  List<String> _getImagePaths() {
    return widget.files.map((file) {
      if (file.file != null) {
        return file.file!.path;
      } else if (file.remotePath != null && file.remotePath!.isNotEmpty) {
        return file.remotePath!;
      } else if (file.assetPath != null && file.assetPath!.isNotEmpty) {
        return file.assetPath!;
      }
      return '';
    }).where((path) => path.isNotEmpty).toList();
  }

  /// 预览页面删除回调
  void _onPreviewDelete(int index) {
    if (index >= 0 && index < widget.files.length) {
      final fileToDelete = widget.files[index];
      _removeFile(fileToDelete);
    }
  }
}
