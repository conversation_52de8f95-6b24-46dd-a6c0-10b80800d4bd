import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 验证码输入组件
class VerificationCodeInput extends StatefulWidget {
  /// 验证码长度，默认6位
  final int codeLength;

  /// 输入框宽度
  final double inputWidth;

  /// 输入框高度
  final double inputHeight;

  /// 输入框间距
  final double spacing;

  /// 输入完成回调
  final Function(String code)? onCompleted;

  /// 输入变化回调
  final Function(String code)? onChanged;

  /// 是否自动聚焦
  final bool autoFocus;

  /// 输入框样式
  final InputBoxDecoration? decoration;

  /// 文本样式
  final TextStyle? textStyle;

  /// 是否启用
  final bool enabled;

  const VerificationCodeInput({
    super.key,
    this.codeLength = 6,
    this.inputWidth = 45,
    this.inputHeight = 56,
    this.spacing = 38,
    this.onCompleted,
    this.onChanged,
    this.autoFocus = true,
    this.decoration,
    this.textStyle,
    this.enabled = true,
  });

  @override
  State<VerificationCodeInput> createState() => VerificationCodeInputState();
}

class VerificationCodeInputState extends State<VerificationCodeInput> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  String _currentCode = '';
  bool _isInitialized = false; // 添加初始化标志

  @override
  void initState() {
    super.initState();
    _initControllers();

    // 自动聚焦第一个输入框
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_focusNodes.isNotEmpty) {
          _focusNodes[0].requestFocus();
        }
      });
    }
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  /// 初始化控制器和焦点节点
  void _initControllers() {
    _controllers = List.generate(
      widget.codeLength,
          (index) => TextEditingController(),
    );
    _focusNodes = List.generate(
      widget.codeLength,
          (index) => FocusNode(),
    );

    // 直接添加监听器，但在回调中检查是否是初始化阶段
    for (int i = 0; i < widget.codeLength; i++) {
      _controllers[i].addListener(() => _onTextChanged(i));
    }

    // 标记初始化完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _isInitialized = true;
    });
  }

  /// 销毁控制器和焦点节点
  void _disposeControllers() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
  }

  /// 处理文本变化
  void _onTextChanged(int index) {
    // 如果还在初始化阶段，不触发回调
    if (!_isInitialized) return;

    final text = _controllers[index].text;

    // 如果输入了多个字符，只保留最后一个
    if (text.length > 1) {
      _controllers[index].text = text.substring(text.length - 1);
      _controllers[index].selection = TextSelection.fromPosition(
        TextPosition(offset: _controllers[index].text.length),
      );
    }

    // 更新当前验证码
    _updateCurrentCode();

    // 如果输入了数字且不是最后一个输入框，自动跳转到下一个
    if (text.isNotEmpty && index < widget.codeLength - 1) {
      _focusNodes[index + 1].requestFocus();
    }

    // 如果验证码输入完整，触发完成回调
    if (_currentCode.length == widget.codeLength) {
      widget.onCompleted?.call(_currentCode);
    }
  }

  /// 更新当前验证码
  void _updateCurrentCode() {
    String code = '';
    for (var controller in _controllers) {
      code += controller.text;
    }

    // 只有当代码真正变化时才触发回调
    if (_currentCode != code) {
      _currentCode = code;
      widget.onChanged?.call(_currentCode);
    }
  }

  /// 清空验证码
  void clear() {
    // 暂时禁用初始化标志，避免清空时触发不必要的回调
    final wasInitialized = _isInitialized;
    _isInitialized = false;

    for (var controller in _controllers) {
      controller.clear();
    }

    // 恢复初始化标志
    _isInitialized = wasInitialized;
    _currentCode = '';
    widget.onChanged?.call(_currentCode);

    if (_focusNodes.isNotEmpty) {
      _focusNodes[0].requestFocus();
    }
  }

  /// 设置验证码
  void setCode(String code) {
    final wasInitialized = _isInitialized;
    _isInitialized = false;

    for (int i = 0; i < widget.codeLength; i++) {
      if (i < code.length) {
        _controllers[i].text = code[i];
      } else {
        _controllers[i].clear();
      }
    }

    _isInitialized = wasInitialized;
    _currentCode = code;
    widget.onChanged?.call(_currentCode);
  }

  /// 获取当前验证码
  String get currentCode => _currentCode;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      spacing: widget.spacing,
      children: List.generate(
        widget.codeLength,
        (index) => _buildSingleInput(index),
      ),
    );
  }

  /// 构建单个输入框
  Widget _buildSingleInput(int index) {
    final decoration = widget.decoration ?? InputBoxDecoration();
    final textStyle = widget.textStyle ?? const TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      color: Colors.black,
    );

    return Expanded(
        child: Container(
      width: widget.inputWidth,
      height: widget.inputHeight,
      decoration: BoxDecoration(
        border:  Border(bottom: BorderSide(color: _focusNodes[index].hasFocus
            ? decoration.focusedBorderColor
            : decoration.borderColor,
          width: _focusNodes[index].hasFocus
              ? decoration.focusedBorderWidth
              : decoration.borderWidth,
        )),
        // borderRadius: BorderRadius.circular(decoration.borderRadius),
        // color: widget.enabled ? decoration.backgroundColor : decoration.disabledBackgroundColor,
      ),
      child: RawKeyboardListener(
        focusNode: FocusNode(),
        onKey: (RawKeyEvent event) {
          // 处理删除键事件
          if (event is RawKeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.backspace) {
              // 如果当前输入框为空且不是第一个，跳转到前一个输入框
              if (_controllers[index].text.isEmpty && index > 0) {
                _focusNodes[index - 1].requestFocus();
              }
            }
          }
        },
        child: TextField(
          controller: _controllers[index],
          focusNode: _focusNodes[index],
          enabled: widget.enabled,
          textAlign: TextAlign.center,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(1),
          ],
          style: textStyle.copyWith(
            color: widget.enabled ? textStyle.color : Colors.grey,
          ),
          decoration: const InputDecoration(
            border: InputBorder.none,
            counterText: '',
            contentPadding: EdgeInsets.zero,
          ),
          onTap: () {
            // 点击时将光标移到末尾
            _controllers[index].selection = TextSelection.fromPosition(
              TextPosition(offset: _controllers[index].text.length),
            );
            // 如果当前输入框有内容，选中所有内容以便替换
            if (_controllers[index].text.isNotEmpty) {
              _controllers[index].selection = TextSelection(
                baseOffset: 0,
                extentOffset: _controllers[index].text.length,
              );
            }
          },
          onChanged: (text) {
            // 在这里直接处理输入变化，确保快速输入时也能正确跳转
            if (text.isNotEmpty && index < widget.codeLength - 1) {
              _focusNodes[index + 1].requestFocus();
            }
          },
          // 添加键盘事件监听，处理删除键
          onEditingComplete: () {
            // 当输入完成时，如果不是最后一个输入框，跳转到下一个
            if (index < widget.codeLength - 1) {
              _focusNodes[index + 1].requestFocus();
            }
          },
        ),
      ),
    ));
  }
}

/// 输入框装饰配置
class InputBoxDecoration {
  /// 边框颜色
  final Color borderColor;

  /// 聚焦时边框颜色
  final Color focusedBorderColor;

  /// 边框宽度
  final double borderWidth;

  /// 聚焦时边框宽度
  final double focusedBorderWidth;

  /// 圆角半径
  final double borderRadius;

  /// 背景颜色
  final Color backgroundColor;

  /// 禁用时背景颜色
  final Color disabledBackgroundColor;

  const InputBoxDecoration({
    this.borderColor = const Color(0xFFE0E0E0),
    this.focusedBorderColor = const Color(0xFF4ACE7F),
    this.borderWidth = 1,
    this.focusedBorderWidth = 2,
    this.borderRadius = 8,
    this.backgroundColor = Colors.white,
    this.disabledBackgroundColor = const Color(0xFFF5F5F5),
  });
}