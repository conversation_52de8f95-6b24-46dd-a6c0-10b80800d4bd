import 'package:flutter/material.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item_widget.dart';
import 'package:get/get.dart';

class TDActionSheetGrid extends StatefulWidget {
  final List<TDActionSheetItem> items;
  final String titleLftText;
  final String titleCenterText;
  final String cancelText;
  final VoidCallback? onTitleLeftTap;
  final VoidCallback? onAddButtonTap;
  final VoidCallback? onCancel;
  final Function(List<TDActionSheetItem> selectedItems)? onSelected;
  final int columns; // 每行显示的列数
  final double itemMaxHeight; // 单个项目的高度

  const TDActionSheetGrid({
    super.key,
    required this.items,
    this.titleLftText = '',
    this.onTitleLeftTap,
    this.titleCenterText = '',
    this.cancelText = '取消',
    this.onCancel,
    this.onSelected,
    this.onAddButtonTap,
    this.columns = 3, // 默认每行4列
    this.itemMaxHeight = 52.0, // 默认高度96
  });

  @override
  createState() => _TDActionSheetGridState();
}

class _TDActionSheetGridState extends State<TDActionSheetGrid> {
  List<TDActionSheetItem> _items = [];

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(18), topRight: Radius.circular(18)),
        color: Colors.white,
      ),
      clipBehavior: Clip.antiAlias,
      padding: EdgeInsets.zero,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //标题
          buildTitleView(context,
              leftTitleText: widget.titleLftText,
              centerTitleText: widget.titleCenterText,
              onTitleLeftTap: widget.onTitleLeftTap,
              onCancel: widget.onCancel),
          // 中间的内容
          _buildGrid(context),

          // 取消按钮和确定按钮
          buildBottomButton(context, widget.cancelText, widget.onCancel, () {
            var select = _items.where((item) => item.selected).toList();
            widget.onSelected?.call(select);
          }),
        ],
      ),
    );
  }

  /// 构建宫格布局
  Widget _buildGrid(BuildContext context) {
    if (_items.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    // 计算行数
    final int rows = (_items.length / widget.columns).ceil();

    return Container(
      padding: const EdgeInsets.all(13),
      constraints: BoxConstraints(minHeight: 200),
      child: Column(
        spacing: 8,
        children: List.generate(rows, (rowIndex) {
          return _buildRow(rowIndex);
        }),
      ),
    );
  }

  /// 构建单行
  Widget _buildRow(int rowIndex) {
    final int startIndex = rowIndex * widget.columns;
    final int endIndex = (startIndex + widget.columns).clamp(0, _items.length);
    final List<TDActionSheetItem> rowItems =
        _items.sublist(startIndex, endIndex);

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 16,
        children: List.generate(widget.columns, (colIndex) {
          if (colIndex < rowItems.length) {
            // 有实际数据的项目
            return Expanded(
              child: TDActionSheetGridItemWidget(
                item: rowItems[colIndex],
                onSelected: _onItemSelected,
                index: startIndex + colIndex,
              ),
            );
          } else {
            // 空白占位项目
            return Expanded(
              child: Container(),
            );
          }
        }),
      ),
    );
  }

  /// 处理项目选中
  void _onItemSelected(TDActionSheetItem item, int index) {
    if (item.isAddButton) {
      _handleAddButtonTap();
      return;
    } // 添加按钮不可选中
    setState(() {
      item.selected = !item.selected;
    });
  }

  /// 处理添加按钮点击 - 保持弹窗在页面下方
  void _handleAddButtonTap() async {
    try {
      // 不关闭弹窗，直接跳转到添加记录页面并等待返回结果
      final result = await Get.toNamed(Routes.recordMultipleEdit);

      if (result != null && result is Map<String, dynamic>) {
        final success = result['success'] as bool? ?? false;
        final name = result['name'] as String? ?? '';

        if (success && name.isNotEmpty) {
          // 创建新的记录项
          final newItem = TDActionSheetItem(
            label: name,
            count: 0,
            selected: false,
            isAddButton: false,
          );

          // 添加到列表中（在添加按钮之前）
          setState(() {
            // 找到添加按钮的位置
            final addButtonIndex = _items.indexWhere((item) => item.isAddButton);
            if (addButtonIndex != -1) {
              // 在添加按钮之前插入新项目
              _items.insert(addButtonIndex, newItem);
            } else {
              // 如果没有找到添加按钮，直接添加到末尾
              _items.add(newItem);
            }
          });

          // 显示成功提示
          Get.snackbar(
            '添加成功',
            '记录 "$name" 已添加到列表',
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );
        }
      }
    } catch (e) {
      debugPrint('处理添加记录回调失败: $e');
      Get.snackbar(
        '操作失败',
        '添加记录时发生错误',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }
}
