import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item_widget.dart';

class TDActionSheetList extends StatefulWidget {
  final List<TDActionSheetItem> items;
  final String titleLeftText;
  final String titleCenterText;
  final VoidCallback? onTitleLeftTap;
  final String cancelText;
  final VoidCallback? onCancel;
  final Function(List<TDActionSheetItem> selectedItems)? onSelected;
  final VoidCallback? onAddButtonTap;
  final Function(TDActionSheetItem item, int index, int newCount)? onCountChanged;
  final double itemHeight; // 单个项目的高度

  const TDActionSheetList({
    super.key,
    required this.items,
    this.titleLeftText = '',
    this.onTitleLeftTap,
    this.titleCenterText = '',
    this.cancelText = '取消',
    this.onCancel,
    this.onSelected,
    this.onAddButtonTap,
    this.onCountChanged,
    this.itemHeight = 40.0, // 默认高度96
  });

  @override
  createState() => _TDActionSheetListState();
}

class _TDActionSheetListState extends State<TDActionSheetList> {
  int currentPage = 0;
  List<TDActionSheetItem> _items = [];

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10), topRight: Radius.circular(10)),
        color: Colors.white,
      ),
      clipBehavior: Clip.antiAlias,
      padding: EdgeInsets.zero,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //标题
          buildTitleView(context,
              leftTitleText: widget.titleLeftText,
              centerTitleText: widget.titleCenterText,
              onCancel: widget.onCancel),
          // 中间的内容
          _buildListView(context),

          // 取消按钮和确定按钮
          buildBottomButton(context, widget.cancelText, widget.onCancel, () {
            var select = _items.where((item) => item.selected).toList();
            widget.onSelected?.call(select);
          }),
        ],
      ),
    );
  }

  /// 构建底部列表
  Widget _buildListView(BuildContext context) {
    if (_items.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(13),
      constraints: BoxConstraints(minHeight: 200),
      child: Column(
        spacing: 8,
        children: List.generate(_items.length, (index) {
          return _buildRow(index);
        }),
      ),
    );
  }

  /// 构建单行
  Widget _buildRow(int index) {
    return IntrinsicHeight(
      child: TDActionSheetListItemWidget(
        item: _items[index],
        index: index,
        onCountChanged: _onCountChanged,
      ),
    );
  }

  /// 处理数量变化
  void _onCountChanged(TDActionSheetItem item, int index, int newCount) {
    setState(() {
      // 更新本地数据
      _items[index] = TDActionSheetItem(
        id: item.id,
        label: item.label,
        count: newCount,
        selected: item.selected,
        isAddButton: item.isAddButton,
      );
    });

    // 调用外部回调
    widget.onCountChanged?.call(item, index, newCount);
  }

}
