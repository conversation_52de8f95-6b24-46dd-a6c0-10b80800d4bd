import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';

typedef TDActionSheetItemCallback = void Function(
    TDActionSheetItem item, int index);

/// ActionSheet 宫格项组件
class TDActionSheetGridItemWidget extends StatelessWidget {
  final TDActionSheetItem item;
  final TDActionSheetItemCallback? onSelected;
  final int index;

  const TDActionSheetGridItemWidget({
    super.key,
    required this.item,
    this.onSelected,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onSelected?.call(item, index);
      },
      child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
          decoration: BoxDecoration(
            color: item.selected
                ? ColorsUtil.primaryColor.withOpacity(0.1)
                : Color(0xFFF2F2F2),
            borderRadius: BorderRadius.circular(5),
            border: item.selected
                ? Border.all(color: ColorsUtil.primaryColor, width: 0.5)
                : null,
          ),
          constraints: BoxConstraints(minHeight: 42),
          child: buildGridItemView(item)),
    );
  }
}

Widget buildGridItemView(TDActionSheetItem item) {
  if (item.isAddButton) {
    return Icon(
      Icons.add,
      color: ColorsUtil.textBlack,
      size: 24,
    );
  }

  return Center(
      child: Text(
    item.label,
    style: TextStyle(
      fontSize: 16,
      color: item.selected ? ColorsUtil.primaryColor : ColorsUtil.textBlack,
    ),
    textAlign: TextAlign.center,
    maxLines: 2,
    overflow: TextOverflow.ellipsis,
  ));
}

class TDActionSheetListItemWidget extends StatelessWidget {
  final TDActionSheetItem item;
  final int index;
  final Function(TDActionSheetItem item, int index, int newCount)?
      onCountChanged;

  const TDActionSheetListItemWidget({
    super.key,
    required this.item,
    required this.index,
    this.onCountChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
          border:
              Border(bottom: BorderSide(color: Color(0xFFE5E5E5), width: 0.5)),
        ),
        constraints: BoxConstraints(minHeight: 43),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              item.label,
              style: TextStyle(
                fontSize: 16,
                color: ColorsUtil.textBlack,
              ),
            ),
            // 加减控件
            _buildCounterWidget()
          ],
        ));
  }

  /// 构建加减控件
  Widget _buildCounterWidget() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 减号按钮
        GestureDetector(
          onTap: () {
            if (item.count > 0) {
              final newCount = item.count - 1;
              onCountChanged?.call(item, index, newCount);
            }
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: item.count > 0 ? ColorsUtil.primaryColor : Colors.grey,
                width: 1,
              ),
            ),
            child: Icon(
              Icons.remove,
              size: 16,
              color: item.count > 0 ? ColorsUtil.primaryColor : Colors.grey,
            ),
          ),
        ),

        // 数量显示
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            '${item.count}',
            style: TextStyle(
              fontSize: 16,
              color: ColorsUtil.textBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // 加号按钮
        GestureDetector(
          onTap: () {
            final newCount = item.count + 1;
            onCountChanged?.call(item, index, newCount);
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: ColorsUtil.primaryColor,
                width: 1,
              ),
            ),
            child: Icon(
              Icons.add,
              size: 16,
              color: ColorsUtil.primaryColor,
            ),
          ),
        ),
      ],
    );
  }
}

/// 构建顶部布局
Widget buildTitleView(BuildContext context,
    {String? leftTitleText, String? centerTitleText,VoidCallback? onTitleLeftTap, VoidCallback? onCancel}) {
  return Container(
    height: 56,
    padding: const EdgeInsets.symmetric(horizontal: 16),
    decoration: const BoxDecoration(
      color: Colors.white,
      border: Border(
        bottom: BorderSide(
          color: Color(0xFFE7E7E7),
          width: 0.5,
        ),
      ),
    ),
    child: Stack(
      alignment: Alignment.center,
      children: [
        // 左侧标题
        if (leftTitleText != null && leftTitleText.isNotEmpty)
          Align(
            alignment: Alignment.centerLeft,
            child: GestureDetector(
              onTap: onTitleLeftTap,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.settings,
                    size: 18,
                    color: ColorsUtil.gary58,
                  ),
                  Text(
                    leftTitleText,
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorsUtil.textBlack,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

        // 中间标题
        if (centerTitleText != null && centerTitleText.isNotEmpty)
          Text(
            centerTitleText,
            style: TextStyle(
              fontSize: 18,
              color: ColorsUtil.textBlack,
              fontWeight: FontWeight.w600,
            ),
          ),

        // 右侧关闭按钮
        Align(
          alignment: Alignment.centerRight,
          child: GestureDetector(
            onTap: () {
              DialogCore.closeDialog();
              onCancel?.call();
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.close,
                size: 24,
                color: ColorsUtil.gary58,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

Widget buildBottomButton(BuildContext context, String cancelText,
    VoidCallback? onCancel, VoidCallback? onConfirm) {
  return Container(
      padding: EdgeInsets.only(top: 0, bottom: 16, left: 13, right: 13),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              onCancel?.call();
            },
            child: Container(
              height: 42,
              width: 92,
              decoration: BoxDecoration(
                color: Color(0xFFEFEFEF),
                borderRadius: BorderRadius.circular(7),
              ),
              child: Center(
                child: Text(
                  cancelText,
                  style: TextStyle(
                      fontSize: 16,
                      color: ColorsUtil.textBlack,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () {
                onConfirm?.call();
              },
              child: Container(
                height: 42,
                width: 92,
                decoration: BoxDecoration(
                  color: ColorsUtil.primaryColor,
                  borderRadius: BorderRadius.circular(7),
                ),
                child: Center(
                  child: Text(
                    "确定",
                    style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            ),
          )
        ],
      ));
}
