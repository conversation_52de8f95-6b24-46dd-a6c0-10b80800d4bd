import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/image_preview/entity/image_preview_props.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 图片网格组件
/// 一行显示4个图片，最多显示4个，超过时在最后一个显示+数量
/// 支持自定义宽高和每行数量（当enableCustomLayout为true时）
class ImageGridWidget extends StatelessWidget {
  const ImageGridWidget({
    super.key,
    required this.images,
    this.onImageTap,
    this.itemsPerRow = 3,
    this.itemWidth,
    this.itemHeight,
    this.spacing = 10.0,
    this.enableCustomLayout = false,
  });

  /// 图片URL列表
  final List<String> images;

  /// 图片点击回调
  final Function(int index)? onImageTap;

  /// 每行显示的图片数量，默认3个（仅在enableCustomLayout为true时生效）
  final int itemsPerRow;

  /// 图片宽度，如果不传则自动计算
  final double? itemWidth;

  /// 图片高度，如果不传则使用宽度（正方形）
  final double? itemHeight;

  /// 图片间距，默认10.0
  final double spacing;

  /// 是否启用自定义布局（9宫格模式），默认false保持原有4个图片+数量遮罩的逻辑
  final bool enableCustomLayout;

  @override
  Widget build(BuildContext context) {
    if (images.isEmpty) {
      return const SizedBox.shrink();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        if (enableCustomLayout) {
          // 新的9宫格布局
          return _buildCustomLayout(constraints);
        } else {
          // 原有的4个图片+数量遮罩布局
          return _buildOriginalLayout(constraints);
        }
      },
    );
  }

  /// 构建原有布局（4个图片+数量遮罩）
  Widget _buildOriginalLayout(BoxConstraints constraints) {
    // 计算每个图片的宽度
    final defaultItemsPerRow = 4;
    final totalSpacing = (defaultItemsPerRow - 1) * spacing;
    final calculatedWidth = itemWidth ??
        (constraints.maxWidth - totalSpacing) / defaultItemsPerRow;
    final calculatedHeight = itemHeight ?? 65.0;

    // 最多显示4个图片
    final displayImages = images.take(4).toList();

    return SizedBox(
      height: calculatedHeight,
      child: Row(
        children: List.generate(displayImages.length, (index) {
          final isLast = index == displayImages.length - 1;
          final hasMore = images.length > 4 && isLast;

          return Row(
            children: [
              _buildImageItem(
                imageUrl: displayImages[index],
                width: calculatedWidth,
                height: calculatedHeight,
                index: index,
                showMoreCount: hasMore ? images.length - 3 : null,
              ),
              if (index < displayImages.length - 1)
                SizedBox(width: spacing),
            ],
          );
        }),
      ),
    );
  }

  /// 构建自定义布局（9宫格）
  Widget _buildCustomLayout(BoxConstraints constraints) {
    // 计算图片尺寸
    final totalSpacing = (itemsPerRow - 1) * spacing;
    final calculatedWidth = itemWidth ??
        (constraints.maxWidth - totalSpacing) / itemsPerRow;
    final calculatedHeight = itemHeight ?? calculatedWidth;

    // 计算需要的行数
    final totalRows = (images.length / itemsPerRow).ceil();

    return Column(
      children: List.generate(totalRows, (rowIndex) {
        final startIndex = rowIndex * itemsPerRow;
        final endIndex = (startIndex + itemsPerRow).clamp(0, images.length);
        final rowImages = images.sublist(startIndex, endIndex);

        return Column(
          children: [
            if (rowIndex > 0) SizedBox(height: spacing),
            _buildImageRow(
              rowImages: rowImages,
              startIndex: startIndex,
              itemWidth: calculatedWidth,
              itemHeight: calculatedHeight,
            ),
          ],
        );
      }),
    );
  }

  /// 构建图片行
  Widget _buildImageRow({
    required List<String> rowImages,
    required int startIndex,
    required double itemWidth,
    required double itemHeight,
  }) {
    return Row(
      children: List.generate(rowImages.length, (index) {
        final imageIndex = startIndex + index;

        return Row(
          children: [
            _buildImageItem(
              imageUrl: rowImages[index],
              width: itemWidth,
              height: itemHeight,
              index: imageIndex,
            ),
            if (index < rowImages.length - 1)
              SizedBox(width: spacing),
          ],
        );
      }),
    );
  }

  /// 构建单个图片项
  Widget _buildImageItem({
    required String imageUrl,
    required double width,
    required double height,
    required int index,
    int? showMoreCount,
  }) {
    return GestureDetector(
      onTap: () => _handleImageTap(index),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
          color: Colors.grey[200],
        ),
        child: Stack(
          children: [
            // 图片
            ClipRRect(
              borderRadius: BorderRadius.circular(4.r),
              child: Image.network(
                imageUrl,
                width: width,
                height: height,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: width,
                    height: height,
                    color: Colors.grey[200],
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                        strokeWidth: 2,
                        color: ColorsUtil.primaryColor,
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: width,
                    height: height,
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.broken_image,
                      color: Colors.grey[400],
                      size: 24,
                    ),
                  );
                },
              ),
            ),

            // 更多数量遮罩
            if (showMoreCount != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(4.r),
                child: Container(
                  width: width,
                  height: height,
                  color: Colors.black.withValues(alpha: 0.6),
                  child: Center(
                    child: Text(
                      '+$showMoreCount',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 处理图片点击
  void _handleImageTap(int index) {
    // 调用外部回调
    onImageTap?.call(index);

    // 跳转到大图预览页面
    Get.toNamed(
      Routes.imagePreview,
      arguments: ImagePreviewProps(
        images: images,
        initialIndex: index,
      ),
    );
  }
}
