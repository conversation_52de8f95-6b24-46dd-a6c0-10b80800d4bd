import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_grid.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';

/// 多选记录底部弹窗
class MultipleRecordDialog {
  static void show({
    required BuildContext context,
    required List<TDActionSheetItem> items,
    String titleLftText = '',
    VoidCallback? onTitleLeftTap,
    String titleCenterText = '',
    String cancelText = '取消',
    VoidCallback? onCancel,
    Function(List<TDActionSheetItem> selectedItems)? onSelected,
    int columns = 3,
    double itemMaxHeight = 52.0,
    bool barrierDismissible = true,
    Color? barrierColor,
  }) {
    DialogCore.openDialog(
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5),
        useSystem:  true,
        bindWidget:  context,
        builder: (context) => TDActionSheetGrid(
              items: items,
              titleLftText: titleLftText,
              onTitleLeftTap: () {
                DialogCore.closeDialog();
                onTitleLeftTap?.call();
              },
              titleCenterText: titleCenterText,
              cancelText: cancelText,
              onCancel: () {
                DialogCore.closeDialog();
              },
              onSelected: (selectedItems) {
                onSelected?.call(selectedItems);
                DialogCore.closeDialog();
              },
              columns: columns,
              itemMaxHeight: itemMaxHeight,
            ));
  }
}
