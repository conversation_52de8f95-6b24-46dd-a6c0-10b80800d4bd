import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 月份选择器工具类
class DatePickersDialog {
  /// 显示月份选择器
  /// [context] 上下文
  /// [initialYear] 初始年份，默认当前年份
  /// [initialMonth] 初始月份，默认当前月份
  /// [onSelected] 选择回调 (year, month)
  static void show({
    required BuildContext context,
    required DateTime initialDate,
    required bool isStartDate,
    DateTime? minDate,
    required  Function(DateTime) onConfirm,
  }) {
    DialogCore.openDialog(
        builder: (context) => DatePickerWidget(
          initialDate: initialDate,
          minDate: minDate,
          isStartDate: isStartDate,
          onConfirm: onConfirm,
        ),
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }
}

/// 日期选择器组件
class DatePickerWidget extends StatefulWidget {
  final DateTime initialDate;
  final Function(DateTime) onConfirm;
  final DateTime? minDate; // 结束日期选择时的最小限制
  final bool isStartDate; // 区分开始/结束日期选择器

  const DatePickerWidget({
    super.key,
    required this.initialDate,
    required this.isStartDate,
    required this.onConfirm,
    this.minDate,
  });

  @override
  State<DatePickerWidget> createState() => _DatePickerWidgetState();
}

class _DatePickerWidgetState extends State<DatePickerWidget> {
  late FixedExtentScrollController _yearController;
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;

  late int _selectedYear;
  late int _selectedMonth;
  late int _selectedDay;
  late List<int> _years;
  final DateTime _now = DateTime.now();

  Timer? _debounceTimer; // 防抖定时器，避免频繁验证

  @override
  void initState() {
    super.initState();
    _initializeData();
    _initializeControllers();
  }

  /// 初始化数据
  void _initializeData() {
    // 结束日期选择器的年份范围从minDate开始，否则显示近6年
    _years = !widget.isStartDate && widget.minDate != null
        ? List.generate(_now.year - widget.minDate!.year + 1,
            (index) => widget.minDate!.year + index)
        : List.generate(6, (index) => _now.year - 5 + index);

    // 确保初始日期不超过当前时间且不小于最小限制
    var initialDate =
        widget.initialDate.isAfter(_now) ? _now : widget.initialDate;
    if (widget.minDate != null && initialDate.isBefore(widget.minDate!)) {
      initialDate = widget.minDate!;
    }

    _selectedYear = initialDate.year;
    _selectedMonth = initialDate.month;
    _selectedDay = initialDate.day;
  }

  /// 初始化滚动控制器
  void _initializeControllers() {
    final yearIndex = _years.indexOf(_selectedYear);
    _yearController = FixedExtentScrollController(
        initialItem: yearIndex >= 0 ? yearIndex : 0);
    _monthController = FixedExtentScrollController(
        initialItem: _selectedMonth - _getMinMonth(_selectedYear));
    _dayController = FixedExtentScrollController(
        initialItem: _selectedDay - _getMinDay(_selectedYear, _selectedMonth));
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    super.dispose();
  }

  /// 获取指定年月的天数
  int _getDaysInMonth(int year, int month) => DateTime(year, month + 1, 0).day;

  /// 获取年份对应的最小月份（结束日期选择器受minDate限制）
  int _getMinMonth(int year) {
    return (!widget.isStartDate &&
            widget.minDate != null &&
            year == widget.minDate!.year)
        ? widget.minDate!.month
        : 1;
  }

  /// 获取年份对应的最大月份（不能超过当前月份）
  int _getMaxMonth(int year) => year == _now.year ? _now.month : 12;

  /// 获取年月对应的最小日期
  int _getMinDay(int year, int month) {
    return (!widget.isStartDate &&
            widget.minDate != null &&
            year == widget.minDate!.year &&
            month == widget.minDate!.month)
        ? widget.minDate!.day
        : 1;
  }

  /// 获取年月对应的最大日期（不能超过当前日期）
  int _getMaxDay(int year, int month) {
    return (year == _now.year && month == _now.month)
        ? _now.day
        : _getDaysInMonth(year, month);
  }

  /// 验证并调整选择的日期到有效范围内
  void _validateAndAdjustDate() {
    final maxMonth = _getMaxMonth(_selectedYear);
    final minMonth = _getMinMonth(_selectedYear);

    if (_selectedMonth > maxMonth) {
      _selectedMonth = maxMonth;
    } else if (_selectedMonth < minMonth) {
      _selectedMonth = minMonth;
    }

    final maxDay = _getMaxDay(_selectedYear, _selectedMonth);
    final minDay = _getMinDay(_selectedYear, _selectedMonth);

    if (_selectedDay > maxDay) {
      _selectedDay = maxDay;
    } else if (_selectedDay < minDay) {
      _selectedDay = minDay;
    }

    _reinitializeControllers();
  }

  /// 重新初始化控制器位置以适应调整后的数据
  void _reinitializeControllers() {
    if (!mounted) return;

    final minMonth = _getMinMonth(_selectedYear);
    final minDay = _getMinDay(_selectedYear, _selectedMonth);

    final monthIndex = _selectedMonth - minMonth;
    if (_monthController.hasClients && monthIndex >= 0) {
      _monthController.jumpToItem(monthIndex);
    }

    final dayIndex = _selectedDay - minDay;
    if (_dayController.hasClients && dayIndex >= 0) {
      _dayController.jumpToItem(dayIndex);
    }

    setState(() {});
  }

  /// 防抖机制：滚动停止后执行验证
  void _scheduleDelayedUpdate() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 600), () {
      if (mounted) {
        _validateAndAdjustDate();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 250.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        children: [
          _buildTitleBar(),
          Expanded(child: _buildPickerArea()),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: ColorsUtil.divideLineColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => DialogCore.closeDialog(),
            child: Icon(
              Icons.close,
              size: 20.w,
              color: const Color(0xFF8A8A99),
            ),
          ),
          Text(
            '请选择时间',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF323233),
            ),
          ),
          GestureDetector(
            onTap: _onConfirm,
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16.sp,
                color: ColorsUtil.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建选择器区域
  Widget _buildPickerArea() {
    return Stack(
      children: [
        Row(
          children: [
            Expanded(child: _buildYearColumn()),
            Expanded(child: _buildMonthColumn()),
            Expanded(child: _buildDayColumn()),
          ],
        ),
        Center(
          child: Container(
            height: 40.h,
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: Color(0xFFE0E0E0), width: 1),
                bottom: BorderSide(color: Color(0xFFE0E0E0), width: 1),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建年份选择列
  Widget _buildYearColumn() {
    return _buildScrollColumn(
      controller: _yearController,
      itemCount: _years.length,
      onSelectedItemChanged: (index) {
        _selectedYear = _years[index];
        _scheduleDelayedUpdate();
      },
      itemBuilder: (index) => '${_years[index]}年',
    );
  }

  /// 构建月份选择列
  Widget _buildMonthColumn() {
    final minMonth = _getMinMonth(_selectedYear);
    final maxMonth = _getMaxMonth(_selectedYear);

    return _buildScrollColumn(
      controller: _monthController,
      itemCount: maxMonth - minMonth + 1,
      onSelectedItemChanged: (index) {
        _selectedMonth = minMonth + index;
        _scheduleDelayedUpdate();
      },
      itemBuilder: (index) =>
          '${(minMonth + index).toString().padLeft(2, '0')}月',
    );
  }

  /// 构建日期选择列
  Widget _buildDayColumn() {
    final minDay = _getMinDay(_selectedYear, _selectedMonth);
    final maxDay = _getMaxDay(_selectedYear, _selectedMonth);

    return _buildScrollColumn(
      controller: _dayController,
      itemCount: maxDay - minDay + 1,
      onSelectedItemChanged: (index) {
        _selectedDay = minDay + index;
      },
      itemBuilder: (index) => '${(minDay + index).toString().padLeft(2, '0')}日',
    );
  }

  /// 滚动列通用构建方法
  Widget _buildScrollColumn({
    required FixedExtentScrollController controller,
    required int itemCount,
    required Function(int) onSelectedItemChanged,
    required String Function(int) itemBuilder,
  }) {
    return ListWheelScrollView.useDelegate(
      controller: controller,
      itemExtent: 40.h,
      physics: const FixedExtentScrollPhysics(),
      useMagnifier: false,
      onSelectedItemChanged: onSelectedItemChanged,
      childDelegate: ListWheelChildBuilderDelegate(
        childCount: itemCount,
        builder: (context, index) {
          if (index < 0 || index >= itemCount) return null;
          return Container(
            alignment: Alignment.center,
            child: Text(
              itemBuilder(index),
              style: TextStyle(fontSize: 18.sp, color: const Color(0xFF323233)),
            ),
          );
        },
      ),
    );
  }

  /// 确认选择并回调
  void _onConfirm() {
    final selectedDate = DateTime(_selectedYear, _selectedMonth, _selectedDay);
    widget.onConfirm(selectedDate);
    DialogCore.closeDialog();
  }
}
