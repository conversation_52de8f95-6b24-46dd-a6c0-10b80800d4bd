import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_kexue/widget/upload/td_upload.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 打卡成功和破戒弹窗，填写日记
class CheckInSuccessDialog {
  static void show({
    DateTime? dateTime,
    bool? isPrivate = true,
    required Function() onConfirm,
  }) {
    DialogCore.openDialog(
        builder: (context) => CheckInSuccessDialogWidget(
              dateTime: dateTime,
              isPrivate: isPrivate == true,
              onConfirm: onConfirm,
            ),
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }
}

class CheckInSuccessDialogWidget extends StatefulWidget {
  final Function() onConfirm;
  final DateTime? dateTime;
  final bool isPrivate;

  const CheckInSuccessDialogWidget({
    super.key,
    required this.dateTime,
    required this.onConfirm,
    required this.isPrivate,
  });

  @override
  State<CheckInSuccessDialogWidget> createState() =>
      _CheckInSuccessDialogWidgetState();
}

class _CheckInSuccessDialogWidgetState
    extends State<CheckInSuccessDialogWidget> {
  // 文本编辑控制器
  final TextEditingController textController = TextEditingController();

  final List<TDUploadFile> files = [];

  // 单选按钮状态
  bool _isSelect = false; // false: 不隐藏, true: 隐藏

  /// 处理文本变化
  void onTextChanged(String value) {
    // 可以在这里处理文本变化逻辑
    print('文本变化: $value');
  }

  void onClick(int key) {
    print('点击 $key');
  }

  void onCancel() {
    print('取消');
  }

  void onValueChanged(List<TDUploadFile> fileList, List<TDUploadFile> value,
      TDUploadType event) {
    switch (event) {
      case TDUploadType.add:
        setState(() {
          fileList.addAll(value);
        });
        break;
      case TDUploadType.remove:
        setState(() {
          fileList.removeWhere((element) => element.key == value[0].key);
        });
        break;
      case TDUploadType.replace:
        setState(() {
          final firstReplaceFile = value.first;
          final index =
              fileList.indexWhere((file) => file.key == firstReplaceFile.key);
          if (index != -1) {
            fileList[index] = firstReplaceFile;
          }
        });
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxHeight: 654.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        children: [
          _buildTitleBar(),
          _buildDateSection(),
          const SizedBox(height: 20),
          Expanded(child: _buildContent()),
          _buildBottomSection(),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              widget.isPrivate ? '打卡成功' : '破戒',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: widget.isPrivate ? ColorsUtil.primaryColor : ColorsUtil.red,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () => DialogCore.closeDialog(),
              child: Icon(
                Icons.close,
                size: 28.w,
                color: const Color(0xFF8A8A99),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 构建日期显示部分
  Widget _buildDateSection({DateTime? dateTime}) {
    final now = dateTime ?? DateTime.now();
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[now.weekday - 1];
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${now.year}年${now.month}月${now.day}日',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorsUtil.textBlack,
            ),
          ),
          const SizedBox(width: 10),
          Text(
            weekday,
            style: TextStyle(
              fontSize: 16,
              color: ColorsUtil.garyB1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          // 文本编辑区域
          _buildTextEditor(),

          // 图片上传区域
          _uploadMultiple(context),
        ],
      ),
    );
  }

  /// 构建文本编辑器
  Widget _buildTextEditor() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 80),
      alignment: Alignment.topLeft,
      child: TextField(
        controller: textController,
        maxLines: null,
        keyboardType: TextInputType.multiline,
        decoration: InputDecoration(
          hintText: '这一刻的想法...',
          hintStyle: TextStyle(
            fontSize: 16,
            color: ColorsUtil.gary58,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 8),
          isDense: true, // 减少默认的内部间距
        ),
        style: TextStyle(
          fontSize: 16,
          color: ColorsUtil.textBlack,
          height: 1.0, // 设置为1.0以减少行高
        ),
        onChanged: (value) => onTextChanged(value),
      ),
    );
  }

  Widget _uploadMultiple(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取父容器的宽度
        final maxWidth = constraints.maxWidth;
        // 计算每个上传控件的宽度（显示3个，留出间距）
        final itemWidth = (maxWidth - 16) / 3; // 减去间距和边距

        // 确保在 Obx 内部访问响应式变量
        return TDUpload(
          files: files,
          multiple: true,
          max: 9,
          onClick: onClick,
          onCancel: onCancel,
          onError: print,
          onValidate: print,
          width: itemWidth,
          height: itemWidth,
          onMaxLimitReached: () {
            ToastUtil.showToast('最多只能上传9张图片');
          },
          // 保持宽高一致
          onChange: ((fileList, type) => onValueChanged(files, fileList, type)),
        );
      },
    );
  }

  ///底部区域
  Widget _buildBottomSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧单选按钮区域
          _buildRadioSection(),
          // 右侧确定按钮
          Expanded(
            child: _buildConfirmButton(),
          ),
        ],
      ),
    );
  }

  /// 构建单选按钮区域
  Widget _buildRadioSection() {
    return Row(
      children: [
        // 不隐藏选项
        GestureDetector(
          onTap: () {
            setState(() {
              _isSelect = !_isSelect;
            });
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isSelect ? Colors.grey : ColorsUtil.primaryColor,
                ),
                child: Icon(
                  Icons.check,
                  size: 14.w,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '不在弹出',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.garyB1,
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 16.w),
      ],
    );
  }

  /// 构建确定按钮
  Widget _buildConfirmButton() {
    return GestureDetector(
      onTap: () {
        // 处理确定按钮点击
        _handleConfirm();
      },
      child: Container(
        width: double.infinity,
        height: 40.h,
        decoration: BoxDecoration(
          color: widget.isPrivate ?  ColorsUtil.primaryColor : ColorsUtil.red,
          borderRadius: BorderRadius.circular(7.r),
        ),
        child: Center(
          child: Text(
            '保存',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理确定按钮点击
  void _handleConfirm() {
    // 获取输入的文本
    final text = textController.text.trim();

    // 打印调试信息
    print('确定按钮被点击');
    print('输入文本: $text');
    print('是否隐藏: $_isSelect');
    print('上传文件数量: ${files.length}');

    // 这里可以添加数据验证逻辑
    // if (text.isEmpty && files.isEmpty) {
    //   ToastUtil.showToast('请输入内容或上传图片');
    //   return;
    // }

    // 调用外部回调
    widget.onConfirm();
    // 关闭弹窗
    DialogCore.closeDialog();
  }

  @override
  void dispose() {
    super.dispose();
    textController.dispose();
  }
}
