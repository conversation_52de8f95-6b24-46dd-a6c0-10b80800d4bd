import 'package:get/get.dart';

class TargetsUIState {
  final int id;
  final String target_name;
  final int target_type;

  ///完成度
  final int? target_status;

  ///目标天数
  final int? target_day;
  final String? target_motivate;

  TargetsUIState({
    required this.id,
    required this.target_name,
    required this.target_type,
    this.target_status,
    this.target_day,
    this.target_motivate,
  });
}

class TargetsDialogUs {
  final _clocks = <TargetsUIState>[].obs;

  List<TargetsUIState> get clocks => _clocks.value;

  setTargetsClocks(List<TargetsUIState> list) {
    _clocks.value = list;
    _clocks.refresh();
  }

  final _controls = <TargetsUIState>[].obs;

  List<TargetsUIState> get controls => _controls.value;

  setTargetsControl(List<TargetsUIState> list) {
    _controls.value = list;
    _controls.refresh();
  }
}
