import 'package:flutter_kexue/data/target/ds/model/net/targets_net_model.dart';
import 'package:flutter_kexue/data/target/repo/target_repo.dart';
import 'package:flutter_kexue/widget/dialog/targets_dialog/targets_dialog_us.dart';

class TargetsDialogViewModel {
  final _repo = TargetRepo();

  TargetsDialogUs us = TargetsDialogUs();

  getTargets(String planId) async {
    var response = await _repo.getTargets(planId);
    if (response.isSuccess) {
      _coverTargetsClocks(response.data?.clock);
      _coverTargetsControl(response.data?.control);
    }
  }

  void _coverTargetsClocks(List<TargetNetModel>? clock) {
    var list = clock
            ?.map((item) => TargetsUIState(
                  id: item.id,
                  target_name: item.target_name,
                  target_type: item.target_type,
                  target_status: item.target_status,
                  target_day: item.target_day ?? 0,
                  target_motivate: item.target_motivate ?? '',
                ))
            .toList() ??
        [];

    us.setTargetsClocks(list);
  }

  void _coverTargetsControl(List<TargetNetModel>? control) {
    var list = control
            ?.map((item) => TargetsUIState(
                  id: item.id,
                  target_name: item.target_name,
                  target_type: item.target_type,
                  target_status: item.target_status,
                  target_day: item.target_day ?? 0,
                  target_motivate: item.target_motivate ?? '',
                ))
            .toList() ??
        [];
    us.setTargetsClocks(list);
  }
}
