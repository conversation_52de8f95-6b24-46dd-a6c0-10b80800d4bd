import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

/// 图片选择对话框
class ImagePickerDialog {
  /// 显示单选图片选择对话框
  /// [context] 上下文
  /// [onImageSelected] 图片选择回调，返回图片路径
  static void show({
    required Function(String imagePath) onImageSelected,
  }) {
    DialogCore.openDialog(
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5),
        builder: (context) => ImagePickerBottomSheet(
              onImageSelected: onImageSelected,
              multiple: false,
              selectedImages: const [],
            ));
  }

  /// 显示多选图片选择对话框
  /// [context] 上下文
  /// [onImagesSelected] 图片选择回调，返回图片路径列表
  /// [selectedImages] 已选中的图片路径列表
  /// [maxCount] 最大选择数量
  static void showMultiple({
    required BuildContext context,
    required Function(List<String> imagePaths) onImagesSelected,
    List<String> selectedImages = const [],
    int maxCount = 9,
  }) {
    DialogCore.openDialog(
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5),
        builder: (context) => ImagePickerBottomSheet(
              onImagesSelected: onImagesSelected,
              multiple: true,
              selectedImages: selectedImages,
              maxCount: maxCount,
            ));
  }
}

/// 图片选择底部弹窗
class ImagePickerBottomSheet extends StatelessWidget {
  final Function(String imagePath)? onImageSelected;
  final Function(List<String> imagePaths)? onImagesSelected;
  final bool multiple;
  final List<String> selectedImages;
  final int maxCount;

  const ImagePickerBottomSheet({
    super.key,
    this.onImageSelected,
    this.onImagesSelected,
    required this.multiple,
    required this.selectedImages,
    this.maxCount = 9,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拍摄按钮
          _buildActionButton(
            context: context,
            text: '拍摄',
            onTap: () => _handleCamera(context),
          ),

          // 分割线
          Container(
            height: 1.h,
            color: Colors.grey[200],
            margin: EdgeInsets.symmetric(horizontal: 16.w),
          ),

          // 从手机相册选择按钮
          _buildActionButton(
            context: context,
            text: '从手机相册选择',
            onTap: () => _handleGallery(context),
          ),

          // 分割线
          Container(
            height: 8.h,
            color: Colors.grey[100],
          ),

          // 取消按钮
          _buildActionButton(
            context: context,
            text: '取消',
            onTap: () => DialogCore.closeDialog(),
            textColor: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required BuildContext context,
    required String text,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: double.infinity,
        height: 56.h,
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
            color: textColor ?? Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 处理拍照
  Future<void> _handleCamera(BuildContext context) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 80,
      );

      if (image != null && context.mounted) {
        DialogCore.closeDialog();
        if (multiple) {
          // 多选模式：将拍照的图片添加到已选列表
          final newSelectedImages = List<String>.from(selectedImages);
          if (!newSelectedImages.contains(image.path)) {
            newSelectedImages.add(image.path);
          }
          onImagesSelected?.call(newSelectedImages);
        } else {
          // 单选模式
          onImageSelected?.call(image.path);
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, '拍照失败: $e');
      }
    }
  }

  /// 处理从相册选择
  Future<void> _handleGallery(BuildContext context) async {
    try {
      final ImagePicker picker = ImagePicker();

      if (multiple) {
        // 多选模式 - 使用pickMultiImage实现真正的多选
        final List<XFile> images = await picker.pickMultiImage(
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 80,
          limit: maxCount,
        );

        if (images.isNotEmpty && context.mounted) {
          DialogCore.closeDialog();
          // 合并已选图片和新选图片
          final newSelectedImages = List<String>.from(selectedImages);
          int addedCount = 0;

          for (final image in images) {
            if (!newSelectedImages.contains(image.path)) {
              // 检查是否超过最大数量限制
              if (newSelectedImages.length >= maxCount) {
                if (context.mounted) {
                  _showErrorSnackBar(
                      context, '最多只能选择$maxCount张图片，已添加$addedCount张');
                }
                break;
              }
              newSelectedImages.add(image.path);
              addedCount++;
            }
          }

          if (addedCount > 0) {
            onImagesSelected?.call(newSelectedImages);
          } else if (context.mounted) {
            _showErrorSnackBar(context, '所选图片已存在或达到数量限制');
          }
        }
      } else {
        // 单选模式
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 80,
        );

        if (image != null && context.mounted) {
          DialogCore.closeDialog();
          onImageSelected?.call(image.path);
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, '选择图片失败: $e');
      }
    }
  }

  /// 显示错误提示
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
