import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 每日几次完成设置对话框
/// 参考 RecordLibDialog 的调用方式实现
class DailyCountDialog {
  static void show({
    required BuildContext context,
    required TargetType targetType,
    required int initialCount,
    int? totalPeriod,//这个数量是破戒劣迹几次为破戒的次数
    required Function(int count) onConfirm,
  }) {
    DialogCore.openDialog(
      builder: (context) => DailyCountWidget(
        targetType: targetType,
        initialCount: initialCount,
        totalPeriod: totalPeriod,
        onConfirm: onConfirm,
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5),
    );
  }
}

class DailyCountWidget extends StatefulWidget {
  final TargetType targetType;
  final int initialCount;
  final int? totalPeriod;
  final Function(int count) onConfirm;

  const DailyCountWidget({
    super.key,
    required this.targetType,
    required this.initialCount,
    required this.onConfirm,
    this.totalPeriod,
  });

  @override
  State<DailyCountWidget> createState() => _DailyCountWidgetState();
}

class _DailyCountWidgetState extends State<DailyCountWidget> {
  late int currentCount;
  late int? totalPeriod;
  late TextEditingController _textController;
  late FocusNode _focusNode;
  late bool isClock;

  @override
  void initState() {
    super.initState();
    isClock = widget.targetType == TargetType.clock;
    currentCount = widget.initialCount;
    totalPeriod = widget.totalPeriod;
    _textController = TextEditingController(text: currentCount.toString());
    _focusNode = FocusNode();
    // 监听文本变化
    _textController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _textController.removeListener(_onTextChanged);
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 文本变化监听
  void _onTextChanged() {
    final text = _textController.text;
    if (text.isEmpty) {
      setState(() {
        currentCount = 1;
      });
      return;
    }

    final value = int.tryParse(text);
    if (value != null) {
      setState(() {
        currentCount = value > 99999 ? 99999 : (value < 1 ? 1 : value);
      });

      // 如果输入的值超过99999，更新文本框显示为99999
      if (value > 99999) {
        _textController.text = '99999';
        _textController.selection = TextSelection.fromPosition(
          TextPosition(offset: _textController.text.length),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // 当键盘弹出时，调整底部边距将布局向上顶
      margin: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(10.r))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitleBar(),
          SizedBox(height: 30.h),
          _buildQuickDaysSection(),
          isClock ? SizedBox(height: 48.h) : SizedBox(height: 30.h),
          _buildCounterSection(),
          if (isClock) ...[
            SizedBox(height: 40.h),
          ] else ...[
            SizedBox(height: 15.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '每',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.gary85,
                  ),
                ),
                Text(
                  '$currentCount',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
                Text(
                  '天内累计$totalPeriod次，记为破戒',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.gary85,
                  ),
                ),
              ],
            ),
            SizedBox(height: 15.h),
          ],
          _buildConfirmButton(),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              isClock ? '目标设置' : '累计次数重置天数',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () => DialogCore.closeDialog(),
              child: Icon(
                Icons.close,
                size: 24.w,
                color: const Color(0xFF8A8A99),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快捷目标天数

  Widget _buildQuickDaysSection() {
    // 周一到周天
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildWeekButton(21),
        _buildWeekButton(90),
        _buildWeekButton(180),
        _buildWeekButton(365),
        _buildWeekButton(1000),
      ],
    );
  }

  /// 构建计数器区域
  Widget _buildCounterSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 减少按钮
        GestureDetector(
          onTap: _decreaseCount,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                color: currentCount > 1 ? ColorsUtil.primaryColor : Colors.grey,
                width: 2.w,
              ),
            ),
            child: Icon(
              Icons.remove,
              size: 24.w,
              color: currentCount > 1 ? ColorsUtil.primaryColor : Colors.grey,
            ),
          ),
        ),

        SizedBox(width: 20.w),

        // 数字输入框
        Container(
          width: 140.w,
          height: 50.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: TextField(
            controller: _textController,
            focusNode: _focusNode,
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly, // 只允许数字
              LengthLimitingTextInputFormatter(5), // 最多5位数字
            ],
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: ColorsUtil.textBlack,
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              isDense: true,
            ),
          ),
        ),

        SizedBox(width: 20.w),

        // 增加按钮
        GestureDetector(
          onTap: _increaseCount,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.add,
              size: 24.w,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建确定按钮
  Widget _buildConfirmButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: GestureDetector(
        onTap: _onConfirm,
        child: Container(
          width: double.infinity,
          height: 44.h,
          decoration: BoxDecoration(
            color: ColorsUtil.primaryColor,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建周期按钮
  Widget _buildWeekButton(int day) {
    return GestureDetector(
      onTap: () {
        widget.onConfirm(day);
        DialogCore.closeDialog();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        width: 52.w,
        decoration: BoxDecoration(
          color: ColorsUtil.garyF2,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Center(
          child: Text(
            "$day天",
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.textBlack,
            ),
          ),
        ),
      ),
    );
  }

  /// 减少次数
  void _decreaseCount() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    }
    if (currentCount > 1) {
      setState(() {
        currentCount--;
        _textController.text = currentCount.toString();
      });
    }
  }

  /// 增加次数
  void _increaseCount() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    }
    if (currentCount < 99999) {
      setState(() {
        currentCount++;
        _textController.text = currentCount.toString();
      });
    }
  }

  /// 确定按钮点击
  void _onConfirm() {
    widget.onConfirm(currentCount);
    DialogCore.closeDialog();
  }
}
