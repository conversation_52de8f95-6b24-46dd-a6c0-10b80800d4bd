import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 每日几次完成设置对话框
/// 参考 RecordLibDialog 的调用方式实现
class DailyCountDialog {
  static void show({
    required BuildContext context,
    required int initialCount,
    required Function(int count) onConfirm,
  }) {
    DialogCore.openDialog(
      builder: (context) => DailyCountWidget(
        initialCount: initialCount,
        onConfirm: onConfirm,
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5),
    );
  }
}

class DailyCountWidget extends StatefulWidget {
  final int initialCount;
  final Function(int count) onConfirm;

  const DailyCountWidget({
    super.key,
    required this.initialCount,
    required this.onConfirm,
  });

  @override
  State<DailyCountWidget> createState() => _DailyCountWidgetState();
}

class _DailyCountWidgetState extends State<DailyCountWidget> {
  late int currentCount;

  @override
  void initState() {
    super.initState();
    currentCount = widget.initialCount;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitleBar(),
          SizedBox(height: 30.h),
          _buildQuickDaysSection(),
          SizedBox(height: 48.h),
          _buildCounterSection(),
          SizedBox(height: 40.h),
          _buildConfirmButton(),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              '目标天数',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () => DialogCore.closeDialog(),
              child: Icon(
                Icons.close,
                size: 24.w,
                color: const Color(0xFF8A8A99),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快捷目标天数

  Widget _buildQuickDaysSection() {
    // 周一到周天
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildWeekButton(21),
        _buildWeekButton(90),
        _buildWeekButton(180),
        _buildWeekButton(365),
        _buildWeekButton(1000),
      ],
    );
  }

  /// 构建计数器区域
  Widget _buildCounterSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 减少按钮
        GestureDetector(
          onTap: _decreaseCount,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                color: currentCount > 1 ? ColorsUtil.primaryColor : Colors.grey,
                width: 2.w,
              ),
            ),
            child: Icon(
              Icons.remove,
              size: 24.w,
              color: currentCount > 1 ? ColorsUtil.primaryColor : Colors.grey,
            ),
          ),
        ),

        SizedBox(width: 20.w),

        // 数字显示
        Container(
          width: 140.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              '$currentCount',
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
          ),
        ),

        SizedBox(width: 20.w),

        // 增加按钮
        GestureDetector(
          onTap: _increaseCount,
          child: Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.add,
              size: 24.w,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建确定按钮
  Widget _buildConfirmButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: GestureDetector(
        onTap: _onConfirm,
        child: Container(
          width: double.infinity,
          height: 44.h,
          decoration: BoxDecoration(
            color: ColorsUtil.primaryColor,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建周期按钮
  Widget _buildWeekButton(int day) {
    return GestureDetector(
      onTap: () {
        widget.onConfirm(day);
        DialogCore.closeDialog();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        width: 52.w,
        decoration: BoxDecoration(
          color: ColorsUtil.garyF2,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Center(
          child: Text(
            "$day天",
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.textBlack,
            ),
          ),
        ),
      ),
    );
  }

  /// 减少次数
  void _decreaseCount() {
    if (currentCount > 1) {
      setState(() {
        currentCount--;
      });
    }
  }

  /// 增加次数
  void _increaseCount() {
    setState(() {
      currentCount++;
    });
  }

  /// 确定按钮点击
  void _onConfirm() {
    widget.onConfirm(currentCount);
    DialogCore.closeDialog();
  }
}
