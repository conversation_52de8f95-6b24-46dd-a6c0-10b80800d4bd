import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 每日几次完成设置对话框
/// 参考 RecordLibDialog 的调用方式实现
class DailyCountDialog {
  static void show({
    required BuildContext context,
    required int initialCount,
    required Function(int count) onConfirm,
  }) {
    DialogCore.openDialog(
      builder: (context) => DailyCountWidget(
        initialCount: initialCount,
        onConfirm: onConfirm,
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5),
    );
  }
}

class DailyCountWidget extends StatefulWidget {
  final int initialCount;
  final Function(int count) onConfirm;

  const DailyCountWidget({
    super.key,
    required this.initialCount,
    required this.onConfirm,
  });

  @override
  State<DailyCountWidget> createState() => _DailyCountWidgetState();
}

class _DailyCountWidgetState extends State<DailyCountWidget> {
  late int currentCount;

  @override
  void initState() {
    super.initState();
    currentCount = widget.initialCount;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitleBar(),
          _buildCounterSection(),
          _buildHintText(),
          _buildConfirmButton(),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              '每日几次完成',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () => DialogCore.closeDialog(),
              child: Icon(
                Icons.close,
                size: 24.w,
                color: const Color(0xFF8A8A99),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建计数器区域
  Widget _buildCounterSection() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 30.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 减少按钮
          GestureDetector(
            onTap: _decreaseCount,
            child: Container(
              width: 44.w,
              height: 44.h,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.remove,
                size: 24.w,
                color: currentCount > 1 ? ColorsUtil.textBlack : Colors.grey,
              ),
            ),
          ),
          
          SizedBox(width: 40.w),
          
          // 数字显示
          Container(
            width: 80.w,
            height: 50.h,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Center(
              child: Text(
                '$currentCount',
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                  color: ColorsUtil.textBlack,
                ),
              ),
            ),
          ),
          
          SizedBox(width: 40.w),
          
          // 增加按钮
          GestureDetector(
            onTap: _increaseCount,
            child: Container(
              width: 44.w,
              height: 44.h,
              decoration: BoxDecoration(
                color: ColorsUtil.primaryColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add,
                size: 24.w,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建提示文本
  Widget _buildHintText() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: '每日打卡',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey,
              ),
            ),
            TextSpan(
              text: '$currentCount',
              style: TextStyle(
                fontSize: 12.sp,
                color: ColorsUtil.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextSpan(
              text: '次，即完成当日目标',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// 构建确定按钮
  Widget _buildConfirmButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: GestureDetector(
        onTap: _onConfirm,
        child: Container(
          width: double.infinity,
          height: 44.h,
          decoration: BoxDecoration(
            color: ColorsUtil.primaryColor,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 减少次数
  void _decreaseCount() {
    if (currentCount > 1) {
      setState(() {
        currentCount--;
      });
    }
  }

  /// 增加次数
  void _increaseCount() {
    setState(() {
      currentCount++;
    });
  }

  /// 确定按钮点击
  void _onConfirm() {
    widget.onConfirm(currentCount);
    DialogCore.closeDialog();
  }
}
