import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_kexue/widget/dialog/plan_lib_dialog/targets_dialog_us.dart';
import 'package:flutter_kexue/widget/dialog/plan_lib_dialog/targets_dialog_viewmodel.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 目标库对话框
/// 支持打卡和持戒两个类型的切换
class GoalLibraryDialog {
  static void show({
    required BuildContext context,
    required TargetType initialType,
    required String planId,
    bool? showCreateBtn,
    required Function(TargetsUIState item) onConfirm,
  }) {
    DialogCore.openDialog(
      builder: (context) => GoalLibraryWidget(
        initialType: initialType,
        planId: planId,
        showCreateBtn: showCreateBtn ?? false,
        onConfirm: onConfirm,
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5),
    );
  }
}

class GoalLibraryWidget extends StatefulWidget {
  final TargetType initialType;
  final String planId;
  final bool showCreateBtn;
  final Function(TargetsUIState item) onConfirm;

  const GoalLibraryWidget({
    super.key,
    required this.initialType,
    required this.planId,
    required this.showCreateBtn,
    required this.onConfirm,
  });

  @override
  State<GoalLibraryWidget> createState() => _GoalLibraryWidgetState();
}

class _GoalLibraryWidgetState extends State<GoalLibraryWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TargetsDialogViewModel vm = TargetsDialogViewModel();

  @override
  void initState() {
    super.initState();
    vm.getTargets(widget.planId);
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialType == TargetType.clock ? 0 : 1,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxHeight: 600.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        children: [
          _buildTitleBar(),
          _buildTabBar(),
          SizedBox(height: 10.h),
          Expanded(child: _buildTabBarView()),
          Visibility(
            visible: widget.showCreateBtn,
            child: _buildBottomSection(),
          ),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              '目标库',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () => DialogCore.closeDialog(),
              child: Icon(
                Icons.close,
                size: 28.w,
                color: const Color(0xFF8A8A99),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Tab栏
  Widget _buildTabBar() {
    return Container(
      height: 44.h,
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: TabBar(
        controller: _tabController,
        indicatorColor: Colors.transparent,
        // 透明色,
        dividerHeight: 0,
        isScrollable: true,
        labelPadding: EdgeInsets.only(right: 16.w),
        tabAlignment: TabAlignment.start,
        labelColor: ColorsUtil.textBlack,
        unselectedLabelColor: Colors.grey,
        labelStyle: TextStyle(
          fontSize: 18.sp,
          color: ColorsUtil.textBlack,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 18.sp,
          color: ColorsUtil.gary85,
          fontWeight: FontWeight.normal,
        ),
        tabs: const [
          Tab(text: '打卡'),
          Tab(text: '持戒'),
        ],
      ),
    );
  }

  /// 构建TabBarView
  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildGoalList(vm.us.clocks),
        _buildGoalList(vm.us.controls),
      ],
    );
  }

  /// 构建目标列表
  Widget _buildGoalList(List<TargetsUIState> goals) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      shrinkWrap: true,
      itemCount: goals.length,
      itemBuilder: (context, index) {
        return _buildGoalItem(goals[index]);
      },
    );
  }

  /// 构建单个目标项
  Widget _buildGoalItem(TargetsUIState item) {
    return GestureDetector(
      onTap: () {
        widget.onConfirm(item);
        DialogCore.closeDialog();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: ColorsUtil.garyEF, width: 1),
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                // 左侧指示器
                Container(
                  width: 5.w,
                  height: 17.h,
                  decoration: BoxDecoration(
                    color: item.target_type == TargetType.clock.value
                        ? ColorsUtil.primaryColor
                        : Colors.red,
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),

                SizedBox(width: 8.w),
                // 标题
                Text(
                  item.target_name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: ColorsUtil.textBlack,
                  ),
                ),

                Spacer(),

                // 箭头图标
                Icon(
                  Icons.arrow_forward_ios,
                  size: 20.w,
                  color: ColorsUtil.garyE5,
                ),
              ],
            ),

            SizedBox(height: 3.h),

            // 副标题
            Padding(
              padding: const EdgeInsets.only(left: 13),
              child: Row(
                children: [
                  if (item.target_type == TargetType.clock.value) ...[
                    Text(
                      '共${item.target_day}天',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: ColorsUtil.primaryColor,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Text(
                        item.target_motivate ?? "",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: ColorsUtil.garyB1,
                        ),
                      ),
                    ),
                  ] else ...[
                    Text('每累计${item.target_day}次为破戒',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: ColorsUtil.gary85,
                        ))
                  ]
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建底部区域
  Widget _buildBottomSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧投稿文本
          Text(
            "投稿",
            style: TextStyle(
              fontSize: 16.sp,
              color: ColorsUtil.gary58,
            ),
          ),

          SizedBox(width: 16.w),

          // 右侧自定义目标按钮
          Expanded(
            child: _buildCustomGoalButton(),
          ),
        ],
      ),
    );
  }

  /// 构建自定义目标按钮
  Widget _buildCustomGoalButton() {
    return GestureDetector(
      onTap: () {},
      child: Container(
        width: double.infinity,
        height: 40.h,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(7.r),
        ),
        child: Center(
          child: Text(
            '自定义目标',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
