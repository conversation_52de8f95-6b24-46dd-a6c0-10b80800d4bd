import 'package:get/get.dart';

class TargetsUIState {
  final int id;
  final String target_name;
  final int target_type;
  ///完成度
  final int? target_status;
  ///目标天数
  final int? target_day;
  final String? target_motivate;

  TargetsUIState({
    required this.id,
    required this.target_name,
    required this.target_type,
    this.target_status,
    this.target_day,
    this.target_motivate,
  });
}

class TargetsDialogUs {
  final _targets = <TargetsUIState>[].obs;

  List<TargetsUIState> get targets => _targets.value;

  setTargets(List<TargetsUIState> list) {
    _targets.value = list;
    _targets.refresh();
  }
}
