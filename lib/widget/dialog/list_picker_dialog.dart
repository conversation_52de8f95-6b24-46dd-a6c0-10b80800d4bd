import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 列表选择项数据模型
class ListPickerItem {
  /// 显示文本
  final String text;
  
  /// 文本颜色
  final Color? textColor;
  
  /// 点击回调数据（可以是任意类型）
  final dynamic data;

  const ListPickerItem({
    required this.text,
    this.textColor,
    this.data,
  });
}

/// 通用列表选择对话框
class ListPickerDialog {
  /// 显示列表选择对话框
  /// [context] 上下文
  /// [items] 列表项数据
  /// [onItemSelected] 项目选择回调，返回选中项的索引和数据
  /// [showCancel] 是否显示取消按钮，默认true
  /// [cancelText] 取消按钮文本，默认"取消"
  /// [cancelTextColor] 取消按钮文本颜色
  static void show({
    required BuildContext context,
    required List<ListPickerItem> items,
    required Function(int index, ListPickerItem item) onItemSelected,
    bool showCancel = true,
    String cancelText = '取消',
    Color? cancelTextColor,
  }) {
    DialogCore.openDialog(
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5),
      builder: (context) => ListPickerBottomSheet(
        items: items,
        onItemSelected: onItemSelected,
        showCancel: showCancel,
        cancelText: cancelText,
        cancelTextColor: cancelTextColor,
      ),
    );
  }
}

/// 列表选择底部弹窗
class ListPickerBottomSheet extends StatelessWidget {
  final List<ListPickerItem> items;
  final Function(int index, ListPickerItem item) onItemSelected;
  final bool showCancel;
  final String cancelText;
  final Color? cancelTextColor;

  const ListPickerBottomSheet({
    super.key,
    required this.items,
    required this.onItemSelected,
    this.showCancel = true,
    this.cancelText = '取消',
    this.cancelTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 列表项
          ...List.generate(items.length, (index) {
            final item = items[index];
            return Column(
              children: [
                _buildActionButton(
                  context: context,
                  text: item.text,
                  textColor: item.textColor,
                  onTap: () => _handleItemTap(index, item),
                ),
                
                // 分割线（最后一项不显示）
                if (index < items.length - 1)
                  Container(
                    height: 1.h,
                    color: Colors.grey[200],
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
              ],
            );
          }),

          // 取消按钮区域
          if (showCancel) ...[
            // 分割线
            Container(
              height: 8.h,
              color: Colors.grey[100],
            ),

            // 取消按钮
            _buildActionButton(
              context: context,
              text: cancelText,
              textColor: cancelTextColor ?? Colors.grey[600],
              onTap: () => DialogCore.closeDialog(),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required BuildContext context,
    required String text,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: double.infinity,
        height: 56.h,
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
            color: textColor ?? Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 处理项目点击
  void _handleItemTap(int index, ListPickerItem item) {
    DialogCore.closeDialog();
    onItemSelected(index, item);
  }
}

/// 列表选择对话框扩展方法
extension ListPickerDialogExtension on BuildContext {
  /// 显示列表选择对话框的便捷方法
  void showListPicker({
    required List<ListPickerItem> items,
    required Function(int index, ListPickerItem item) onItemSelected,
    bool showCancel = true,
    String cancelText = '取消',
    Color? cancelTextColor,
  }) {
    ListPickerDialog.show(
      context: this,
      items: items,
      onItemSelected: onItemSelected,
      showCancel: showCancel,
      cancelText: cancelText,
      cancelTextColor: cancelTextColor,
    );
  }
}
