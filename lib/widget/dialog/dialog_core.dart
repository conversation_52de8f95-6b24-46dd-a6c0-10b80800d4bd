
import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_smart_dialog/src/kit/typedef.dart';

class DialogCore {
  //弹窗相关
  //弹窗栈
  static final List<String> _dialogStack = [];

  static Future<dynamic> openDialog(
      {required WidgetBuilder builder,
        Color? maskColor,
        VoidCallback? onDismiss,
        bool? clickMaskDismiss = true,
        VoidCallback? onMaskClick,
        SmartOnBack? onBack,
        SmartDialogController? controller,
        Alignment? alignment,
        bool? usePenetrate,
        bool? useAnimation,
        SmartAnimationType? animationType,
        List<SmartNonAnimationType>? nonAnimationTypes,
        Duration? animationTime,
        Widget? maskWidget,
        bool? debounce,
        Duration? displayTime,
        String? tag,
        @Deprecated("please use backType") bool? backDismiss,
        bool? keepSingle,
        bool? permanent,
        bool? useSystem,
        bool? bindPage,
        BuildContext? bindWidget,
        Rect? ignoreArea,
        SmartBackType? backType}) {
    _dialogStack.add("id");
    return SmartDialog.show(
        tag: tag,
        builder: builder,
        clickMaskDismiss: clickMaskDismiss,
        maskColor: maskColor,
        onMask: onMaskClick,
        onDismiss: () {
          if (_dialogStack.isNotEmpty) {
            _dialogStack.removeLast();
          }
          if (onDismiss != null) {
            onDismiss();
          }
        },
        onBack: onBack,
        bindWidget: bindWidget,
        alignment: alignment,
        permanent: permanent,
        bindPage: bindPage,
        useSystem: useSystem ?? false, // 关键：使用自定义弹窗系统
        usePenetrate: usePenetrate,
        useAnimation: useAnimation,
        animationType: animationType,
        nonAnimationTypes: nonAnimationTypes,
        animationTime: animationTime,
        maskWidget: maskWidget,
        debounce: debounce,
        displayTime: displayTime,
        keepSingle: keepSingle,
        ignoreArea: ignoreArea,
        backType: backType);
  }

  static void closeDialog() {
    SmartDialog.dismiss();
  }

  static bool isDialogShowing() {
    return _dialogStack.isNotEmpty;
  }
}