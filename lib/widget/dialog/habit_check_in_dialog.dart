import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/enums/target_type.dart';
import 'package:flutter_kexue/page/common_uistate/clock_uistate.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/action_sheet/td_action_sheet_item_widget.dart';
import 'package:get/get.dart';

/// 新增习惯弹窗
/// 按照 TDActionSheetDialog 样式实现
class HabitCheckInDialog {
  /// 显示新增习惯弹窗
  static Future<void> showAddHabitDialog(
    BuildContext context, {
    Function(ClockUIState)? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Align(
          alignment: Alignment.bottomCenter,
          child: Material(
            color: Colors.transparent,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 0),
              child: _AddHabitDialogContent(
                onConfirm: onConfirm,
                onCancel: onCancel,
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 1),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutCubic,
          )),
          child: child,
        );
      },
    );
  }
}

/// 新增习惯弹窗内容
class _AddHabitDialogContent extends StatefulWidget {
  final Function(ClockUIState)? onConfirm;
  final VoidCallback? onCancel;

  const _AddHabitDialogContent({
    this.onConfirm,
    this.onCancel,
  });

  @override
  State<_AddHabitDialogContent> createState() => _AddHabitDialogContentState();
}

class _AddHabitDialogContentState extends State<_AddHabitDialogContent> {
  // ==================== 数据区域 ====================

  /// 习惯类型：0-正能量，1-负能量(-1)，2-破戒(-3)
  int selectedHabitType = 0;

  /// 习惯名称
  final TextEditingController habitNameController = TextEditingController();

  /// 每日次数：true-每日单次，false-每日多次
  bool isDailySingle = true;

  /// 目标描述
  String targetDescription = '开发中...';

  // ==================== UI构建区域 ====================

  @override
  Widget build(BuildContext context) {
    // 获取键盘高度
    final bottomPadding = MediaQuery.of(context).viewInsets.bottom;

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      ),
      clipBehavior: Clip.antiAlias,
      padding: EdgeInsets.zero,
      child: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: bottomPadding), // 底部留出键盘空间
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            buildTitleView(context, centerTitleText: "新增习惯", onCancel: () {
              // buildTitleView 内部已经调用了 Navigator.maybePop(context)
              // 这里只需要执行取消回调，不要重复关闭弹窗
              widget.onCancel?.call();
            }),
            // 内容区域
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 习惯类型选择
                  _buildHabitTypeSection(),

                  const SizedBox(height: 16),

                  // 习惯名称输入
                  _buildHabitNameSection(),

                  const SizedBox(height: 16),

                  // 每日次数选择
                  _buildDailyFrequencySection(),

                  const SizedBox(height: 16),

                  // 目标设置
                  _buildTargetSection(),

                  const SizedBox(height: 20),

                  // 确定按钮
                  _buildConfirmButton(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建习惯类型选择区域
  Widget _buildHabitTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '习惯类型',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: ColorsUtil.textBlack,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildHabitTypeButton('正能量', 0, ColorsUtil.primaryColor),
            const SizedBox(width: 12),
            _buildHabitTypeButton('负能量(-1)', 1, Colors.orange),
            const SizedBox(width: 12),
            _buildHabitTypeButton('破戒(-3)', 2, Colors.red),
          ],
        ),
      ],
    );
  }

  /// 构建习惯类型按钮
  Widget _buildHabitTypeButton(String text, int type, Color color) {
    final isSelected = selectedHabitType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedHabitType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? color : Color(0xFFF2F2F2),
          border: Border.all(
            color: isSelected ? color : Color(0xFFF2F2F2),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: isSelected ? Colors.white : ColorsUtil.textBlack,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 构建习惯名称输入区域
  Widget _buildHabitNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '习惯名称',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: ColorsUtil.textBlack,
          ),
        ),
        const SizedBox(height: 12),
        Container(
            height: 44,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: double.infinity,
                    child: TextField(
                      controller: habitNameController,
                      decoration: const InputDecoration(
                        hintText: '看黄',
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF999999),
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        color: ColorsUtil.textBlack,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                GestureDetector(
                  onTap: () {
                    // TODO: 打开习惯库选择
                  },
                  child: Text(
                    '习惯库',
                    style: TextStyle(
                      fontSize: 14,
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            )),
      ],
    );
  }

  /// 构建每日次数选择区域
  Widget _buildDailyFrequencySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '每日次数',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: ColorsUtil.textBlack,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildFrequencyButton('每日单次', true),
            const SizedBox(width: 12),
            _buildFrequencyButton('多次计数', false),
          ],
        ),
      ],
    );
  }

  /// 构建次数选择按钮
  Widget _buildFrequencyButton(String text, bool isSingle) {
    final isSelected = isDailySingle == isSingle;

    return GestureDetector(
      onTap: () {
        setState(() {
          isDailySingle = isSingle;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? ColorsUtil.primaryColor : Color(0xFFF2F2F2),
          border: Border.all(
            color: isSelected ? ColorsUtil.primaryColor : Color(0xFFF2F2F2),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: isSelected ? Colors.white : ColorsUtil.textBlack,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 构建目标设置区域
  Widget _buildTargetSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '目标（开发中...）',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: ColorsUtil.textBlack,
          ),
        ),
      ],
    );
  }

  /// 构建确定按钮
  Widget _buildConfirmButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: _onConfirmPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorsUtil.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: const Text(
          '确定',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  // ==================== 事件处理区域 ====================

  /// 处理确定按钮点击
  void _onConfirmPressed() {
    final habitName = habitNameController.text.trim();

    if (habitName.isEmpty) {
      Get.snackbar('提示', '请输入习惯名称');
      return;
    }

    // 根据选择的类型创建对应的 DailyClockUIState
    final habitUIState = _createHabitUIState(habitName);

    // 关闭弹窗
    Get.back();
    // Navigator.maybePop(context);
    // 回调结果
    widget.onConfirm?.call(habitUIState);
  }

  /// 创建习惯UI状态对象
  ClockUIState _createHabitUIState(String habitName) {
    // 根据习惯类型设置不同的图标和属性
    String icon;
    double typeValue = 1.0;
    bool isPositive = true;

    switch (selectedHabitType) {
      case 0: // 正能量
        icon = '👍';
        isPositive = true;
        typeValue = 1.0;
        break;
      case 1: // 负能量(-1)
        icon = '👎';
        isPositive = false;
        typeValue = -1.0;
        break;
      default: // 破戒(-3)
        icon = '💔';
        isPositive = false;
        typeValue = -3.0;
        break;
    }

    return ClockUIState(
      target: "锻炼",
      goalType: TargetType.persist,
      goalDay: 10,
      totalCount: 30,
      dailyCount: 3,
      motivation: "今天成都下大暴雨，我的心情很差，今天成都下大暴雨...",
      progress: 50,
    );
  }

  @override
  void dispose() {
    habitNameController.dispose();
    super.dispose();
  }
}
