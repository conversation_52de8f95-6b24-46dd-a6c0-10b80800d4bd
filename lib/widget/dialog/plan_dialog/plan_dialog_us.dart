import 'package:get/get.dart';

class PlanUIState {
  final int id;
  final String plan_name;
  final int completion;
  final int status;
  final String? brief_introduction;

  PlanUIState({
    required this.id,
    required this.plan_name,
    required this.completion,
    required this.status,
    this.brief_introduction,
  });
}

class PlansDialogUs {
  final _plans = <PlanUIState>[].obs;

  List<PlanUIState> get plans => _plans.value;

  setPlans(List<PlanUIState> value) {
    _plans.value = value;
    _plans.refresh();
  }

}
