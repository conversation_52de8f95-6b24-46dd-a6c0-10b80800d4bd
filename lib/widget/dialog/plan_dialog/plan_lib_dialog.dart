import 'package:flutter/material.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/vm/plan_uistate.dart';
import 'package:flutter_kexue/page/page/record_add_or_edit/entity/record_add_or_edit_props.dart';
import 'package:flutter_kexue/routes/routes.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_kexue/widget/dialog/plan_dialog/plan_dialog_viewmodel.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 体系库
class PlanLibDialog {
  static void show({
    required BuildContext context,
    required Function() onConfirm,
    bool? isShowBottomView = true,
  }) {
    DialogCore.openDialog(
        builder: (context) => PlanLibWidget(
              onConfirm: onConfirm,
              isShowBottomView: isShowBottomView,
            ),
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }
}

class PlanLibWidget extends StatefulWidget {
  final Function() onConfirm;
  final bool? isShowBottomView;

  const PlanLibWidget({
    super.key,
    required this.onConfirm,
    this.isShowBottomView = true,
  });

  @override
  State<PlanLibWidget> createState() => _PlanLibWidgetState();
}

class _PlanLibWidgetState extends State<PlanLibWidget> {
  final PlansDialogViewModel vm = PlansDialogViewModel();

  @override
  void initState() {
    super.initState();
    vm.getPlanList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxHeight: 654.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        children: [
          _buildTitleBar(),
          SizedBox(height: 10.h),
          Expanded(child: _buildContent()),
          Visibility(
              visible: widget.isShowBottomView == true,
              child: _buildBottomSection())
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              '体系',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () => DialogCore.closeDialog(),
              child: Icon(
                Icons.close,
                size: 28.w,
                color: const Color(0xFF8A8A99),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ListView.builder(
          itemCount: vm.us.plans.length,
          padding: EdgeInsets.zero,
          itemBuilder: (BuildContext context, int index) {
            return _buildItemView(vm.us.plans[index], index);
          }),
    );
  }

  /// 构建单个习惯项
  Widget _buildItemView(PlanUIState state, int index) {
    return GestureDetector(
      onTap: () => {},
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.only(top: 16.h, bottom: 8.h),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFE0E0E0),
              width: 0.5,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Text(
                  state.plan_name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 16,
                    color: ColorsUtil.textBlack,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                Image.asset(
                  'assets/images/common/ic_arrow_right.png',
                  width: 20.w,
                  height: 20.h,
                ),
              ],
            ),
            Text(
              "${state.brief_introduction}",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 12,
                color: ColorsUtil.garyB1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///底部区域
  Widget _buildBottomSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧单选按钮区域
          Text(
            "投稿",
            style: TextStyle(
              fontSize: 16.sp,
              color: ColorsUtil.gary58,
            ),
          ),
          const SizedBox(width: 16),
          // 右侧确定按钮
          Expanded(
            child: _buildConfirmButton(),
          ),
        ],
      ),
    );
  }

  /// 构建自定义记录按钮
  Widget _buildConfirmButton() {
    return GestureDetector(
      onTap: () {
        var params = RecordAddOrEditProps(
          recordType: 0,
          recordName: '自定义记录',
          isAdd: false,
        );
        // 跳转到新增记录页面
        Get.toNamed(Routes.recordAddOrEdit, arguments: params);
        DialogCore.closeDialog();
      },
      child: Container(
        width: double.infinity,
        height: 40.h,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(7.r),
        ),
        child: Center(
          child: Text(
            '自定义记录',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
