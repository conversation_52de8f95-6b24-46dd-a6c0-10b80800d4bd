import 'package:flutter/material.dart';
import 'package:flutter_kexue/data/plan/ds/model/param/plan_edit_params_model.dart';
import 'package:flutter_kexue/data/plan/repo/plan_repo.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/vm/plan_uistate.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/utils/ui_util/toast_util.dart';
import 'package:flutter_kexue/widget/dialog/dialog_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 编辑行动系统对话框
class PlanEditDialog {
  static void show({
    PlanUIState? state,
    required Function(String planName) onConfirm,
  }) {
    DialogCore.openDialog(
      builder: (context) => PlanEditWidget(
        state: state,
        onConfirm: onConfirm,
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5),
    );
  }
}

class PlanEditWidget extends StatefulWidget {
  final PlanUIState? state;
  final Function(String planName) onConfirm;

  const PlanEditWidget({
    super.key,
    this.state,
    required this.onConfirm,
  });

  @override
  State<PlanEditWidget> createState() => _PlanEditWidgetState();
}

class _PlanEditWidgetState extends State<PlanEditWidget> {
  final _planRepo = PlanRepo();
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController =
        TextEditingController(text: widget.state?.plan_name ?? '');

    // 延迟获取焦点，确保对话框完全显示后再弹出键盘
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 如果有初始文本，选中所有文本
      if (widget.state?.plan_name?.isNotEmpty == true) {
        _textController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _textController.text.length,
        );
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 150),
      // 当键盘弹出时，调整底部边距
      margin: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(10.r))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          SizedBox(height: 18.h),
          _buildContent(),
          _buildButton(),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 20.h,
        bottom: 16.h,
      ),
      child: Row(
        children: [
          // 删除图标
          Image.asset(
            'assets/images/plan/plan_delete.png',
            width: 23.w,
            height: 23.h,
          ),

          SizedBox(width: 12.w),

          // 标题
          Expanded(
            child: Text(
              '编辑行动系统',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.textBlack,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // 关闭按钮
          GestureDetector(
            onTap: () => DialogCore.closeDialog(),
            child: Image.asset(
              'assets/images/common/ic_delete.png',
              width: 22.w,
              height: 28.h,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 输入标签
          Row(
            children: [
              Text(
                '行动系统名称',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.textBlack,
                  fontWeight: FontWeight.w500,
                ),
              ),

              Spacer(),

              // 行动系统库按钮
              GestureDetector(
                onTap: _onLibraryTap,
                child: Text(
                  '行动系统库',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // 输入框
          Container(
            decoration: BoxDecoration(
              color: ColorsUtil.garyF5,
              borderRadius: BorderRadius.circular(7.r),
            ),
            child: TextField(
              controller: _textController,
              style: TextStyle(
                fontSize: 16.sp,
                color: ColorsUtil.textBlack,
              ),
              decoration: InputDecoration(
                hintText: '输入行动系统名称',
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: ColorsUtil.gary99,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 9.h,
                ),
                isDense: true,
              ),
              maxLength: 50,
              buildCounter: (context,
                  {required currentLength, required isFocused, maxLength}) {
                return null; // 隐藏字符计数器
              },
            ),
          ),

          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  /// 构建确定按钮
  Widget _buildButton() {
    return Container(
      padding: EdgeInsets.only(
        left: 20.w,
        right: 20.w,
        bottom: 20.h,
      ),
      child: SizedBox(
        width: double.infinity,
        height: 44.h,
        child: ElevatedButton(
          onPressed: _onConfirm,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorsUtil.primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            '确定',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理行动系统库点击
  void _onLibraryTap() {

  }

  /// 处理确定按钮点击
  Future<void> _onConfirm() async {
    final planName = _textController.text.trim();
    if (planName.isEmpty) {
      return;
    }
    var request = PlanEditParams(plan_id: "${widget.state?.id}", plan_name: planName);
    var response = await _planRepo.updatePlan(request);
    if (response.isSuccess) {
      ToastUtil.showToast('修改成功');
      widget.onConfirm(planName);
      DialogCore.closeDialog();
    } else {
      ToastUtil.showToast(response.message);
    }

  }
}
