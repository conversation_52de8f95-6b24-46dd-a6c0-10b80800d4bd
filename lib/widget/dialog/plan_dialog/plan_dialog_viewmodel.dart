import 'package:flutter_kexue/data/plan/ds/model/net/plans_net_model.dart';
import 'package:flutter_kexue/data/plan/repo/plan_repo.dart';
import 'package:flutter_kexue/page/page/plan/plan_add_goal/vm/plan_uistate.dart';
import 'package:flutter_kexue/widget/dialog/plan_dialog/plan_dialog_us.dart';

class PlansDialogViewModel {
  final _repo = PlanRepo();

  PlansDialogUs us = PlansDialogUs();

  getPlanList() async {
    var response = await _repo.getPlanList();
    if (response.isSuccess) {
      _coverPlansList(response.data);
    }
  }

  void _coverPlansList(List<PlanNetModel>? data) {
    var list = data
            ?.map((item) => PlanUIState(
                  id: item.id,
                  plan_name: item.plan_name,
                  completion: item.completion,
                  status: item.status,
                  brief_introduction: item.brief_introduction,
                ))
            .toList() ??
        [];
    us.setPlans(list);
  }
}
