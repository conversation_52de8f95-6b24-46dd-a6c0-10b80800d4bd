/// 日历日期项数据模型
class CalendarDayData {
  final DateTime date;
  final bool isCurrentMonth;
  final bool isToday;
  final bool isPlaceholder; // 是否为占位符（不显示内容）
  final bool isSelected; // 是否被选中
  //是否拍照
  final bool isPhoto;

  //是否写了日记
  final bool isDiary;

  //是否达到了目标
  final bool isPositive;

  //是否破戒
  final bool isNegative;

  //记录列表
  final List<String>? listRecord;

  //正能量打卡列表
  final List<String>? listPositive;

  //持戒打卡列表
  final List<String>? listNegative;

  CalendarDayData({
    required this.date,
    required this.isCurrentMonth,
    required this.isToday,
    this.isPlaceholder = false,
    this.isSelected = false,
    this.isPositive = false,
    this.isNegative = false,
    this.isPhoto = false,
    this.isDiary = false,
    this.listRecord,
    this.listPositive,
    this.listNegative,
  });

  // 任意一个列表不是空的
  bool get hasListData =>
      (listRecord != null && listRecord!.isNotEmpty) ||
      (listPositive != null && listPositive!.isNotEmpty) ||
      (listNegative != null && listNegative!.isNotEmpty);

  String get getRecordText => listRecord?.join('|') ?? '';
  String get getPositiveText => listPositive?.join('|') ?? '';
  String get getNegativeText => listNegative?.join('|') ?? '';

}