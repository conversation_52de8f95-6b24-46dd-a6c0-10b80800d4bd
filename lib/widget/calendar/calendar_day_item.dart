import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/lunar_util.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_model.dart';

/// 日历日期项组件
class CalendarDayItem extends StatelessWidget {
  final CalendarDayData dayData;
  final VoidCallback? onTap;
  final Widget Function(CalendarDayData dayData)? customBuilder;

  const CalendarDayItem({
    super.key,
    required this.dayData,
    this.onTap,
    this.customBuilder,
  });

  @override
  Widget build(BuildContext context) {
    // 如果提供了自定义构建器，使用自定义构建器
    if (customBuilder != null) {
      return GestureDetector(
        onTap: onTap,
        child: customBuilder?.call(dayData),
      );
    }

    // 如果是占位符，返回空容器
    if (dayData.isPlaceholder) {
      return Container(); // 保留占位但不显示内容
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          // 选中状态的背景色
          color: dayData.isSelected
              ? const Color(0xFF4ACE7F).withOpacity(0.2)
              : null,
          // 如果有数据，显示边框
          border: null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 公历日期
            Text(
              '${dayData.date.day}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: _getDateTextColor(),
              ),
            ),
            const SizedBox(height: 2),
            // 农历日期或"今天"
            Text(
              LunarUtil.getDisplayText(dayData.date, isToday: dayData.isToday),
              style: TextStyle(
                fontSize: 10,
                color: _getLunarTextColor(),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 获取公历日期文字颜色
  Color _getDateTextColor() {
    if (!dayData.isCurrentMonth) {
      return Colors.grey.withOpacity(0.4);
    }

    if (dayData.isToday) {
      return const Color(0xFF4ACE7F);
    }

    return Colors.black87;
  }

  /// 获取农历文字颜色
  Color _getLunarTextColor() {
    if (!dayData.isCurrentMonth) {
      return Colors.grey.withOpacity(0.3);
    }

    if (dayData.isToday) {
      return const Color(0xFF4ACE7F);
    }

    return Colors.grey;
  }
}
