import 'package:flutter/material.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_item.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_model.dart';

/// 日历体组件
class TdCalendarBody extends StatefulWidget {
  /// 选中的日期（同时控制显示月份和选中状态）
  final DateTime? selectedDate;

  /// 月份切换回调（返回该月第一天的DateTime）
  final Function(DateTime monthFirstDay)? onMonthChanged;

  /// 日期点击回调
  final Function(DateTime date)? onDateTap;

  /// 有数据的日期列表
  final List<CalendarDayData>? datesWithData;

  /// 自定义日期项构建器
  final Widget Function(CalendarDayData dayData)? customDayBuilder;

  final EdgeInsets padding;

  const TdCalendarBody({
    super.key,
    this.selectedDate,
    this.onMonthChanged,
    this.onDateTap,
    this.datesWithData,
    this.customDayBuilder,
    this.padding = const EdgeInsets.all(0),
  });

  @override
  State<TdCalendarBody> createState() => _TdCalendarBodyState();
}

class _TdCalendarBodyState extends State<TdCalendarBody>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late DateTime _currentDate;
  late AnimationController _heightAnimationController;
  late Animation<double> _heightAnimation;

  // 用于计算页面索引的基准日期（2000年1月）
  static final DateTime _baseDate = DateTime(2000, 1, 1);

  // 每行的高度
  double _rowHeight = 55.0;

  // GridView的行间距（与_buildMonthView中的mainAxisSpacing保持一致）
  static const double _mainAxisSpacing = 8.0;

  // 页面切换动画标志，防止重复触发
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    // 根据选中日期确定显示的月份，如果没有选中日期则显示当前月份
    _currentDate = widget.selectedDate ?? DateTime.now();

    // 初始化高度动画控制器
    _heightAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    // 初始化动画（高度将在build时计算）
    _heightAnimation = Tween<double>(
      begin: 200.0, // 临时初始值
      end: 200.0,
    ).animate(CurvedAnimation(
      parent: _heightAnimationController,
      curve: Curves.easeInOut,
    ));

    // 计算当前月份相对于基准日期的索引
    final initialIndex = _getMonthIndex(_currentDate);
    _pageController = PageController(initialPage: initialIndex);
  }

  @override
  void didUpdateWidget(TdCalendarBody oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedDate != oldWidget.selectedDate && !_isAnimating) {
      // 当选中日期改变时，更新显示的月份
      final newDate = widget.selectedDate != null
          ? DateTime(widget.selectedDate!.year, widget.selectedDate!.month, 1)
          : DateTime.now();

      _animateToMonth(newDate);
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 动态计算每行高度
        final availableWidth =
            constraints.maxWidth - widget.padding.left - widget.padding.right;
        final newRowHeight = availableWidth / 7; // 每行高度 = 可用宽度 / 7列
        // 如果行高发生变化，更新并重新计算动画
        if ((newRowHeight - _rowHeight).abs() > 0.1) {
          _rowHeight = newRowHeight;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateHeightAnimation();
          });
        }

        return AnimatedBuilder(
          animation: _heightAnimation,
          builder: (context, child) {
            return Container(
              height: _heightAnimation.value,
              padding: widget.padding,
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemBuilder: (context, index) {
                  final monthDate = _getDateFromIndex(index);
                  return _buildMonthView(monthDate);
                },
              ),
            );
          },
        );
      },
    );
  }

  /// 构建月份视图
  Widget _buildMonthView(DateTime monthDate) {
    final days = _generateMonthDays(monthDate);

    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        mainAxisSpacing: _mainAxisSpacing,
      ),
      itemCount: days.length,
      itemBuilder: (context, index) {
        final dayData = days[index];
        return CalendarDayItem(
          dayData: dayData,
          onTap: () => widget.onDateTap?.call(dayData.date),
          customBuilder: widget.customDayBuilder,
        );
      },
    );
  }

  /// 生成月份的所有日期数据
  List<CalendarDayData> _generateMonthDays(DateTime monthDate) {
    final List<CalendarDayData> days = [];

    // 获取月份第一天
    final firstDay = DateTime(monthDate.year, monthDate.month, 1);

    // 获取月份最后一天
    final lastDay = DateTime(monthDate.year, monthDate.month + 1, 0);

    // 获取第一天是星期几（0=周日，1=周一...6=周六）
    final firstWeekday = firstDay.weekday % 7;

    // 添加上个月的日期占位（不显示内容）
    for (int i = firstWeekday - 1; i >= 0; i--) {
      final date = firstDay.subtract(Duration(days: i + 1));
      days.add(
          _createDayData(date, isCurrentMonth: false, isPlaceholder: true));
    }

    // 添加当前月份的所有日期
    for (int day = 1; day <= lastDay.day; day++) {
      final date = DateTime(monthDate.year, monthDate.month, day);
      days.add(
          _createDayData(date, isCurrentMonth: true, isPlaceholder: false));
    }

    // 添加下个月的日期占位（不显示内容）
    final totalRows = _calculateMonthRows(monthDate);
    final totalCells = totalRows * 7;
    final remainingDays = totalCells - days.length;

    for (int day = 1; day <= remainingDays; day++) {
      final date = DateTime(monthDate.year, monthDate.month + 1, day);
      days.add(
          _createDayData(date, isCurrentMonth: false, isPlaceholder: true));
    }

    return days;
  }

  /// 创建日期数据
  CalendarDayData _createDayData(DateTime date,
      {required bool isCurrentMonth, required bool isPlaceholder}) {
    final today = DateTime.now();
    final isToday = date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;

    // 检查是否被选中
    final isSelected = widget.selectedDate != null &&
        widget.selectedDate?.year == date.year &&
        widget.selectedDate?.month == date.month &&
        widget.selectedDate?.day == date.day;

    // 查找后台数据中是否有该日期的数据
    CalendarDayData? backendData;
    if (widget.datesWithData != null) {
      try {
        backendData = widget.datesWithData?.firstWhere(
          (data) => data.date.year == date.year &&
                   data.date.month == date.month &&
                   data.date.day == date.day,
        );
      } catch (e) {
        // 没有找到对应日期的数据，使用默认值
        backendData = null;
      }
    }

    // 合并后台数据和基础数据
    return CalendarDayData(
      date: date,
      isCurrentMonth: isCurrentMonth,
      isToday: isToday,
      isPlaceholder: isPlaceholder,
      isSelected: isSelected,
      // 从后台数据中获取业务数据，如果没有则使用默认值
      isPhoto: backendData?.isPhoto ?? false,
      isDiary: backendData?.isDiary ?? false,
      isPositive: backendData?.isPositive ?? false,
      isNegative: backendData?.isNegative ?? false,
      listRecord: backendData?.listRecord,
      listPositive: backendData?.listPositive,
      listNegative: backendData?.listNegative,
    );
  }

  /// 页面切换回调
  void _onPageChanged(int index) {
    if (_isAnimating) return; // 如果正在动画中，忽略回调

    final newDate = _getDateFromIndex(index);
    if (newDate.year != _currentDate.year ||
        newDate.month != _currentDate.month) {
      _currentDate = newDate;

      // 计算新月份的高度并执行动画
      _animateToNewHeight(newDate);

      widget.onMonthChanged?.call(newDate);
    }
  }

  /// 计算月份需要的行数
  int _calculateMonthRows(DateTime monthDate) {
    // 获取月份第一天
    final firstDay = DateTime(monthDate.year, monthDate.month, 1);

    // 获取月份最后一天
    final lastDay = DateTime(monthDate.year, monthDate.month + 1, 0);

    // 获取第一天是星期几（0=周日，1=周一...6=周六）
    final firstWeekday = firstDay.weekday % 7;

    // 计算总天数（包括上个月填充的天数）
    final totalDays = firstWeekday + lastDay.day;

    // 计算需要的行数
    return (totalDays / 7).ceil();
  }

  /// 计算月份的高度
  double _calculateMonthHeight(DateTime monthDate) {
    final rows = _calculateMonthRows(monthDate);
    // 计算总高度 = 行数 * 行高 + (行数-1) * 行间距
    final double totalSpacing = rows > 1 ? (rows - 1) * _mainAxisSpacing : 0;
    return rows * _rowHeight + totalSpacing;
  }

  /// 执行高度变化动画
  void _animateToNewHeight(DateTime newDate) {
    final newHeight = _calculateMonthHeight(newDate);
    final currentHeight = _heightAnimation.value;

    if ((newHeight - currentHeight).abs() > 0.1) {
      _heightAnimation = Tween<double>(
        begin: currentHeight,
        end: newHeight,
      ).animate(CurvedAnimation(
        parent: _heightAnimationController,
        curve: Curves.easeInOut,
      ));

      _heightAnimationController.reset();
      _heightAnimationController.forward();
    }
  }

  /// 更新高度动画（当行高发生变化时）
  void _updateHeightAnimation() {
    final newHeight = _calculateMonthHeight(_currentDate);
    final currentHeight = _heightAnimation.value;

    if ((newHeight - currentHeight).abs() > 0.1) {
      _heightAnimation = Tween<double>(
        begin: currentHeight,
        end: newHeight,
      ).animate(CurvedAnimation(
        parent: _heightAnimationController,
        curve: Curves.easeInOut,
      ));

      _heightAnimationController.reset();
      _heightAnimationController.forward();
    }
  }

  /// 动画切换到指定月份
  Future<void> _animateToMonth(DateTime targetDate) async {
    if (_isAnimating) return;

    _isAnimating = true;

    try {
      final newIndex = _getMonthIndex(targetDate);
      final currentIndex = _pageController.page?.round() ?? 0;

      // 如果目标页面和当前页面相同，直接返回
      if (newIndex == currentIndex) {
        _isAnimating = false;
        return;
      }

      // 先更新当前日期和高度动画
      _currentDate = targetDate;
      _animateToNewHeight(targetDate);

      // 执行页面切换动画
      await _pageController.animateToPage(
        newIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      // 通知月份变化
      widget.onMonthChanged?.call(targetDate);

    } finally {
      _isAnimating = false;
    }
  }

  /// 根据索引获取月份索引
  int _getMonthIndex(DateTime date) {
    final yearDiff = date.year - _baseDate.year;
    final monthDiff = date.month - _baseDate.month;
    return yearDiff * 12 + monthDiff;
  }

  /// 根据索引获取日期
  DateTime _getDateFromIndex(int index) {
    // 计算从基准日期开始的总月数
    final totalMonths = index;

    // 计算年份和月份
    final year = _baseDate.year + (totalMonths ~/ 12);
    final month = _baseDate.month + (totalMonths % 12);

    // 处理月份边界情况
    if (month > 12) {
      return DateTime(year + 1, month - 12, 1);
    } else if (month < 1) {
      return DateTime(year - 1, month + 12, 1);
    }

    return DateTime(year, month, 1);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _heightAnimationController.dispose();
    super.dispose();
  }
}
