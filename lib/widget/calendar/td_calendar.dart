import 'package:flutter/material.dart';
import 'package:flutter_kexue/utils/ui_util/colors_util.dart';
import 'package:flutter_kexue/widget/calendar/calendar_day_model.dart';

import 'td_calendar_body.dart';
import 'td_calendar_header.dart';

class TDCalendar extends StatefulWidget {
  /// 当前选中的日期（同时控制显示月份和选中状态）
  final DateTime selectedDate;

  /// 月份切换回调（返回该月第一天的DateTime）
  final Function(DateTime monthFirstDay)? onMonthChanged;

  /// 日期点击回调
  final Function(DateTime date)? onDateTap;

  /// 自定义日期项构建器
  final Widget Function(CalendarDayData dayData)? customDayBuilder;

  /// 日历内边距
  final EdgeInsets padding;

  /// 有数据的日期列表
  final List<CalendarDayData>? datesWithData;

  const TDCalendar({
    super.key,
    required this.selectedDate,
    this.onMonthChanged,
    this.onDateTap,
    this.customDayBuilder,
    this.padding = const EdgeInsets.symmetric(horizontal: 14),
    this.datesWithData,
  });

  @override
  State<TDCalendar> createState() => _TDCalendarState();
}

class _TDCalendarState extends State<TDCalendar> {
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate ?? DateTime.now();
  }

  @override
  void didUpdateWidget(TDCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedDate != oldWidget.selectedDate) {
      _selectedDate = widget.selectedDate;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          TdCalendarHeader(
            weekdayHeight: 30,
            padding: widget.padding,
            weekdayStyle: TextStyle(
                fontSize: 14,
                color: ColorsUtil.textBlack,
                fontWeight: FontWeight.w500),
          ),
          TdCalendarBody(
            selectedDate: _selectedDate,
            padding: widget.padding,
            onMonthChanged: (monthFirstDay) {
              setState(() {
                _selectedDate = monthFirstDay;
              });
              widget.onMonthChanged?.call(monthFirstDay);
            },
            onDateTap: (date) {
              setState(() {
                _selectedDate = date;
              });
              widget.onDateTap?.call(date);
            },
            datesWithData: widget.datesWithData,
            customDayBuilder: widget.customDayBuilder,
          ),
        ],
      ),
    );
  }
}
