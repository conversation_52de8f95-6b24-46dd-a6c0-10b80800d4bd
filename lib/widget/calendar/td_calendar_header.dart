import 'package:flutter/material.dart';

class TdCalendarHeader extends StatelessWidget {
  const TdCalendarHeader({
    super.key,
    this.weekdayHeight = 30,
    this.padding = const EdgeInsets.all(0),
    this.bgColor = const Color(0xFFF5F6FA),
    this.weekdayStyle = const TextStyle(fontSize: 14, color: Colors.grey),
    this.weekdayNames = const ['日', '一', '二', '三', '四', '五', '六'],
  });

  final EdgeInsets padding;
  final Color bgColor;
  final double weekdayHeight;
  final TextStyle weekdayStyle;
  final List<String> weekdayNames;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      color: bgColor,
      child: Row(
        children: List.generate(weekdayNames.length, (index) {
          return [
            Expanded(
              child: SizedBox(
                height: weekdayHeight,
                child: Center(
                  child: Text(
                    weekdayNames[index],
                    style: weekdayStyle,
                  ),
                ),
              ),
            ),
          ];
        }).expand((element) => element).toList(),
      ),
    );
  }
}
